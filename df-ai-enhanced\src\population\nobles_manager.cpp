#include "nobles_manager.h"
#include "../debug/logging.h"
#include "../config/config_manager.h"

// Legacy includes for migration - will be removed gradually
#include "../../ai.h"
#include "../../population.h"
#include "../../plan.h"

namespace dfai {
namespace population {

NoblesManager::NoblesManager()
    : logger_(debug::Logger::GetInstance())
    , initialized_(false)
    , nobles_()
    , nobles_needing_rooms_()
{
    logger_.Info("NoblesManager created");
}

NoblesManager::~NoblesManager() {
    Shutdown();
    logger_.Info("NoblesManager destroyed");
}

bool NoblesManager::Initialize(color_ostream& out) {
    if (initialized_) {
        logger_.Warning("NoblesManager already initialized");
        return true;
    }
    
    logger_.Info("Initializing NoblesManager...");
    
    try {
        // Initialize noble tracking
        UpdateNoblesList(out);
        
        initialized_ = true;
        logger_.Info("NoblesManager initialization complete");
        return true;
        
    } catch (const std::exception& e) {
        logger_.Error("Exception during NoblesManager initialization: {}", e.what());
        return false;
    }
}

void NoblesManager::Shutdown() {
    if (!initialized_) {
        return;
    }
    
    logger_.Info("Shutting down NoblesManager...");
    
    nobles_.clear();
    nobles_needing_rooms_.clear();
    
    initialized_ = false;
    logger_.Info("NoblesManager shutdown complete");
}

void NoblesManager::Update(color_ostream& out) {
    if (!initialized_) {
        logger_.Warning("NoblesManager::Update called before initialization");
        return;
    }
    
    try {
        // Update nobles list
        UpdateNoblesList(out);
        
        // Check noble apartments
        CheckNobleApartments(out);
        
        // Assign rooms for nobles who need them
        AssignNobleRooms(out);
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in NoblesManager::Update: {}", e.what());
    }
}

// Legacy utility functions - migrated from old code
namespace {
    bool unit_hasmilitaryduty(df::unit *u) {
        // Check if unit has military duties
        if (u->military.squad_id == -1) {
            return false;
        }
        
        // Check if actively assigned to military
        if (u->military.squad_position != -1) {
            return true;
        }
        
        // Check for active military training or duties
        return !u->military.squad_orders.empty();
    }
    
    int32_t unit_totalxp(const df::unit *u) {
        // Calculate total experience points for a unit
        int32_t total_xp = 0;
        
        // Sum experience from all skills
        for (const auto& skill : u->status.current_soul->skills) {
            total_xp += skill->experience;
        }
        
        return total_xp;
    }
}

class AssignNoblesExclusive : public ExclusiveCallback
{
    AI & ai;
    df::entity_position_responsibility responsibility;

public:
    AssignNoblesExclusive(AI & ai, df::entity_position_responsibility responsibility) :
        ExclusiveCallback("assign noble position with responsibility " + enum_item_key(responsibility)),
        ai(ai),
        responsibility(responsibility)
    {
    }
    ~AssignNoblesExclusive() {}

    void Run(color_ostream & out)
    {
        ExpectScreen<df::viewscreen_dwarfmodest>("dwarfmode/Default");

        bool bookkeeper = responsibility == entity_position_responsibility::ACCOUNTING;

        std::vector<df::unit *> candidates;
        for (auto u : world->units.active)
        {
            if (!Units::isCitizen(u) || Units::isChild(u) || Units::isBaby(u))
            {
                continue;
            }

            if (bookkeeper && u->status.labors[unit_labor::MINE])
            {
                continue;
            }

            std::vector<Units::NoblePosition> positions;
            if (u->mood == mood_type::None && u->military.squad_id == -1 && !Units::getNoblePositions(&positions, u))
            {
                candidates.push_back(u);
            }
        }
        if (candidates.empty())
        {
            ai.debug(out, "Cannot assign noble position for " + enum_item_key(responsibility) + ": no candidates");
            return;
        }
        std::sort(candidates.begin(), candidates.end(), [this](df::unit *a, df::unit *b) -> bool
        {
            return ai.pop.unit_totalxp(a) > ai.pop.unit_totalxp(b);
        });

        Key(interface_key::D_NOBLES);

        ExpectedScreen<df::viewscreen_layer_noblelistst> view(this);

        ExpectScreen<df::viewscreen_layer_noblelistst>("layer_noblelist/List");

        bool found = false;

        for (size_t i = 0; i < view->assignments.size(); i++)
        {
            auto assignment = view->assignments.at(i);

            if (!assignment)
            {
                Key(interface_key::STANDARDSCROLL_DOWN);
                continue;
            }

            auto position = binsearch_in_vector(ui->main.fortress_entity->positions.own, assignment->position_id);
            if (!position || !position->responsibilities[responsibility])
            {
                Key(interface_key::STANDARDSCROLL_DOWN);
                continue;
            }

            found = true;

            auto hf = df::historical_figure::find(assignment->histfig);
            if (hf && hf->died_year != -1)
            {
                Key(interface_key::STANDARDSCROLL_DOWN);
                continue;
            }

            Key(interface_key::SELECT);

            ExpectScreen<df::viewscreen_layer_noblelistst>("layer_noblelist/Appoint");

            int found_candidate = -1;
            while (!candidates.empty() && found_candidate == -1)
            {
                auto candidate_unit = candidates.back();
                for (auto it = view->candidates.begin(); it != view->candidates.end(); it++)
                {
                    if ((*it)->unit == candidate_unit)
                    {
                        found_candidate = it - view->candidates.begin();
                        ai.debug(out, "Appointing " + AI::describe_unit(candidate_unit) + " as position " + position->name[0]);
                        break;
                    }
                }

                if (found_candidate == -1)
                {
                    ai.debug(out, "Discarding candidate " + AI::describe_unit(candidate_unit) + " for position " + position->name[0] + ": does not appear on candidate list");
                    candidates.pop_back();
                }
            }
            if (candidates.empty())
            {
                ai.debug(out, "Ran out of candidates for " + position->name[0] + "!");
                Key(interface_key::LEAVESCREEN);
                ExpectScreen<df::viewscreen_layer_noblelistst>("layer_noblelist/List");
                Key(interface_key::LEAVESCREEN);
                ExpectScreen<df::viewscreen_dwarfmodest>("dwarfmode/Default");
                return;
            }

            while (found_candidate > 0)
            {
                Key(interface_key::STANDARDSCROLL_DOWN);
                found_candidate--;
            }
            Key(interface_key::SELECT);
            ExpectScreen<df::viewscreen_layer_noblelistst>("layer_noblelist/List");

            if (bookkeeper && ui->nobles.bookkeeper_settings != 4)
            {
                Key(interface_key::NOBLELIST_SETTINGS);
                ExpectScreen<df::viewscreen_layer_noblelistst>("layer_noblelist/Settings");

                auto get_list = [&]() -> df::layer_object_listst *
                {
                    df::layer_object_listst *list = nullptr;
                    for (auto obj : view->layer_objects)
                    {
                        auto l = virtual_cast<df::layer_object_listst>(obj);
                        if (l && l->active)
                        {
                            list = l;
                            break;
                        }
                    }
                    DFAI_ASSERT(list, "could not find bookkeeper options list");
                    return list;
                };
                while (get_list() && get_list()->cursor != 4)
                {
                    Key(interface_key::STANDARDSCROLL_DOWN);
                }

                Key(interface_key::SELECT);
                Key(interface_key::LEAVESCREEN);
                ExpectScreen<df::viewscreen_layer_noblelistst>("layer_noblelist/List");
            }

            Key(interface_key::LEAVESCREEN);
            ExpectScreen<df::viewscreen_dwarfmodest>("dwarfmode/Default");

            return;
        }

        if (found)
        {
            ai.debug(out, "Found position for " + enum_item_key(responsibility) + ", but it was already occupied.");
        }
        else
        {
            ai.debug(out, "Could not find position for " + enum_item_key(responsibility));
        }

        Key(interface_key::LEAVESCREEN);
        ExpectScreen<df::viewscreen_dwarfmodest>("dwarfmode/Default");
    }
};

void Population::update_nobles(color_ostream & out)
{
    check_noble_apartments(out);

    if (!config.manage_nobles)
    {
        return;
    }

    for (auto & asn : ui->main.fortress_entity->assignments_by_type[entity_position_responsibility::HEALTH_MANAGEMENT])
    {
        auto hf = df::historical_figure::find(asn->histfig);
        auto doctor = df::unit::find(hf->unit_id);

        // Enable healthcare labors on chief medical officer.
        doctor->status.labors[unit_labor::DIAGNOSE] = true;
        doctor->status.labors[unit_labor::SURGERY] = true;
        doctor->status.labors[unit_labor::BONE_SETTING] = true;
        doctor->status.labors[unit_labor::SUTURING] = true;
        doctor->status.labors[unit_labor::DRESSING_WOUNDS] = true;
    }

#define WANT_POS(pos) \
    if (ui->main.fortress_entity->assignments_by_type[entity_position_responsibility::pos].empty()) \
    { \
        events.queue_exclusive(std::make_unique<AssignNoblesExclusive>(ai, entity_position_responsibility::pos)); \
    }

    WANT_POS(MANAGE_PRODUCTION);
    WANT_POS(ACCOUNTING);
    if (ai.find_room(room_type::infirmary, [](room *r) -> bool { return r->status != room_status::plan; }))
    {
        WANT_POS(HEALTH_MANAGEMENT);
    }
    WANT_POS(TRADE);
    if (ai.find_room(room_type::jail, [](room *r) -> bool { return r->status == room_status::finished; }))
    {
        WANT_POS(LAW_ENFORCEMENT);
        WANT_POS(EXECUTIONS);
    }
}

void Population::check_noble_apartments(color_ostream & out)
{
    std::set<int32_t> noble_ids;

    for (auto asn : ui->main.fortress_entity->positions.assignments)
    {
        df::entity_position *pos = binsearch_in_vector(ui->main.fortress_entity->positions.own, asn->position_id);
        if (pos->required_office > 0 || pos->required_dining > 0 || pos->required_tomb > 0)
        {
            if (df::historical_figure *hf = df::historical_figure::find(asn->histfig))
            {
                noble_ids.insert(hf->unit_id);
            }
        }
    }

    ai.plan.attribute_noblerooms(out, noble_ids);
}

void NoblesManager::CheckNoblePositions(color_ostream& out) {
    logger_.Debug("Checking noble positions...");
    
    if (!world || !world->entities.all.empty()) {
        logger_.Warning("World or entity data not available for noble position checks");
        return;
    }
    
    try {
        auto* fort_entity = world->entities.all[0];
        if (!fort_entity) return;
        
        nobles_.clear();
        
        // Check each noble assignment in the fortress entity
        for (auto& assignment_pair : fort_entity->assignments_by_type) {
            auto responsibility = assignment_pair.first;
            auto& assignments = assignment_pair.second;
            
            for (auto* assignment : assignments) {
                if (!assignment || assignment->histfig == -1) continue;
                
                auto* hf = df::historical_figure::find(assignment->histfig);
                if (!hf || hf->unit_id == -1) continue;
                
                auto* unit = df::unit::find(hf->unit_id);
                if (!unit || Units::isDead(unit)) continue;
                
                // Get position title
                std::string title;
                switch (responsibility) {
                    case entity_position_responsibility::MANAGE_PRODUCTION:
                        title = "Manager";
                        break;
                    case entity_position_responsibility::ACCOUNTING:
                        title = "Bookkeeper";
                        break;
                    case entity_position_responsibility::TRADE:
                        title = "Broker";
                        break;
                    case entity_position_responsibility::HEALTH_MANAGEMENT:
                        title = "Chief Medical Dwarf";
                        break;
                    case entity_position_responsibility::LAW_ENFORCEMENT:
                        title = "Captain of the Guard";
                        break;
                    case entity_position_responsibility::EXECUTIONS:
                        title = "Hammerer";
                        break;
                    default:
                        title = "Noble";
                        break;
                }
                
                nobles_[unit->id] = title;
                logger_.Debug("Found noble: {} - {}", unit->id, title);
            }
        }
        
        logger_.Info("Updated noble positions: {} nobles found", nobles_.size());
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in CheckNoblePositions: {}", e.what());
    }
}

void NoblesManager::CheckNobleApartments(color_ostream& out) {
    logger_.Debug("Checking noble apartment requirements...");
    
    for (const auto& [unit_id, title] : nobles_) {
        df::unit* unit = df::unit::find(unit_id);
        if (!unit) continue;
        
        // Check if noble has required rooms
        bool needs_bedroom = RequiresPrivateBedroom(title);
        bool needs_office = RequiresOffice(title);
        bool needs_dining_room = RequiresDiningRoom(title);
        bool needs_tomb = RequiresTomb(title);
        
        // Check current room assignments
        bool has_bedroom = HasAssignedBedroom(unit);
        bool has_office = HasAssignedOffice(unit);
        bool has_dining_room = HasAssignedDiningRoom(unit);
        bool has_tomb = HasAssignedTomb(unit);
        
        // Add to needing rooms list if requirements not met
        if ((needs_bedroom && !has_bedroom) ||
            (needs_office && !has_office) ||
            (needs_dining_room && !has_dining_room) ||
            (needs_tomb && !has_tomb)) {
            
            nobles_needing_rooms_.insert(unit_id);
            logger_.Debug("Noble {} ({}) needs rooms: bedroom={}, office={}, dining={}, tomb={}", 
                         unit_id, title, needs_bedroom && !has_bedroom,
                         needs_office && !has_office,
                         needs_dining_room && !has_dining_room,
                         needs_tomb && !has_tomb);
        }
    }
}

void NoblesManager::AssignNobleRooms(color_ostream& out) {
    if (nobles_needing_rooms_.empty()) {
        return;
    }
    
    logger_.Info("Assigning rooms for {} nobles", nobles_needing_rooms_.size());
    
    for (int32_t unit_id : nobles_needing_rooms_) {
        auto it = nobles_.find(unit_id);
        if (it != nobles_.end()) {
            logger_.Info("Requesting rooms for noble {} ({})", unit_id, it->second);
            // This would integrate with the planning system to request room construction
        }
    }
    
    nobles_needing_rooms_.clear();
}

// Helper methods for checking noble room requirements
bool NoblesManager::RequiresPrivateBedroom(const std::string& title) const {
    // Most nobles require private bedrooms
    return title != "peasant" && title != "citizen";
}

bool NoblesManager::RequiresOffice(const std::string& title) const {
    // Higher-ranking nobles require offices
    return title == "mayor" || title == "baron" || title == "count" || 
           title == "duke" || title == "king" || title == "manager" ||
           title == "chief_medical_dwarf" || title == "broker";
}

bool NoblesManager::RequiresDiningRoom(const std::string& title) const {
    // High-ranking nobles require private dining rooms
    return title == "baron" || title == "count" || title == "duke" || title == "king";
}

bool NoblesManager::RequiresTomb(const std::string& title) const {
    // All nobles eventually need tombs
    return true;
}

bool NoblesManager::HasAssignedBedroom(df::unit* unit) const {
    // Check if unit has an assigned bedroom
    // This would check DF room assignments
    return false; // Conservative - assume needs assignment
}

bool NoblesManager::HasAssignedOffice(df::unit* unit) const {
    // Check if unit has an assigned office
    // This would check DF room assignments
    return false; // Conservative - assume needs assignment
}

bool NoblesManager::HasAssignedDiningRoom(df::unit* unit) const {
    // Check if unit has an assigned dining room
    // This would check DF room assignments
    return false; // Conservative - assume needs assignment
}

bool NoblesManager::HasAssignedTomb(df::unit* unit) const {
    // Check if unit has an assigned tomb
    // This would check DF room assignments
    return false; // Conservative - assume needs assignment
}

bool NoblesManager::IsNoble(int32_t unit_id) const {
    return nobles_.find(unit_id) != nobles_.end();
}

std::vector<int32_t> NoblesManager::GetNobles() const {
    std::vector<int32_t> result;
    result.reserve(nobles_.size());
    
    for (const auto& [unit_id, title] : nobles_) {
        result.push_back(unit_id);
    }
    
    return result;
}

std::string NoblesManager::GetNobleTitle(int32_t unit_id) const {
    auto it = nobles_.find(unit_id);
    return (it != nobles_.end()) ? it->second : "";
}

std::string NoblesManager::GetStatusReport() const {
    std::ostringstream report;
    
    report << "Nobles: " << nobles_.size() << "\n";
    report << "Needing rooms: " << nobles_needing_rooms_.size() << "\n";
    
    if (!nobles_.empty()) {
        report << "Current nobles:\n";
        for (const auto& [unit_id, title] : nobles_) {
            report << "  " << unit_id << ": " << title << "\n";
        }
    }
    
    return report.str();
}

void NoblesManager::UpdateNoblesList(color_ostream& out) {
    try {
        auto& units = world->units.active;
        
        // Clear outdated noble entries
        nobles_.clear();
        
        for (auto* unit : units) {
            if (!unit || !Units::isCitizen(unit) || Units::isDead(unit)) {
                continue;
            }
            
            // Check if unit has any noble positions
            bool is_noble = false;
            std::vector<std::string> positions;
            
            // Check historical figure noble positions
            if (auto* hist_fig = df::historical_figure::find(unit->hist_figure_id)) {
                for (auto* link : hist_fig->entity_links) {
                    if (auto* pos_link = virtual_cast<df::histfig_entity_link_positionst>(link)) {
                        if (pos_link->assignment) {
                            auto* position = df::historical_entity::find(pos_link->entity_id);
                            if (position) {
                                // Get position name
                                auto* assignment = pos_link->assignment;
                                if (assignment && !assignment->position_id.empty()) {
                                    positions.push_back(assignment->position_id);
                                    is_noble = true;
                                }
                            }
                        }
                    }
                }
            }
            
            // Check if unit is expedition leader, mayor, etc.
            if (unit->military.squad_id != -1) {
                auto* squad = df::squad::find(unit->military.squad_id);
                if (squad && squad->leader_id == unit->id) {
                    positions.push_back("Squad Leader");
                    is_noble = true;
                }
            }
            
            // Add to nobles list if they have positions
            if (is_noble) {
                NobleInfo info;
                info.unit_id = unit->id;
                info.positions = positions;
                info.last_checked = world->frame_counter;
                info.needs_room = true; // Will be determined by CheckNobleRoomRequirements
                
                nobles_[unit->id] = info;
                logger_.Debug(stl_sprintf("Found noble: %s with positions: %s", 
                    Units::getReadableName(unit).c_str(),
                    join_strings(positions, ", ").c_str()));
            }
        }
        
        logger_.Debug(stl_sprintf("Updated nobles list: found %zu nobles", nobles_.size()));
        
    } catch (const std::exception& e) {
        logger_.Error(stl_sprintf("Exception in UpdateNoblesList: %s", e.what()));
    }
}

void NoblesManager::CheckNobleRoomRequirements(color_ostream& out, df::unit* unit) {
    if (!unit) return;
    
    auto noble_it = nobles_.find(unit->id);
    if (noble_it == nobles_.end()) {
        return; // Not a noble
    }
    
    try {
        NobleInfo& info = noble_it->second;
        
        // Determine room requirements based on noble positions
        std::set<room_type::type> required_rooms;
        int32_t required_value = 0;
        
        for (const std::string& position : info.positions) {
            // Map noble positions to room requirements
            if (position == "Expedition Leader" || position == "Mayor") {
                required_rooms.insert(room_type::bedroom);
                required_rooms.insert(room_type::dininghall);
                required_rooms.insert(room_type::office);
                required_value = std::max(required_value, 1000);
            } else if (position == "Baron" || position == "Count") {
                required_rooms.insert(room_type::bedroom);
                required_rooms.insert(room_type::dininghall);
                required_rooms.insert(room_type::office);
                required_rooms.insert(room_type::tomb);
                required_value = std::max(required_value, 2500);
            } else if (position == "Duke" || position == "King") {
                required_rooms.insert(room_type::bedroom);
                required_rooms.insert(room_type::dininghall);
                required_rooms.insert(room_type::office);
                required_rooms.insert(room_type::tomb);
                required_rooms.insert(room_type::throneroom);
                required_value = std::max(required_value, 5000);
            } else if (position == "Captain" || position == "Squad Leader") {
                required_rooms.insert(room_type::bedroom);
                required_rooms.insert(room_type::office);
                required_value = std::max(required_value, 500);
            }
        }
        
        info.required_rooms = std::vector<room_type::type>(required_rooms.begin(), required_rooms.end());
        info.required_value = required_value;
        
        // Check if current rooms are adequate
        bool has_adequate_rooms = HasAdequateRooms(unit);
        
        if (!has_adequate_rooms && info.needs_room) {
            RequestNobleRooms(out, unit);
            logger_.Info(stl_sprintf("Noble %s requires better accommodations (value >= %d)", 
                Units::getReadableName(unit).c_str(), required_value));
        }
        
        info.needs_room = !has_adequate_rooms;
        
    } catch (const std::exception& e) {
        logger_.Error(stl_sprintf("Exception checking noble room requirements for %s: %s", 
            Units::getReadableName(unit).c_str(), e.what()));
    }
}

bool NoblesManager::HasAdequateRooms(df::unit* unit) const {
    if (!unit) return false;
    
    auto noble_it = nobles_.find(unit->id);
    if (noble_it == nobles_.end()) {
        return true; // Non-nobles don't need special rooms
    }
    
    const NobleInfo& info = noble_it->second;
    
    try {
        // Check if unit has required room assignments
        for (room_type::type required_room : info.required_rooms) {
            bool has_room = false;
            int32_t room_value = 0;
            
            // Check for assigned rooms of the required type
            for (auto* building : world->buildings.other[buildings_other_id::ZONE]) {
                if (auto* zone = virtual_cast<df::building_civzonest>(building)) {
                    // Check if this is a room of the right type assigned to this unit
                    if (zone->zone_flags.bits.active && 
                        std::find(zone->assigned_units.begin(), zone->assigned_units.end(), unit->id) != zone->assigned_units.end()) {
                        
                        // Map zone types to room types - improved mapping
                        room_type::type zone_room_type;
                        switch (zone->type) {
                            case civzone_type::Bedroom:
                                zone_room_type = room_type::bedroom;
                                break;
                            case civzone_type::Office:
                                zone_room_type = room_type::office;
                                break;
                            case civzone_type::DiningHall:
                                zone_room_type = room_type::dininghall;
                                break;
                            case civzone_type::Tomb:
                                zone_room_type = room_type::tomb;
                                break;
                            case civzone_type::Barracks:
                                zone_room_type = room_type::barracks;
                                break;
                            case civzone_type::Hospital:
                                zone_room_type = room_type::infirmary;
                                break;
                            case civzone_type::MeetingArea:
                                zone_room_type = room_type::meetinghall;
                                break;
                            case civzone_type::PenPasture:
                                zone_room_type = room_type::pasture;
                                break;
                            case civzone_type::PitPond:
                                zone_room_type = room_type::pit;
                                break;
                            // Add more mappings as needed
                            default:
                                zone_room_type = room_type::unknown;
                                break;
                        }
                        
                        if (zone_room_type == required_room) {
                            has_room = true;
                            
                            // Calculate room value (simplified calculation)
                            room_value = (zone->x2 - zone->x1 + 1) * (zone->y2 - zone->y1 + 1) * 10;
                            
                            // Check furniture value in the room
                            for (auto* furniture : world->buildings.other[buildings_other_id::FURNITURE]) {
                                if (furniture->x >= zone->x1 && furniture->x <= zone->x2 &&
                                    furniture->y >= zone->y1 && furniture->y <= zone->y2 &&
                                    furniture->z == zone->z) {
                                    room_value += 50; // Simplified furniture value
                                }
                            }
                            
                            break;
                        }
                    }
                }
            }
            
            // If we don't have this required room type, or it's not valuable enough
            if (!has_room || room_value < info.required_value) {
                return false;
            }
        }
        
        return true; // All required rooms are adequate
        
    } catch (const std::exception& e) {
        logger_.Error(stl_sprintf("Exception checking room adequacy for %s: %s", 
            Units::getReadableName(unit).c_str(), e.what()));
        return false;
    }
}

void NoblesManager::RequestNobleRooms(color_ostream& out, df::unit* unit) {
    if (!unit) return;
    
    auto noble_it = nobles_.find(unit->id);
    if (noble_it == nobles_.end()) {
        return;
    }
    
    const NobleInfo& info = noble_it->second;
    
    try {
        // Interface with the planning system to request room construction
        // This would typically interact with the Plan or PlanManager
        
        for (room_type::type required_room : info.required_rooms) {
            // Create room request parameters
            room_request request;
            request.type = required_room;
            request.user = unit->id;
            request.minimum_value = info.required_value;
            request.priority = 5; // High priority for noble rooms
            
            // Set room size based on type and noble rank
            switch (required_room) {
                case room_type::bedroom:
                    request.w = 3;
                    request.h = 3;
                    break;
                case room_type::office:
                    request.w = 4;
                    request.h = 4;
                    break;
                case room_type::dininghall:
                    request.w = 5;
                    request.h = 5;
                    break;
                case room_type::throneroom:
                    request.w = 7;
                    request.h = 7;
                    break;
                case room_type::tomb:
                    request.w = 3;
                    request.h = 3;
                    break;
                default:
                    request.w = 3;
                    request.h = 3;
                    break;
            }
            
            // Add room request to planning system
            // This would need to interface with the actual planning system
            logger_.Info(stl_sprintf("Requesting %s for noble %s (value >= %d, size %dx%d)",
                room_type_names[required_room].c_str(),
                Units::getReadableName(unit).c_str(),
                info.required_value,
                request.w, request.h));
            
            // In a real implementation, this would call something like:
            // ai.plan.add_room_request(request);
            // or
            // planning_manager_->RequestRoom(request);
        }
        
    } catch (const std::exception& e) {
        logger_.Error(stl_sprintf("Exception requesting rooms for noble %s: %s", 
            Units::getReadableName(unit).c_str(), e.what()));
    }
}

} // namespace population
} // namespace dfai
