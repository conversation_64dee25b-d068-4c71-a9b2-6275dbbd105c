#pragma once

#include "../utils/common/common.h"
#include "../debug/logging.h"
#include "plan_types.h"

#include <list>
#include <queue>
#include <memory>
#include <string>
#include <vector>

// Forward declarations
class color_ostream;

namespace dfai {
namespace planning {

// Forward declaration of task types
class Task;
class PriorityTask;
class GenericTask;
class FurnitureTask;
class ConstructionTask;
class DiggingTask;

/**
 * Modern task management system
 * Handles task scheduling, execution, and prioritization
 */
class TaskManager {
public:
    TaskManager();
    ~TaskManager();
    
    // Core lifecycle
    bool Initialize(color_ostream& out);
    void Shutdown();
    void Update(color_ostream& out);
    
    // Task management
    void AddTask(std::unique_ptr<Task> task);
    void AddPriorityTask(std::unique_ptr<PriorityTask> task);
    void RemoveTask(int32_t task_id);
    void ClearAllTasks();
    
    // Task execution control
    void SetMaxTasksPerUpdate(int32_t max_tasks);
    void PauseTaskExecution();
    void ResumeTaskExecution();
    bool IsTaskExecutionPaused() const { return execution_paused_; }
    
    // Task queries
    size_t GetTaskCount() const;
    size_t GetPriorityTaskCount() const;
    std::vector<int32_t> GetActiveTasks() const;
    std::shared_ptr<Task> GetTask(int32_t task_id) const;
    
    // Status reporting
    std::string GetStatusReport() const;
    void GenerateReport(std::ostream& out, bool html = false) const;

private:
    // Core state
    debug::Logger& logger_;
    bool initialized_;
    bool execution_paused_;
    
    // Task storage
    std::list<std::shared_ptr<Task>> generic_tasks_;
    std::list<std::shared_ptr<Task>> furniture_tasks_;
    std::list<std::shared_ptr<Task>> construction_tasks_;
    std::list<std::shared_ptr<Task>> digging_tasks_;
    std::queue<std::shared_ptr<PriorityTask>> priority_tasks_;
    
    // Task execution state
    std::list<std::shared_ptr<Task>>::iterator current_generic_task_;
    std::list<std::shared_ptr<Task>>::iterator current_furniture_task_;
    std::list<std::shared_ptr<Task>>::iterator current_construction_task_;
    std::list<std::shared_ptr<Task>>::iterator current_digging_task_;
    
    // Execution control
    int32_t max_tasks_per_update_;
    int32_t tasks_executed_this_update_;
    size_t total_tasks_executed_;
    
    // Task ID generation
    int32_t next_task_id_;
    
    // Internal methods
    void ExecuteTasks(color_ostream& out);
    void ExecutePriorityTasks(color_ostream& out);
    void ExecuteGenericTasks(color_ostream& out);
    void ExecuteFurnitureTasks(color_ostream& out);
    void ExecuteConstructionTasks(color_ostream& out);
    void ExecuteDiggingTasks(color_ostream& out);
    
    bool ExecuteTask(std::shared_ptr<Task> task, color_ostream& out);
    void CleanupCompletedTasks();
    void UpdateTaskIterators();
    
    // Helper methods
    std::list<std::shared_ptr<Task>>& GetTaskList(Task* task);
    std::list<std::shared_ptr<Task>>::iterator& GetTaskIterator(Task* task);
    int32_t GenerateTaskId();
};
    bool execute_next_task(color_ostream& out);
    bool execute_task(color_ostream& out, Task* task);
    
    // Task queries
    Task* get_current_task() const;
    bool has_tasks() const;
    bool has_priority_tasks() const;
    size_t task_count() const;
    size_t priority_task_count() const;
    
    // Task status
    bool is_idle() const;
    Task* is_digging() const;
    std::vector<Task*> get_tasks_for_room(room* r) const;
    std::vector<Task*> get_tasks_by_type(task_type::type type) const;
    
    // Task creation helpers
    Task* create_dig_task(room* r);
    Task* create_construct_task(room* r, furniture* f = nullptr);
    Task* create_furnish_task(room* r, furniture* f);
    Task* create_smooth_task(room* r);
    Task* create_setup_task(room* r, task_type::type setup_type);
    
    // Settings
    void set_max_tasks_per_update(int32_t max) { max_tasks_per_update_ = max; }
    int32_t max_tasks_per_update() const { return max_tasks_per_update_; }
    
    // Status and reporting
    std::string status() const;
    void report(std::ostream& out, bool html = false) const;
    
private:
    // Task execution helpers
    bool execute_dig_task(color_ostream& out, Task* task);
    bool execute_construct_task(color_ostream& out, Task* task);    // Task execution with improved error handling
    bool execute_dig_task(color_ostream& out, Task* task);
    bool execute_furnish_task(color_ostream& out, Task* task);
    bool execute_smooth_task(color_ostream& out, Task* task);
    bool execute_setup_task(color_ostream& out, Task* task);
    bool execute_monitor_task(color_ostream& out, Task* task);
    
    // Enhanced task validation
    enum class ValidationResult {
        VALID,
        INVALID_PARAMETERS,
        MISSING_DEPENDENCIES,
        RESOURCE_UNAVAILABLE,
        PRECONDITION_FAILED,
        TIMEOUT_EXCEEDED
    };
    
    ValidationResult validate_task(Task* task) const;
    bool can_execute_task(Task* task) const;
    std::string get_validation_error_message(ValidationResult result) const;
    
    // Improved error handling
    enum class TaskFailureReason {
        UNKNOWN,
        INVALID_COORDINATES,
        MISSING_MATERIALS,
        INACCESSIBLE_LOCATION,
        UNIT_UNAVAILABLE,
        BUILDING_OBSTRUCTED,
        DESIGNATION_FAILED,
        RESOURCE_CONFLICT,
        TIMEOUT,
        GAME_STATE_INVALID
    };
    
    void mark_task_failed(Task* task, TaskFailureReason reason, const std::string& details = "");
    void mark_task_completed(Task* task);
    void mark_task_retry(Task* task, const std::string& reason = "");
    
    // Task recovery and retry logic
    bool should_retry_task(Task* task) const;
    std::chrono::seconds get_retry_delay(Task* task) const;
    void schedule_task_retry(Task* task, std::chrono::seconds delay);
    
    // Task prioritization with dynamic adjustment
    void sort_tasks_by_priority();
    int32_t calculate_task_priority(Task* task) const;
    void adjust_task_priority(Task* task, int32_t priority_delta);
    void boost_critical_tasks();
    
    // Deadlock detection and resolution
    bool detect_task_deadlock() const;
    std::vector<Task*> find_deadlocked_tasks() const;
    void resolve_task_deadlock(const std::vector<Task*>& deadlocked_tasks);
    
    // Resource conflict resolution
    bool has_resource_conflict(Task* task) const;
    std::vector<Task*> get_conflicting_tasks(Task* task) const;
    void resolve_resource_conflicts();
    
    // Performance monitoring
    struct TaskPerformanceMetrics {
        size_t total_tasks_executed = 0;
        size_t successful_tasks = 0;
        size_t failed_tasks = 0;
        size_t retried_tasks = 0;
        double average_execution_time_ms = 0.0;
        std::map<TaskFailureReason, size_t> failure_counts;
        std::chrono::steady_clock::time_point last_reset;
    };
    
    TaskPerformanceMetrics GetPerformanceMetrics() const;
    void ResetPerformanceMetrics();
    
    // Cleanup with improved safety
    void cleanup_completed_tasks();
    void cleanup_failed_tasks();
    void cleanup_expired_tasks();
    void force_cleanup_all_tasks(); // Emergency cleanup
};

} // namespace planning
} // namespace dfai
