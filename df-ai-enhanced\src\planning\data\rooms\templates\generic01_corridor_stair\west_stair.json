{"$schema": "https://ben.lubar.me/df-ai-schemas/room-template.json", "f": [{"type": "door", "x": 3, "y": 0, "z": 0, "ignore": true}, {"type": "door", "x": 3, "y": 2, "z": 0, "ignore": true}, {"construction": "UpDownStair", "dig": "UpDownStair", "x": 0, "y": 0, "z": 0, "stairs_special": true}, {"construction": "UpDownStair", "dig": "UpDownStair", "x": 1, "y": 0, "z": 0, "stairs_special": true}, {"construction": "UpDownStair", "dig": "UpDownStair", "x": 2, "y": 0, "z": 0, "stairs_special": true}, {"construction": "UpDownStair", "dig": "UpDownStair", "x": 0, "y": 1, "z": 0, "stairs_special": true}, {"construction": "UpDownStair", "dig": "UpDownStair", "x": 1, "y": 1, "z": 0, "stairs_special": true}, {"construction": "UpDownStair", "dig": "UpDownStair", "x": 2, "y": 1, "z": 0, "stairs_special": true}, {"construction": "UpDownStair", "dig": "UpDownStair", "x": 0, "y": 2, "z": 0, "stairs_special": true}, {"construction": "UpDownStair", "dig": "UpDownStair", "x": 1, "y": 2, "z": 0, "stairs_special": true}, {"construction": "UpDownStair", "dig": "UpDownStair", "x": 2, "y": 2, "z": 0, "stairs_special": true}], "r": [{"type": "corridor", "corridor_type": "corridor", "min": [-3, -1, 0], "max": [-1, 1, 0], "layout": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "exits": [["generic01_corridor", 1, -1, 0], ["generic01_corridor", -1, 1, 0], ["generic01_corridor", 1, 3, 0], ["generic01_corridor", 3, 1, 0], ["generic01_stair", 1, 1, -1], ["generic01_stair", 1, 1, 1]], "comment": "staircase", "remove_if_unused": true}]}