#pragma once

#include "../utils/common/common.h"
#include "../debug/logging.h"
#include "../utils/callbacks/exclusive_callback.h"

#include <memory>
#include <functional>

namespace dfai {
namespace population {

/**
 * @brief Enhanced exclusive callback system for population management
 * 
 * Addresses FIXME issues by providing proper ExclusiveCallback implementations
 * for zone assignments, justice actions, and pet management.
 */
class PopulationCallbacks {
public:
    PopulationCallbacks();
    ~PopulationCallbacks();
    
    // Zone assignment callbacks
    std::unique_ptr<ExclusiveCallback> CreateZoneAssignmentCallback(
        df::unit* unit, df::building_civzonest* zone, const std::string& purpose = "");
    
    // Justice system callbacks
    std::unique_ptr<ExclusiveCallback> CreateJusticeActionCallback(
        df::unit* criminal, const std::string& crime, const std::string& punishment);
    
    // Pet management callbacks
    std::unique_ptr<ExclusiveCallback> CreatePetAssignmentCallback(
        df::unit* pet, df::unit* owner, const std::string& assignment_type);
    
    // Pasture management callbacks
    std::unique_ptr<ExclusiveCallback> CreatePastureAssignmentCallback(
        df::unit* animal, df::building_civzonest* pasture);
    
private:
    debug::Logger& logger_;
    
    // Callback implementations
    class ZoneAssignmentCallback;
    class JusticeActionCallback;
    class PetAssignmentCallback;
    class PastureAssignmentCallback;
};

/**
 * @brief Zone assignment exclusive callback
 * Replaces direct UI manipulation with proper callback pattern
 */
class PopulationCallbacks::ZoneAssignmentCallback : public ExclusiveCallback {
public:
    ZoneAssignmentCallback(df::unit* unit, df::building_civzonest* zone, const std::string& purpose);
    
protected:
    void Run(color_ostream& out) override;
    
private:
    df::unit* unit_;
    df::building_civzonest* zone_;
    std::string purpose_;
    
    bool ValidateAssignment(color_ostream& out);
    bool ExecuteAssignment(color_ostream& out);
    void HandleAssignmentFailure(color_ostream& out, const std::string& reason);
};

/**
 * @brief Justice action exclusive callback
 * Handles criminal justice actions through proper callback system
 */
class PopulationCallbacks::JusticeActionCallback : public ExclusiveCallback {
public:
    JusticeActionCallback(df::unit* criminal, const std::string& crime, const std::string& punishment);
    
protected:
    void Run(color_ostream& out) override;
    
private:
    df::unit* criminal_;
    std::string crime_;
    std::string punishment_;
    
    bool ValidateJusticeAction(color_ostream& out);
    bool ExecuteJusticeAction(color_ostream& out);
    void LogJusticeAction(color_ostream& out);
};

/**
 * @brief Pet assignment exclusive callback
 * Manages pet-owner assignments through callback system
 */
class PopulationCallbacks::PetAssignmentCallback : public ExclusiveCallback {
public:
    PetAssignmentCallback(df::unit* pet, df::unit* owner, const std::string& assignment_type);
    
protected:
    void Run(color_ostream& out) override;
    
private:
    df::unit* pet_;
    df::unit* owner_;
    std::string assignment_type_;
    
    bool ValidatePetAssignment(color_ostream& out);
    bool ExecutePetAssignment(color_ostream& out);
    void HandleAssignmentResult(color_ostream& out, bool success);
};

/**
 * @brief Pasture assignment exclusive callback
 * Manages animal-pasture assignments
 */
class PopulationCallbacks::PastureAssignmentCallback : public ExclusiveCallback {
public:
    PastureAssignmentCallback(df::unit* animal, df::building_civzonest* pasture);
    
protected:
    void Run(color_ostream& out) override;
    
private:
    df::unit* animal_;
    df::building_civzonest* pasture_;
    
    bool ValidatePastureAssignment(color_ostream& out);
    bool ExecutePastureAssignment(color_ostream& out);
    bool CheckPastureCapacity(color_ostream& out);
};

/**
 * @brief Enhanced population manager with proper callback integration
 * 
 * Replaces direct UI manipulation calls with proper ExclusiveCallback usage
 */
class EnhancedPopulationManager {
public:
    EnhancedPopulationManager();
    ~EnhancedPopulationManager();
    
    // Zone management with callbacks
    bool AssignUnitToZone(df::unit* unit, df::building_civzonest* zone, 
                         const std::string& purpose = "", color_ostream& out = Core::getInstance().getConsole());
    
    // Justice management with callbacks
    bool ExecuteJusticeAction(df::unit* criminal, const std::string& crime, 
                             const std::string& punishment, color_ostream& out = Core::getInstance().getConsole());
    
    // Pet management with callbacks
    bool AssignPetToOwner(df::unit* pet, df::unit* owner, const std::string& assignment_type = "companion",
                         color_ostream& out = Core::getInstance().getConsole());
    
    // Pasture management with callbacks
    bool AssignAnimalToPasture(df::unit* animal, df::building_civzonest* pasture,
                              color_ostream& out = Core::getInstance().getConsole());
    
    // Callback status monitoring
    size_t GetPendingCallbackCount() const;
    void ClearCompletedCallbacks();
    std::vector<std::string> GetCallbackStatus() const;
    
private:
    debug::Logger& logger_;
    PopulationCallbacks callback_factory_;
    std::vector<std::shared_ptr<ExclusiveCallback>> pending_callbacks_;
    
    // Helper methods
    void QueueCallback(std::unique_ptr<ExclusiveCallback> callback);
    void ProcessCompletedCallbacks();
};

} // namespace population
} // namespace dfai
