{"$schema": "https://ben.lubar.me/df-ai-schemas/room-template.json", "f": [{"type": "door", "x": 10, "y": 4, "z": 0}, {"type": "door", "x": 10, "y": 6, "z": 0}, {"type": "chest", "x": 0, "y": 0, "z": 0}, {"type": "chest", "x": 1, "y": 0, "z": 0}, {"type": "table", "x": 0, "y": 1, "z": 0, "placeholder": 1}, {"type": "chair", "x": 1, "y": 1, "z": 0, "placeholder": 1}, {"type": "table", "x": 0, "y": 2, "z": 0, "placeholder": 1}, {"type": "chair", "x": 1, "y": 2, "z": 0, "placeholder": 1}, {"type": "table", "x": 0, "y": 3, "z": 0, "placeholder": 1}, {"type": "chair", "x": 1, "y": 3, "z": 0, "placeholder": 1}, {"type": "table", "x": 0, "y": 4, "z": 0, "placeholder": 1}, {"type": "chair", "x": 1, "y": 4, "z": 0, "placeholder": 1}, {"type": "table", "x": 0, "y": 5, "z": 0, "placeholder": 1}, {"type": "chair", "x": 1, "y": 5, "z": 0, "placeholder": 1}, {"type": "table", "x": 0, "y": 6, "z": 0, "placeholder": 1}, {"type": "chair", "x": 1, "y": 6, "z": 0, "placeholder": 1}, {"type": "table", "x": 0, "y": 7, "z": 0, "placeholder": 1}, {"type": "chair", "x": 1, "y": 7, "z": 0, "placeholder": 1}, {"type": "table", "x": 0, "y": 8, "z": 0, "placeholder": 1}, {"type": "chair", "x": 1, "y": 8, "z": 0, "placeholder": 1}, {"type": "table", "x": 0, "y": 9, "z": 0, "placeholder": 1}, {"type": "chair", "x": 1, "y": 9, "z": 0, "placeholder": 1}, {"x": 3, "y": 1, "z": 0, "placeholder": 3}, {"x": 4, "y": 1, "z": 0, "placeholder": 3}, {"x": 5, "y": 1, "z": 0, "placeholder": 3}, {"x": 6, "y": 1, "z": 0, "placeholder": 3}, {"x": 7, "y": 1, "z": 0, "placeholder": 3}, {"x": 8, "y": 1, "z": 0, "placeholder": 3}, {"x": 3, "y": 3, "z": 0, "placeholder": 3}, {"x": 4, "y": 3, "z": 0, "placeholder": 3}, {"x": 5, "y": 3, "z": 0, "placeholder": 3}, {"x": 6, "y": 3, "z": 0, "placeholder": 3}, {"x": 7, "y": 3, "z": 0, "placeholder": 3}, {"x": 8, "y": 3, "z": 0, "placeholder": 3}, {"x": 3, "y": 6, "z": 0, "placeholder": 3}, {"x": 4, "y": 6, "z": 0, "placeholder": 3}, {"x": 5, "y": 6, "z": 0, "placeholder": 3}, {"x": 6, "y": 6, "z": 0, "placeholder": 3}, {"x": 7, "y": 6, "z": 0, "placeholder": 3}, {"x": 8, "y": 6, "z": 0, "placeholder": 3}, {"x": 3, "y": 8, "z": 0, "placeholder": 3}, {"x": 4, "y": 8, "z": 0, "placeholder": 3}, {"x": 5, "y": 8, "z": 0, "placeholder": 3}, {"x": 6, "y": 8, "z": 0, "placeholder": 3}, {"x": 7, "y": 8, "z": 0, "placeholder": 3}, {"x": 8, "y": 8, "z": 0, "placeholder": 3}], "r": [{"type": "location", "min": [-10, -5, 0], "max": [-1, 5, 0], "layout": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], "placeholder": 0}, {"min": [-8, -5, 0], "max": [-1, -5, 0], "workshop": 0, "placeholder": 2}]}