#pragma once

#include "common.h"
#include "../../config.h"

namespace df_ai {
namespace utils {

/**
 * Centralized configuration management
 */
class ConfigManager {
private:
    Config config_;
    static ConfigManager* instance_;
    
public:
    static ConfigManager& instance();
    
    // Configuration access
    const Config& config() const { return config_; }
    Config& config() { return config_; }
    
    // Specific setting accessors
    bool random_embark() const { return config_.random_embark; }
    const std::string& random_embark_world() const { return config_.random_embark_world; }
    bool write_console() const { return config_.write_console; }
    bool write_log() const { return config_.write_log; }
    bool record_movie() const { return config_.record_movie; }
    bool no_quit() const { return config_.no_quit; }
    int32_t world_size() const { return config_.world_size; }
    bool camera_enabled() const { return config_.camera; }
    bool fps_meter() const { return config_.fps_meter; }
    const std::string& manage_labors() const { return config_.manage_labors; }
    bool manage_nobles() const { return config_.manage_nobles; }
    uint8_t cancel_announce() const { return config_.cancel_announce; }
    bool lockstep() const { return config_.lockstep; }
    bool allow_pause() const { return config_.allow_pause; }
    
    // Configuration modification
    void set_random_embark(bool value) { config_.random_embark = value; }
    void set_random_embark_world(const std::string& world) { config_.random_embark_world = world; }
    void set_write_console(bool value) { config_.write_console = value; }
    void set_write_log(bool value) { config_.write_log = value; }
    void set_record_movie(bool value) { config_.record_movie = value; }
    void set_no_quit(bool value) { config_.no_quit = value; }
    void set_world_size(int32_t size) { config_.world_size = size; }
    void set_camera_enabled(bool value) { config_.camera = value; }
    void set_fps_meter(bool value) { config_.fps_meter = value; }
    void set_manage_labors(const std::string& mode) { config_.manage_labors = mode; }
    void set_manage_nobles(bool value) { config_.manage_nobles = value; }
    void set_cancel_announce(uint8_t value) { config_.cancel_announce = value; }
    void set_lockstep(bool value) { config_.lockstep = value; }
    void set_allow_pause(bool value) { config_.allow_pause = value; }
    
    // File operations
    void load(color_ostream& out);
    void save(color_ostream& out);
    
private:
    ConfigManager() = default;
};

} // namespace utils
} // namespace df_ai
