#pragma once

#include "../common/common.h"
#include "../debug/logging.h"

#include <chrono>
#include <map>
#include <vector>
#include <mutex>

namespace dfai {
namespace utils {

/**
 * @brief Performance monitoring and profiling system for DF-AI
 * 
 * Tracks performance metrics, execution times, and provides
 * profiling information for optimization purposes.
 */
class PerformanceMonitor {
public:
    PerformanceMonitor();
    ~PerformanceMonitor();
    
    // Lifecycle
    bool Initialize();
    void Shutdown();
    
    // Profiling
    class ProfileScope {
    public:
        ProfileScope(const std::string& name, PerformanceMonitor& monitor);
        ~ProfileScope();
        
    private:
        std::string name_;
        PerformanceMonitor& monitor_;
        std::chrono::high_resolution_clock::time_point start_time_;
    };
    
    // Manual timing
    void StartTimer(const std::string& name);
    void EndTimer(const std::string& name);
    
    // Metrics tracking
    void IncrementCounter(const std::string& name, int32_t value = 1);
    void SetGauge(const std::string& name, double value);
    void RecordValue(const std::string& name, double value);
    
    // Memory usage tracking
    void RecordMemoryUsage(const std::string& component, size_t bytes);
    size_t GetMemoryUsage(const std::string& component) const;
    size_t GetTotalMemoryUsage() const;
    
    // Performance statistics
    struct PerformanceStats {
        std::string name;
        size_t call_count = 0;
        double total_time_ms = 0.0;
        double average_time_ms = 0.0;
        double min_time_ms = std::numeric_limits<double>::max();
        double max_time_ms = 0.0;
        double last_time_ms = 0.0;
    };
    
    std::vector<PerformanceStats> GetStats() const;
    PerformanceStats GetStats(const std::string& name) const;
    
    // Reporting
    void GenerateReport(color_ostream& out, bool detailed = false) const;
    void GenerateHTMLReport(std::ostream& out) const;
    std::string GetQuickSummary() const;
    
    // Configuration
    void SetEnabled(bool enabled) { enabled_ = enabled; }
    bool IsEnabled() const { return enabled_; }
    void SetMaxHistorySize(size_t size) { max_history_size_ = size; }
    
    // Watchdog functionality
    void StartWatchdog(const std::string& operation, double timeout_seconds);
    void StopWatchdog(const std::string& operation);
    std::vector<std::string> GetTimedOutOperations() const;
    
    // System resource monitoring
    struct SystemStats {
        double cpu_percent = 0.0;
        size_t memory_mb = 0;
        size_t virtual_memory_mb = 0;
        double disk_io_mbps = 0.0;
        size_t thread_count = 0;
    };
    
    SystemStats GetSystemStats() const;
    void UpdateSystemStats();
    
    // Memory pool tracking for performance optimization
    struct MemoryPool {
        std::string name;
        size_t total_size;
        size_t used_size;
        size_t peak_usage;
        size_t allocation_count;
        size_t deallocation_count;
        std::chrono::steady_clock::time_point created_at;
    };
    
    // Cache performance metrics
    struct CacheMetrics {
        std::string cache_name;
        size_t hits = 0;
        size_t misses = 0;
        size_t evictions = 0;
        double hit_ratio() const { return hits + misses > 0 ? static_cast<double>(hits) / (hits + misses) : 0.0; }
    };
    
    // Performance prediction
    struct PredictiveMetrics {
        std::string operation;
        std::vector<double> execution_times;
        double predicted_next_execution_ms = 0.0;
        double confidence_level = 0.0;
        std::chrono::steady_clock::time_point last_prediction;
    };
    
    // Memory pool management
    void RegisterMemoryPool(const std::string& name, size_t total_size);
    void UpdateMemoryPool(const std::string& name, size_t used_size, size_t allocation_count = 0, size_t deallocation_count = 0);
    MemoryPool GetMemoryPoolStats(const std::string& name) const;
    std::vector<MemoryPool> GetAllMemoryPools() const;
    
    // Cache monitoring
    void RecordCacheHit(const std::string& cache_name);
    void RecordCacheMiss(const std::string& cache_name);
    void RecordCacheEviction(const std::string& cache_name);
    CacheMetrics GetCacheMetrics(const std::string& cache_name) const;
    std::vector<CacheMetrics> GetAllCacheMetrics() const;
    
    // Performance prediction
    void UpdatePrediction(const std::string& operation, double execution_time_ms);
    double GetPredictedExecutionTime(const std::string& operation) const;
    double GetPredictionConfidence(const std::string& operation) const;
    
    // Advanced analytics
    std::map<std::string, double> GetBottleneckAnalysis() const;
    std::vector<std::string> GetPerformanceRecommendations() const;
    bool IsOperationTrending(const std::string& operation, bool upward = true) const;
    
    // Profiling optimization
    class SmartProfileScope {
    public:
        SmartProfileScope(const std::string& name, PerformanceMonitor& monitor, bool adaptive = true);
        ~SmartProfileScope();
        
        void AddContext(const std::string& key, const std::string& value);
        void SetImportanceLevel(int level); // 1-5, 5 being most important
        
    private:
        std::string name_;
        PerformanceMonitor& monitor_;
        std::chrono::high_resolution_clock::time_point start_time_;
        std::map<std::string, std::string> context_;
        int importance_level_;
        bool adaptive_;
    };

private:
    debug::Logger& logger_;
    bool initialized_;
    bool enabled_;
    size_t max_history_size_;
    mutable std::mutex stats_mutex_;
    
    // Performance data
    std::map<std::string, PerformanceStats> performance_stats_;
    std::map<std::string, std::chrono::high_resolution_clock::time_point> active_timers_;
    std::map<std::string, int32_t> counters_;
    std::map<std::string, double> gauges_;
    std::map<std::string, std::vector<double>> value_history_;
    std::map<std::string, size_t> memory_usage_;
    
    // Watchdog
    struct WatchdogEntry {
        std::chrono::high_resolution_clock::time_point start_time;
        double timeout_seconds;
    };
    std::map<std::string, WatchdogEntry> watchdog_entries_;
    
    // System monitoring
    SystemStats system_stats_;
    std::chrono::steady_clock::time_point last_system_update_;
    
    // Additional data members for new features
    std::map<std::string, MemoryPool> memory_pools_;
    std::map<std::string, CacheMetrics> cache_metrics_;
    std::map<std::string, PredictiveMetrics> predictive_metrics_;
    std::map<std::string, std::vector<std::pair<std::chrono::steady_clock::time_point, double>>> trend_data_;
    
    // Performance optimization settings
    bool adaptive_profiling_enabled_;
    int minimum_importance_level_;
    size_t max_trend_data_points_;
    
    // Helper methods
    double GetElapsedMilliseconds(std::chrono::high_resolution_clock::time_point start) const;
    void UpdatePerformanceStats(const std::string& name, double elapsed_ms);
    void TrimHistory(std::vector<double>& history);
    
    // System specific helpers
#ifdef _WIN32
    void UpdateWindowsSystemStats();
#else
    void UpdateLinuxSystemStats();
#endif

    // Helper methods for new features
    void UpdateTrendData(const std::string& operation, double value);
    double CalculateTrend(const std::vector<std::pair<std::chrono::steady_clock::time_point, double>>& data) const;
    double PredictNextValue(const std::vector<double>& values) const;
    void OptimizeDataStructures();
    void AnalyzeBottlenecks();
};

// Convenient macro for profiling scopes
#define DF_AI_PROFILE(monitor, name) \
    dfai::utils::PerformanceMonitor::ProfileScope _profile_scope(name, monitor)

} // namespace utils
} // namespace dfai
