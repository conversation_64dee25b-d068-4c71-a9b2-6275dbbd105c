#pragma once

#include "../utils/common.h"
#include "../../room.h"
#include "../../plan_priorities.h"

namespace df_ai {
namespace planning {

/**
 * Task types for construction and management
 */
namespace task_type {
    enum type {
        want_dig,
        want_construct,
        want_deconstruct,
        furnish,
        unfurnish,
        rescue_caged,
        setup_farmplot,
        setup_cistern,
        setup_stockpile,
        setup_workshop,
        dig_room,
        dig_corridor,
        smooth_room,
        smooth_corridor,
        check_room,
        check_corridor,
        monitor_cistern,
        monitor_farm,
        _task_type_count
    };
}

/**
 * Individual task structure
 */
struct Task {
    task_type::type type;
    room* target_room;
    furniture* target_furniture;
    std::string last_status;
    int32_t item_id;
    int32_t priority;
    int32_t retry_count;
    int32_t created_tick;
    
    Task(task_type::type t, room* r = nullptr, furniture* f = nullptr, int32_t id = -1)
        : type(t), target_room(r), target_furniture(f), last_status(""), 
          item_id(id), priority(0), retry_count(0), created_tick(0) {}
};

/**
 * Plan priority from the original system
 */
using PlanPriority = plan_priority_t;

/**
 * Construction materials and requirements
 */
struct MaterialRequirement {
    df::item_type item_type;
    int16_t item_subtype;
    df::job_material_category material_category;
    int32_t quantity;
    bool allow_quality_substitution;
    bool allow_material_substitution;
    
    MaterialRequirement(df::item_type type, int16_t subtype = -1, int32_t qty = 1)
        : item_type(type), item_subtype(subtype), quantity(qty),
          allow_quality_substitution(true), allow_material_substitution(false) {
        material_category.whole = 0;
    }
};

/**
 * Construction job information
 */
struct ConstructionJob {
    df::coord position;
    df::building_type building_type;
    int16_t building_subtype;
    std::vector<MaterialRequirement> materials;
    room* associated_room;
    furniture* associated_furniture;
    int32_t priority;
    
    ConstructionJob(df::coord pos, df::building_type type, int16_t subtype = -1)
        : position(pos), building_type(type), building_subtype(subtype),
          associated_room(nullptr), associated_furniture(nullptr), priority(0) {}
};

/**
 * Room configuration parameters
 */
struct RoomConfig {
    room_type::type type;
    df::coord size;
    int32_t min_quality;
    int32_t max_quality;
    bool requires_furniture;
    bool requires_smoothing;
    bool requires_engraving;
    std::vector<layout_type::type> required_furniture_types;
    
    RoomConfig(room_type::type t) : type(t), size(3, 3, 1), min_quality(0), max_quality(5),
                                   requires_furniture(true), requires_smoothing(false),
                                   requires_engraving(false) {}
};

/**
 * Digging designation information
 */
struct DigDesignation {
    df::coord position;
    df::tile_dig_designation designation;
    int32_t priority;
    room* associated_room;
    std::string reason;
    
    DigDesignation(df::coord pos, df::tile_dig_designation dig_type, int32_t prio = 0)
        : position(pos), designation(dig_type), priority(prio), associated_room(nullptr) {}
};

/**
 * Smoothing job information
 */
struct SmoothingJob {
    df::coord position;
    bool engrave;
    room* associated_room;
    int32_t priority;
    
    SmoothingJob(df::coord pos, bool do_engrave = false, int32_t prio = 0)
        : position(pos), engrave(do_engrave), associated_room(nullptr), priority(prio) {}
};

/**
 * Workshop configuration
 */
struct WorkshopConfig {
    df::workshop_type type;
    std::string name;
    bool is_critical;
    bool auto_setup;
    int32_t min_count;
    int32_t max_count;
    std::vector<MaterialRequirement> setup_materials;
    
    WorkshopConfig(df::workshop_type t, const std::string& n, bool critical = false)
        : type(t), name(n), is_critical(critical), auto_setup(true),
          min_count(1), max_count(3) {}
};

/**
 * Furnace configuration
 */
struct FurnaceConfig {
    df::furnace_type type;
    std::string name;
    bool is_critical;
    bool auto_setup;
    int32_t min_count;
    int32_t max_count;
    std::vector<MaterialRequirement> setup_materials;
    
    FurnaceConfig(df::furnace_type t, const std::string& n, bool critical = false)
        : type(t), name(n), is_critical(critical), auto_setup(true),
          min_count(1), max_count(2) {}
};

/**
 * Stockpile configuration
 */
struct StockpileConfig {
    stockpile_type::type type;
    std::string name;
    df::coord size;
    bool auto_link;
    bool quantum_enabled;
    std::set<df::stockpile_list> disabled_categories;
    
    StockpileConfig(stockpile_type::type t, const std::string& n)
        : type(t), name(n), size(5, 5, 1), auto_link(true), quantum_enabled(false) {}
};

/**
 * Planning statistics
 */
struct PlanningStats {
    size_t total_rooms = 0;
    size_t completed_rooms = 0;
    size_t active_tasks = 0;
    size_t pending_tasks = 0;
    size_t failed_tasks = 0;
    size_t dig_jobs = 0;
    size_t construction_jobs = 0;
    size_t furniture_jobs = 0;
    size_t smoothing_jobs = 0;
    
    double completion_percentage() const {
        return total_rooms > 0 ? static_cast<double>(completed_rooms) / total_rooms * 100.0 : 0.0;
    }
    
    size_t total_jobs() const {
        return dig_jobs + construction_jobs + furniture_jobs + smoothing_jobs;
    }
};

} // namespace planning
} // namespace df_ai
