#pragma once

#include "../utils/common/common.h"
#include "../debug/logging.h"

namespace dfai {
namespace core {

class AIController;

/**
 * @brief Manages the lifecycle of the AI system
 * 
 * The LifecycleManager handles initialization, shutdown, and state transitions
 * of the AI system, ensuring proper resource management and graceful handling
 * of system state changes.
 */
class LifecycleManager {
public:
    enum class State {
        UNINITIALIZED,
        INITIALIZING,
        RUNNING,
        PAUSED,
        SHUTTING_DOWN,
        SHUTDOWN,
        ERROR_STATE
    };

    enum class InitializationPhase {
        CONFIGURATION,
        DFHACK_INTEGRATION,
        SUBSYSTEMS,
        EVENT_HANDLERS,
        VALIDATION,
        COMPLETE
    };

    explicit LifecycleManager(AIController& controller);
    ~LifecycleManager();

    // Core lifecycle operations
    bool Initialize(color_ostream& out);
    void Shutdown();
    bool IsInitialized() const { return current_state_ == State::RUNNING; }
    
    // State management
    State GetCurrentState() const { return current_state_; }
    bool TransitionToState(State new_state, color_ostream& out);
    bool CanTransitionTo(State target_state) const;
    
    // Pause/Resume functionality
    bool Pause(color_ostream& out);
    bool Resume(color_ostream& out);
    bool IsPaused() const { return current_state_ == State::PAUSED; }
    
    // Emergency operations
    void EmergencyShutdown(const std::string& reason);
    bool AttemptRecovery(color_ostream& out);
    
    // Status and monitoring
    std::string GetStateDescription() const;
    std::string GetInitializationProgress() const;
    double GetInitializationProgress() const;
    
    // Event callbacks for state changes
    void RegisterStateChangeCallback(std::function<void(State, State)> callback);
    void UnregisterStateChangeCallbacks();

private:
    AIController& controller_;
    debug::Logger& logger_;
    
    State current_state_;
    State previous_state_;
    InitializationPhase current_phase_;
    
    std::chrono::steady_clock::time_point initialization_start_;
    std::chrono::steady_clock::time_point last_state_change_;
    
    std::vector<std::function<void(State, State)>> state_change_callbacks_;
    
    // Initialization phases
    bool InitializeConfiguration(color_ostream& out);
    bool InitializeDFHackIntegration(color_ostream& out);
    bool InitializeSubsystems(color_ostream& out);
    bool InitializeEventHandlers(color_ostream& out);
    bool ValidateInitialization(color_ostream& out);
    
    // Shutdown phases
    void ShutdownEventHandlers();
    void ShutdownSubsystems();
    void ShutdownDFHackIntegration();
    void CleanupResources();
    
    // State transition validation
    bool ValidateStateTransition(State from, State to) const;
    void OnStateChanged(State old_state, State new_state);
    void NotifyStateChangeCallbacks(State old_state, State new_state);
    
    // Error handling
    void HandleInitializationError(const std::string& phase, const std::string& error);
    void HandleRuntimeError(const std::string& error);
    
    // Utility methods
    std::string StateToString(State state) const;
    std::string PhaseToString(InitializationPhase phase) const;
    bool IsValidState(State state) const;
};

} // namespace core
} // namespace dfai
