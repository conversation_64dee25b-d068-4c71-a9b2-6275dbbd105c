{"$schema": "https://ben.lubar.me/df-ai-schemas/room-template.json", "f": [{"type": "door", "x": 1, "y": 3, "z": 0, "ignore": true}, {"x": 0, "y": 3, "z": 0}, {"x": 1, "y": 3, "z": 0}, {"x": 2, "y": 3, "z": 0}, {"x": 0, "y": 3, "z": 0}, {"x": 1, "y": 3, "z": 0}, {"x": 2, "y": 3, "z": 0}], "r": [{"min": [-1, -3, 0], "max": [1, -1, 0], "level": 1, "layout": [0], "accesspath": [3], "placeholder": 0}, {"min": [-1, -7, 0], "max": [1, -5, 0], "level": 2, "layout": [1, 2, 3], "accesspath": [4], "placeholder": 0}, {"min": [-1, -11, 0], "max": [1, -9, 0], "level": 3, "layout": [4, 5, 6], "accesspath": [5], "placeholder": 0}, {"type": "stockpile", "min": [-1, 1, 0], "max": [1, 1, 0], "workshop": 0, "in_corridor": true, "placeholder": 1}, {"type": "stockpile", "min": [-1, -4, 0], "max": [1, -4, 0], "workshop": 1, "accesspath": [0], "placeholder": 1}, {"type": "stockpile", "min": [-1, -8, 0], "max": [1, -8, 0], "workshop": 2, "accesspath": [1], "placeholder": 1}]}