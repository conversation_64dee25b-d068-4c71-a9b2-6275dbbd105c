#include "advanced_task_scheduler.h"
#include "../debug/logging.h"
#include <algorithm>
#include <chrono>

namespace dfai {
namespace scheduling {

AdvancedTaskScheduler::AdvancedTaskScheduler()
    : logger_(debug::Logger::GetInstance())
    , initialized_(false)
    , running_(false)
    , max_queue_size_(10000)
    , next_task_id_(1)
    , current_strategy_(SchedulingStrategy::PRIORITY_FIRST)
    , enable_dependency_checking_(true)
    , enable_deadline_enforcement_(true)
    , worker_thread_count_(std::thread::hardware_concurrency())
{
}

AdvancedTaskScheduler::~AdvancedTaskScheduler() {
    Shutdown();
}

bool AdvancedTaskScheduler::Initialize(color_ostream& out) {
    if (initialized_.load()) {
        return true;
    }
    
    try {
        // Initialize default resource pools
        CreateResourcePool("cpu", 100);
        CreateResourcePool("memory", 1024 * 1024 * 1024); // 1GB
        CreateResourcePool("disk_io", 100);
        CreateResourcePool("network", 100);
        
        // Start worker threads
        running_ = true;
        for (size_t i = 0; i < worker_thread_count_; ++i) {
            worker_threads_.emplace_back(&AdvancedTaskScheduler::WorkerThreadFunction, this, i);
        }
        
        // Start scheduler thread
        scheduler_thread_ = std::thread(&AdvancedTaskScheduler::SchedulerThreadFunction, this);
        
        initialized_ = true;
        logger_.Info("AdvancedTaskScheduler initialized with {} worker threads", worker_thread_count_);
        return true;
        
    } catch (const std::exception& e) {
        logger_.Error("Failed to initialize AdvancedTaskScheduler: {}", e.what());
        return false;
    }
}

void AdvancedTaskScheduler::Shutdown() {
    if (!initialized_.load()) {
        return;
    }
    
    running_ = false;
    
    // Wake up all threads
    task_condition_.notify_all();
    scheduler_condition_.notify_all();
    
    // Wait for scheduler thread
    if (scheduler_thread_.joinable()) {
        scheduler_thread_.join();
    }
    
    // Wait for worker threads
    for (auto& thread : worker_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    
    worker_threads_.clear();
    
    // Clear all data
    {
        std::lock_guard<std::mutex> lock(tasks_mutex_);
        task_queue_.clear();
        active_tasks_.clear();
        completed_tasks_.clear();
        failed_tasks_.clear();
    }
    
    {
        std::lock_guard<std::mutex> lock(resources_mutex_);
        resource_pools_.clear();
    }
    
    initialized_ = false;
    logger_.Info("AdvancedTaskScheduler shutdown completed");
}

int32_t AdvancedTaskScheduler::ScheduleTask(const TaskDefinition& task_def) {
    if (!initialized_.load()) {
        logger_.Error("Cannot schedule task: scheduler not initialized");
        return -1;
    }
    
    if (task_def.name.empty() || !task_def.execute_function) {
        logger_.Error("Cannot schedule task: invalid task definition");
        return -1;
    }
    
    std::lock_guard<std::mutex> lock(tasks_mutex_);
    
    if (task_queue_.size() >= max_queue_size_) {
        logger_.Warning("Task queue full, cannot schedule task: {}", task_def.name);
        statistics_.queue_overflows++;
        return -1;
    }
    
    // Create new task
    auto task = std::make_shared<ScheduledTask>();
    task->id = next_task_id_++;
    task->definition = task_def;
    task->status = TaskStatus::QUEUED;
    task->queued_at = std::chrono::steady_clock::now();
    task->retry_count = 0;
    
    // Validate dependencies
    if (enable_dependency_checking_ && !ValidateTaskDependencies(*task)) {
        logger_.Error("Cannot schedule task {}: dependency validation failed", task_def.name);
        return -1;
    }
    
    // Check resource requirements
    if (!CheckResourceRequirements(*task)) {
        logger_.Warning("Task {} scheduled but resources not available", task_def.name);
        task->status = TaskStatus::WAITING_FOR_RESOURCES;
    }
    
    task_queue_.push_back(task);
    
    // Wake up scheduler
    task_condition_.notify_one();
    
    statistics_.tasks_scheduled++;
    logger_.Debug("Scheduled task {} with ID {}", task_def.name, task->id);
    
    return task->id;
}

bool AdvancedTaskScheduler::CancelTask(int32_t task_id) {
    std::lock_guard<std::mutex> lock(tasks_mutex_);
    
    // Check queue
    auto queue_it = std::find_if(task_queue_.begin(), task_queue_.end(),
        [task_id](const std::shared_ptr<ScheduledTask>& task) {
            return task->id == task_id;
        });
    
    if (queue_it != task_queue_.end()) {
        (*queue_it)->status = TaskStatus::CANCELLED;
        task_queue_.erase(queue_it);
        statistics_.tasks_cancelled++;
        return true;
    }
    
    // Check active tasks
    auto active_it = std::find_if(active_tasks_.begin(), active_tasks_.end(),
        [task_id](const std::shared_ptr<ScheduledTask>& task) {
            return task->id == task_id;
        });
    
    if (active_it != active_tasks_.end()) {
        (*active_it)->status = TaskStatus::CANCELLED;
        (*active_it)->should_cancel = true;
        return true;
    }
    
    return false;
}

TaskStatus AdvancedTaskScheduler::GetTaskStatus(int32_t task_id) const {
    std::lock_guard<std::mutex> lock(tasks_mutex_);
    
    // Check all task containers
    auto check_container = [task_id](const auto& container) -> TaskStatus {
        auto it = std::find_if(container.begin(), container.end(),
            [task_id](const std::shared_ptr<ScheduledTask>& task) {
                return task->id == task_id;
            });
        return (it != container.end()) ? (*it)->status : TaskStatus::UNKNOWN;
    };
    
    TaskStatus status = check_container(task_queue_);
    if (status != TaskStatus::UNKNOWN) return status;
    
    status = check_container(active_tasks_);
    if (status != TaskStatus::UNKNOWN) return status;
    
    status = check_container(completed_tasks_);
    if (status != TaskStatus::UNKNOWN) return status;
    
    status = check_container(failed_tasks_);
    if (status != TaskStatus::UNKNOWN) return status;
    
    return TaskStatus::UNKNOWN;
}

void AdvancedTaskScheduler::SetSchedulingStrategy(SchedulingStrategy strategy) {
    current_strategy_ = strategy;
    scheduler_condition_.notify_one(); // Trigger re-sorting
}

void AdvancedTaskScheduler::CreateResourcePool(const std::string& name, int32_t capacity) {
    std::lock_guard<std::mutex> lock(resources_mutex_);
    resource_pools_[name] = std::make_shared<ResourcePool>(name, capacity);
}

void AdvancedTaskScheduler::WorkerThreadFunction(size_t worker_id) {
    logger_.Debug("Worker thread {} started", worker_id);
    
    while (running_.load()) {
        std::shared_ptr<ScheduledTask> task;
        
        // Get next task
        {
            std::unique_lock<std::mutex> lock(tasks_mutex_);
            task_condition_.wait(lock, [this] {
                return !running_.load() || !ready_tasks_.empty();
            });
            
            if (!running_.load()) {
                break;
            }
            
            if (!ready_tasks_.empty()) {
                task = ready_tasks_.front();
                ready_tasks_.pop_front();
                active_tasks_.push_back(task);
            }
        }
        
        if (task) {
            ExecuteTask(task, worker_id);
        }
    }
    
    logger_.Debug("Worker thread {} stopped", worker_id);
}

void AdvancedTaskScheduler::SchedulerThreadFunction() {
    logger_.Debug("Scheduler thread started");
    
    while (running_.load()) {
        {
            std::unique_lock<std::mutex> lock(scheduler_mutex_);
            scheduler_condition_.wait_for(lock, std::chrono::milliseconds(100));
        }
        
        if (!running_.load()) {
            break;
        }
        
        ProcessScheduling();
        CleanupCompletedTasks();
        UpdateStatistics();
    }
    
    logger_.Debug("Scheduler thread stopped");
}

void AdvancedTaskScheduler::ProcessScheduling() {
    std::lock_guard<std::mutex> lock(tasks_mutex_);
    
    if (task_queue_.empty()) {
        return;
    }
    
    // Sort tasks based on current strategy
    SortTaskQueue();
    
    // Move ready tasks to ready queue
    auto current_time = std::chrono::steady_clock::now();
    
    for (auto it = task_queue_.begin(); it != task_queue_.end();) {
        auto& task = *it;
        
        if (task->status == TaskStatus::CANCELLED) {
            it = task_queue_.erase(it);
            continue;
        }
        
        // Check if task is ready to execute
        if (IsTaskReady(*task, current_time)) {
            task->status = TaskStatus::READY;
            ready_tasks_.push_back(task);
            it = task_queue_.erase(it);
            task_condition_.notify_one();
        } else {
            ++it;
        }
    }
}

void AdvancedTaskScheduler::SortTaskQueue() {
    switch (current_strategy_) {
        case SchedulingStrategy::PRIORITY_FIRST:
            std::sort(task_queue_.begin(), task_queue_.end(),
                [](const std::shared_ptr<ScheduledTask>& a, const std::shared_ptr<ScheduledTask>& b) {
                    return a->definition.priority > b->definition.priority;
                });
            break;
            
        case SchedulingStrategy::DEADLINE_FIRST:
            std::sort(task_queue_.begin(), task_queue_.end(),
                [](const std::shared_ptr<ScheduledTask>& a, const std::shared_ptr<ScheduledTask>& b) {
                    return a->definition.deadline < b->definition.deadline;
                });
            break;
            
        case SchedulingStrategy::SHORTEST_JOB_FIRST:
            std::sort(task_queue_.begin(), task_queue_.end(),
                [](const std::shared_ptr<ScheduledTask>& a, const std::shared_ptr<ScheduledTask>& b) {
                    return a->definition.estimated_duration < b->definition.estimated_duration;
                });
            break;
            
        default:
            break;
    }
}

bool AdvancedTaskScheduler::IsTaskReady(const ScheduledTask& task, std::chrono::steady_clock::time_point current_time) const {
    // Check if task should be delayed
    if (task.execute_after > current_time) {
        return false;
    }
    
    // Check dependencies
    if (enable_dependency_checking_ && !AreDependenciesSatisfied(task)) {
        return false;
    }
    
    // Check resource availability
    if (!CheckResourceRequirements(task)) {
        return false;
    }
    
    return true;
}

bool AdvancedTaskScheduler::ValidateTaskDependencies(const ScheduledTask& task) const {
    for (int32_t dependency_id : task.definition.dependencies) {
        TaskStatus dep_status = GetTaskStatus(dependency_id);
        if (dep_status != TaskStatus::COMPLETED && dep_status != TaskStatus::UNKNOWN) {
            return false;
        }
    }
    return true;
}

bool AdvancedTaskScheduler::AreDependenciesSatisfied(const ScheduledTask& task) const {
    return ValidateTaskDependencies(task);
}

bool AdvancedTaskScheduler::CheckResourceRequirements(const ScheduledTask& task) const {
    std::lock_guard<std::mutex> lock(resources_mutex_);
    
    for (const auto& requirement : task.definition.resource_requirements) {
        auto pool_it = resource_pools_.find(requirement.first);
        if (pool_it == resource_pools_.end()) {
            return false;
        }
        
        if (!pool_it->second->CanAllocate(requirement.second)) {
            return false;
        }
    }
    
    return true;
}

void AdvancedTaskScheduler::ExecuteTask(std::shared_ptr<ScheduledTask> task, size_t worker_id) {
    task->status = TaskStatus::RUNNING;
    task->started_at = std::chrono::steady_clock::now();
    task->worker_id = worker_id;
    
    logger_.Debug("Executing task {} on worker {}", task->definition.name, worker_id);
    
    try {
        // Allocate resources
        AllocateTaskResources(*task);
        
        // Execute the task
        bool success = task->definition.execute_function();
        
        // Release resources
        ReleaseTaskResources(*task);
        
        // Update task status
        task->completed_at = std::chrono::steady_clock::now();
        
        if (success && !task->should_cancel) {
            task->status = TaskStatus::COMPLETED;
            
            std::lock_guard<std::mutex> lock(tasks_mutex_);
            auto it = std::find(active_tasks_.begin(), active_tasks_.end(), task);
            if (it != active_tasks_.end()) {
                active_tasks_.erase(it);
                completed_tasks_.push_back(task);
            }
            
            statistics_.tasks_completed++;
            logger_.Debug("Task {} completed successfully", task->definition.name);
            
        } else {
            HandleTaskFailure(task, "Task execution returned false or was cancelled");
        }
        
    } catch (const std::exception& e) {
        ReleaseTaskResources(*task);
        HandleTaskFailure(task, "Exception during execution: " + std::string(e.what()));
        
    } catch (...) {
        ReleaseTaskResources(*task);
        HandleTaskFailure(task, "Unknown exception during execution");
    }
}

void AdvancedTaskScheduler::HandleTaskFailure(std::shared_ptr<ScheduledTask> task, const std::string& error_message) {
    task->status = TaskStatus::FAILED;
    task->error_message = error_message;
    task->completed_at = std::chrono::steady_clock::now();
    
    logger_.Warning("Task {} failed: {}", task->definition.name, error_message);
    
    // Check if task should be retried
    if (task->retry_count < task->definition.max_retries) {
        task->retry_count++;
        task->status = TaskStatus::QUEUED;
        task->execute_after = std::chrono::steady_clock::now() + task->definition.retry_delay;
        
        std::lock_guard<std::mutex> lock(tasks_mutex_);
        auto it = std::find(active_tasks_.begin(), active_tasks_.end(), task);
        if (it != active_tasks_.end()) {
            active_tasks_.erase(it);
            task_queue_.push_back(task);
        }
        
        statistics_.tasks_retried++;
        logger_.Debug("Task {} scheduled for retry ({}/{})", task->definition.name, task->retry_count, task->definition.max_retries);
        
    } else {
        std::lock_guard<std::mutex> lock(tasks_mutex_);
        auto it = std::find(active_tasks_.begin(), active_tasks_.end(), task);
        if (it != active_tasks_.end()) {
            active_tasks_.erase(it);
            failed_tasks_.push_back(task);
        }
        
        statistics_.tasks_failed++;
    }
}

void AdvancedTaskScheduler::AllocateTaskResources(ScheduledTask& task) {
    std::lock_guard<std::mutex> lock(resources_mutex_);
    
    for (const auto& requirement : task.definition.resource_requirements) {
        auto pool_it = resource_pools_.find(requirement.first);
        if (pool_it != resource_pools_.end()) {
            if (pool_it->second->Allocate(task.id, requirement.second)) {
                task.allocated_resources[requirement.first] = requirement.second;
            }
        }
    }
}

void AdvancedTaskScheduler::ReleaseTaskResources(ScheduledTask& task) {
    std::lock_guard<std::mutex> lock(resources_mutex_);
    
    for (const auto& allocation : task.allocated_resources) {
        auto pool_it = resource_pools_.find(allocation.first);
        if (pool_it != resource_pools_.end()) {
            pool_it->second->Release(task.id);
        }
    }
    
    task.allocated_resources.clear();
}

void AdvancedTaskScheduler::CleanupCompletedTasks() {
    std::lock_guard<std::mutex> lock(tasks_mutex_);
    
    auto cleanup_container = [](auto& container, size_t max_size) {
        if (container.size() > max_size) {
            container.erase(container.begin(), container.begin() + (container.size() - max_size));
        }
    };
    
    cleanup_container(completed_tasks_, 1000);
    cleanup_container(failed_tasks_, 1000);
}

void AdvancedTaskScheduler::UpdateStatistics() {
    auto current_time = std::chrono::steady_clock::now();
    statistics_.last_update = current_time;
    
    std::lock_guard<std::mutex> lock(tasks_mutex_);
    statistics_.queued_tasks = task_queue_.size();
    statistics_.active_tasks = active_tasks_.size();
    statistics_.ready_tasks = ready_tasks_.size();
}

AdvancedTaskScheduler::SchedulingStatistics AdvancedTaskScheduler::GetStatistics() const {
    return statistics_;
}

} // namespace scheduling
} // namespace dfai
