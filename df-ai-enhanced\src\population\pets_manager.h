#pragma once

#include "../utils/common/common.h"
#include "../debug/logging.h"
#include "population_types.h"

#include <vector>
#include <map>
#include <set>
#include <queue>

// Forward declarations
class color_ostream;
namespace df {
    struct unit;
    struct building_civzonest;
}

namespace dfai {
namespace population {

/**
 * Manages pets, livestock, and animal-related tasks
 */
class PetsManager {
public:
    PetsManager();
    ~PetsManager();
    
    // Core lifecycle
    bool Initialize(color_ostream& out);
    void Shutdown();
    void Update(color_ostream& out);
    
    // Pet management
    void UpdatePets(color_ostream& out);
    void AssignPetToZone(color_ostream& out, df::unit* pet, df::building_civzonest* zone);
    void HandleStrayPets(color_ostream& out);
    void ProcessPetQueue(color_ostream& out);
    
    // Queries
    bool IsPet(int32_t unit_id) const;
    PetFlags GetPetFlags(int32_t unit_id) const;
    std::vector<int32_t> GetPets() const;
    std::vector<int32_t> GetStrayPets() const;
    
    // Livestock management
    void ManageLivestock(color_ostream& out);
    void AssignAnimalToJob(color_ostream& out, df::unit* animal, const std::string& job_type);
    
    // Status reporting
    std::string GetStatusReport() const;

private:
    debug::Logger& logger_;
    bool initialized_;
    
    // Pet tracking
    std::map<int32_t, PetFlags> pets_;
    std::set<int32_t> stray_pets_;
    std::queue<int32_t> pet_assignment_queue_;
    
    // Update tracking
    size_t update_counter_;
    
    // Helper methods
    void ScanForNewPets(color_ostream& out);
    void UpdatePetStatus(color_ostream& out, int32_t unit_id);
    void CleanupDeadPets();
    bool ShouldManagePet(df::unit* unit) const;
    df::building_civzonest* FindSuitableZone(df::unit* pet) const;
};

} // namespace population
} // namespace dfai
