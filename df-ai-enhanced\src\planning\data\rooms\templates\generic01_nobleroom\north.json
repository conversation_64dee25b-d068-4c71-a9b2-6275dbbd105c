{"$schema": "https://ben.lubar.me/df-ai-schemas/room-template.json", "f": [{"x": -1, "y": 4, "z": 0, "type": "door", "internal": true}, {"x": 2, "y": 2, "z": 0, "type": "coffin", "makeroom": true}, {"x": 6, "y": 1, "z": 0, "type": "bed", "construction": "Floor", "makeroom": true}, {"x": 6, "y": 0, "z": 0, "construction": "Floor"}, {"x": 6, "y": 2, "z": 0, "construction": "Floor"}, {"x": 5, "y": 3, "z": 0, "construction": "Wall", "dig": "No"}, {"x": 5, "y": 4, "z": 0, "type": "door", "internal": true}, {"x": 2, "y": 5, "z": 0, "type": "door", "internal": true}, {"x": -1, "y": 2, "z": 0, "type": "door", "internal": true}, {"x": 5, "y": 2, "z": 0, "type": "door", "internal": true}, {"x": 0, "y": 2, "z": 0, "type": "door"}, {"x": 2, "y": 1, "z": 0, "type": "chair", "makeroom": true}, {"x": 2, "y": 2, "z": 0, "type": "table"}, {"x": 2, "y": 2, "z": 0, "type": "table", "makeroom": true}, {"x": 2, "y": 1, "z": 0, "type": "chair"}, {"x": 0, "y": 0, "z": 0, "type": "weapon_rack", "ignore": true}, {"x": 4, "y": 0, "z": 0, "type": "weapon_rack", "ignore": true}, {"x": 0, "y": 1, "z": 0, "type": "armor_stand", "ignore": true}, {"x": 4, "y": 1, "z": 0, "type": "armor_stand", "ignore": true}, {"x": 0, "y": 3, "z": 0, "type": "chest", "ignore": true}, {"x": 0, "y": 1, "z": 0, "type": "cabinet", "ignore": true}, {"x": 0, "y": 2, "z": 0, "type": "chest", "ignore": true}, {"x": 0, "y": 3, "z": 0, "type": "cabinet", "ignore": true}], "r": [{"min": [0, -2, 0], "max": [0, -1, 0], "type": "corridor", "corridor_type": "corridor", "layout": [10]}, {"min": [0, -6, 0], "max": [0, -3, 0], "type": "corridor", "corridor_type": "corridor", "accesspath": [0]}, {"min": [-6, -4, 0], "max": [-2, -1, 0], "type": "nobleroom", "nobleroom_type": "office", "noblesuite": 0, "layout": [9, 11, 12, 15, 16, 17, 18, 19], "accesspath": [0], "require_stone": true}, {"min": [-6, -10, 0], "max": [-1, -6, 0], "type": "nobleroom", "nobleroom_type": "bedroom", "noblesuite": 0, "layout": [2, 3, 4, 5, 6, 7, 20, 21, 22], "accesspath": [1, 2], "require_stone": true}, {"min": [2, -4, 0], "max": [6, -1, 0], "type": "nobleroom", "nobleroom_type": "dining", "noblesuite": 0, "layout": [8, 13, 14], "accesspath": [0], "require_stone": true}, {"min": [2, -10, 0], "max": [6, -6, 0], "type": "nobleroom", "nobleroom_type": "tomb", "noblesuite": 0, "layout": [0, 1], "accesspath": [1], "require_stone": true}]}