#pragma once

#include "event_types.h"
#include "../debug/logging.h"
#include "../utils/common/common.h"
#include <functional>
#include <vector>
#include <map>
#include <mutex>
#include <memory>

namespace dfai {
namespace events {

/**
 * @brief Manages DFHack hooks and their integration with the event system
 * 
 * The HooksManager handles registration and management of DFHack hooks,
 * converting them into events that can be processed by the event system.
 */
class HooksManager {
public:
    enum class HookType {
        ON_UPDATE,
        ON_STATE_CHANGE,
        ON_UNIT_NEW,
        ON_UNIT_DEATH,
        ON_JOB_INITIATED,
        ON_JOB_COMPLETED,
        ON_BUILDING_CREATED,
        ON_BUILDING_DESTROYED,
        ON_ITEM_CREATED,
        ON_ANNOUNCEMENT,
        ON_PAUSE,
        ON_UNPAUSE,
        CUSTOM
    };

    struct HookInfo {
        std::string id;
        HookType type;
        std::string description;
        bool enabled;
        std::chrono::steady_clock::time_point registered_time;
        uint64_t trigger_count;
        std::chrono::milliseconds total_processing_time;
        
        HookInfo(const std::string& hook_id, HookType hook_type, const std::string& desc)
            : id(hook_id), type(hook_type), description(desc), enabled(true)
            , registered_time(std::chrono::steady_clock::now())
            , trigger_count(0), total_processing_time(0) {}
    };

    HooksManager();
    ~HooksManager();

    // Lifecycle management
    bool Initialize();
    void Shutdown();
    bool IsInitialized() const { return initialized_; }

    // Hook registration
    std::string RegisterHook(HookType type, const std::string& description = "");
    bool UnregisterHook(const std::string& hook_id);
    bool UnregisterHooksByType(HookType type);
    void UnregisterAllHooks();

    // Hook management
    bool EnableHook(const std::string& hook_id);
    bool DisableHook(const std::string& hook_id);
    bool IsHookRegistered(const std::string& hook_id) const;
    bool IsHookEnabled(const std::string& hook_id) const;

    // Event generation
    void SetEventManager(class EventManager* event_manager) { event_manager_ = event_manager; }
    void TriggerHook(HookType type, const Json::Value& data = Json::Value());
    void TriggerHook(const std::string& hook_id, const Json::Value& data = Json::Value());

    // Information and statistics
    std::vector<HookInfo> GetHooksForType(HookType type) const;
    std::vector<HookInfo> GetAllHooks() const;
    HookInfo GetHookInfo(const std::string& hook_id) const;
    size_t GetHookCount() const;
    size_t GetHookCount(HookType type) const;

    // Performance monitoring
    std::chrono::milliseconds GetAverageProcessingTime(const std::string& hook_id) const;
    std::chrono::milliseconds GetTotalProcessingTime(HookType type) const;
    uint64_t GetTotalTriggerCount(HookType type) const;

    // DFHack integration
    bool SetupDFHackHooks();
    void CleanupDFHackHooks();
    bool IsDFHackHookActive(HookType type) const;

    // Debugging and diagnostics
    void LogHookStatistics() const;
    std::string GetStatusReport() const;
    void DumpHookInfo(color_ostream& out) const;

    // Hook type utilities
    static std::string HookTypeToString(HookType type);
    static HookType StringToHookType(const std::string& str);
    static EventType HookTypeToEventType(HookType type);

private:
    mutable std::mutex hooks_mutex_;
    std::map<std::string, std::unique_ptr<HookInfo>> hooks_;
    std::map<HookType, std::vector<std::string>> hooks_by_type_;
    
    bool initialized_;
    debug::Logger& logger_;
    class EventManager* event_manager_;
    
    // DFHack hook handles
    std::map<HookType, void*> dfhack_hooks_;
    
    // Internal utilities
    std::string GenerateHookId();
    void ProcessHookTrigger(const HookInfo& hook, const Json::Value& data);
    void UpdateTriggerStatistics(HookInfo& hook, std::chrono::milliseconds processing_time);
    
    // Thread safety helpers
    void AddHookToTypeMap(const std::string& hook_id, HookType type);
    void RemoveHookFromTypeMap(const std::string& hook_id, HookType type);
    
    // DFHack hook callbacks
    static void OnUpdateHook(color_ostream& out);
    static void OnStateChangeHook(color_ostream& out, int32_t state);
    static void OnUnitNewHook(color_ostream& out, int32_t unit_id);
    static void OnUnitDeathHook(color_ostream& out, int32_t unit_id);
    static void OnJobInitiatedHook(color_ostream& out, int32_t job_id);
    static void OnJobCompletedHook(color_ostream& out, int32_t job_id);
    static void OnBuildingCreatedHook(color_ostream& out, int32_t building_id);
    static void OnBuildingDestroyedHook(color_ostream& out, int32_t building_id);
    static void OnItemCreatedHook(color_ostream& out, int32_t item_id);
    static void OnAnnouncementHook(color_ostream& out, int32_t announcement_id);
    static void OnPauseHook(color_ostream& out);
    static void OnUnpauseHook(color_ostream& out);
    
    // Hook setup helpers
    bool SetupUpdateHook();
    bool SetupStateChangeHook();
    bool SetupUnitHooks();
    bool SetupJobHooks();
    bool SetupBuildingHooks();
    bool SetupItemHooks();
    bool SetupAnnouncementHook();
    bool SetupPauseHooks();
    
    // Hook cleanup helpers
    void CleanupUpdateHook();
    void CleanupStateChangeHook();
    void CleanupUnitHooks();
    void CleanupJobHooks();
    void CleanupBuildingHooks();
    void CleanupItemHooks();
    void CleanupAnnouncementHook();
    void CleanupPauseHooks();
    
    // Validation
    bool ValidateHookType(HookType type) const;
    bool ValidateHookId(const std::string& hook_id) const;
    
    // Static instance for callbacks
    static HooksManager* instance_;
};

/**
 * @brief RAII wrapper for hook registration
 * 
 * Automatically unregisters the hook when the object is destroyed.
 */
class ScopedHook {
public:
    ScopedHook(HooksManager& manager, HooksManager::HookType type, 
              const std::string& description = "");
    ~ScopedHook();
    
    // Non-copyable, movable
    ScopedHook(const ScopedHook&) = delete;
    ScopedHook& operator=(const ScopedHook&) = delete;
    ScopedHook(ScopedHook&& other) noexcept;
    ScopedHook& operator=(ScopedHook&& other) noexcept;
    
    // Hook management
    bool IsValid() const { return !hook_id_.empty(); }
    const std::string& GetHookId() const { return hook_id_; }
    bool Enable();
    bool Disable();
    
    // Manual control
    void Release(); // Releases ownership without unregistering
    void Unregister(); // Manually unregister

private:
    HooksManager* manager_;
    std::string hook_id_;
};

/**
 * @brief Hook trigger context
 * 
 * Provides context information when a hook is triggered.
 */
class HookContext {
public:
    HookContext(HooksManager::HookType type, const Json::Value& data);
    
    // Hook information
    HooksManager::HookType GetHookType() const { return hook_type_; }
    const Json::Value& GetData() const { return data_; }
    
    // Timing information
    std::chrono::steady_clock::time_point GetTriggerTime() const { return trigger_time_; }
    std::chrono::milliseconds GetElapsedTime() const;
    
    // Data access helpers
    int32_t GetIntData(const std::string& key, int32_t default_value = 0) const;
    std::string GetStringData(const std::string& key, const std::string& default_value = "") const;
    bool GetBoolData(const std::string& key, bool default_value = false) const;

private:
    HooksManager::HookType hook_type_;
    Json::Value data_;
    std::chrono::steady_clock::time_point trigger_time_;
};

} // namespace events
} // namespace dfai
