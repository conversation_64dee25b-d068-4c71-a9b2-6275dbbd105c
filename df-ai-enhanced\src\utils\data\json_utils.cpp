#include "json_utils.h"
#include "../common/types.h"
#include <fstream>
#include <sstream>

namespace dfai {
namespace utils {
namespace data {

// JSON loading and saving
Json::Value LoadJsonFromFile(const std::string& filename, std::string& error) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        error = "Could not open file: " + filename;
        return Json::Value();
    }
    
    Json::Value root;
    Json::CharReaderBuilder builder;
    std::string parse_errors;
    
    if (!Json::parseFromStream(builder, file, &root, &parse_errors)) {
        error = "JSON parse error in " + filename + ": " + parse_errors;
        return Json::Value();
    }
    
    return root;
}

bool SaveJsonToFile(const Json::Value& json, const std::string& filename, std::string& error) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        error = "Could not open file for writing: " + filename;
        return false;
    }
    
    Json::StreamWriterBuilder builder;
    builder["indentation"] = "  ";
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
    
    writer->write(json, &file);
    return true;
}

Json::Value ParseJsonString(const std::string& json_string, std::string& error) {
    Json::Value root;
    Json::CharReaderBuilder builder;
    std::string parse_errors;
    
    std::istringstream stream(json_string);
    if (!Json::parseFromStream(builder, stream, &root, &parse_errors)) {
        error = "JSON parse error: " + parse_errors;
        return Json::Value();
    }
    
    return root;
}

std::string JsonToString(const Json::Value& json, bool pretty) {
    if (pretty) {
        Json::StreamWriterBuilder builder;
        builder["indentation"] = "  ";
        return Json::writeString(builder, json);
    } else {
        Json::StreamWriterBuilder builder;
        builder["indentation"] = "";
        return Json::writeString(builder, json);
    }
}

// JSON validation
bool ValidateJsonSchema(const Json::Value& json, const Json::Value& schema, std::string& error) {
    // Basic schema validation - this is a simplified implementation
    // A full JSON schema validator would be more complex
    
    if (schema.isMember("type")) {
        std::string expected_type = schema["type"].asString();
        
        if (expected_type == "object" && !json.isObject()) {
            error = "Expected object, got " + GetJsonTypeName(json);
            return false;
        } else if (expected_type == "array" && !json.isArray()) {
            error = "Expected array, got " + GetJsonTypeName(json);
            return false;
        } else if (expected_type == "string" && !json.isString()) {
            error = "Expected string, got " + GetJsonTypeName(json);
            return false;
        } else if (expected_type == "number" && !json.isNumeric()) {
            error = "Expected number, got " + GetJsonTypeName(json);
            return false;
        } else if (expected_type == "boolean" && !json.isBool()) {
            error = "Expected boolean, got " + GetJsonTypeName(json);
            return false;
        }
    }
    
    if (schema.isMember("required") && json.isObject()) {
        const Json::Value& required = schema["required"];
        if (required.isArray()) {
            for (const Json::Value& field : required) {
                if (field.isString() && !json.isMember(field.asString())) {
                    error = "Missing required field: " + field.asString();
                    return false;
                }
            }
        }
    }
    
    return true;
}

bool IsValidJson(const std::string& json_string) {
    std::string error;
    ParseJsonString(json_string, error);
    return error.empty();
}

std::string GetJsonTypeName(const Json::Value& value) {
    if (value.isNull()) return "null";
    if (value.isBool()) return "boolean";
    if (value.isInt()) return "integer";
    if (value.isDouble()) return "number";
    if (value.isString()) return "string";
    if (value.isArray()) return "array";
    if (value.isObject()) return "object";
    return "unknown";
}

// JSON path operations
Json::Value GetJsonValue(const Json::Value& root, const std::string& path, const Json::Value& default_value) {
    if (path.empty()) {
        return root;
    }
    
    std::vector<std::string> parts = SplitJsonPath(path);
    const Json::Value* current = &root;
    
    for (const std::string& part : parts) {
        if (current->isObject() && current->isMember(part)) {
            current = &(*current)[part];
        } else if (current->isArray()) {
            try {
                int index = std::stoi(part);
                if (index >= 0 && index < static_cast<int>(current->size())) {
                    current = &(*current)[index];
                } else {
                    return default_value;
                }
            } catch (const std::exception&) {
                return default_value;
            }
        } else {
            return default_value;
        }
    }
    
    return *current;
}

bool SetJsonValue(Json::Value& root, const std::string& path, const Json::Value& value) {
    if (path.empty()) {
        root = value;
        return true;
    }
    
    std::vector<std::string> parts = SplitJsonPath(path);
    Json::Value* current = &root;
    
    for (size_t i = 0; i < parts.size() - 1; ++i) {
        const std::string& part = parts[i];
        
        if (!current->isObject()) {
            *current = Json::Value(Json::objectValue);
        }
        
        if (!current->isMember(part)) {
            (*current)[part] = Json::Value(Json::objectValue);
        }
        
        current = &(*current)[part];
    }
    
    const std::string& last_part = parts.back();
    if (!current->isObject()) {
        *current = Json::Value(Json::objectValue);
    }
    
    (*current)[last_part] = value;
    return true;
}

bool HasJsonPath(const Json::Value& root, const std::string& path) {
    Json::Value result = GetJsonValue(root, path, Json::Value());
    return !result.isNull();
}

std::vector<std::string> SplitJsonPath(const std::string& path) {
    std::vector<std::string> parts;
    std::string current_part;
    
    for (char c : path) {
        if (c == '.' || c == '/') {
            if (!current_part.empty()) {
                parts.push_back(current_part);
                current_part.clear();
            }
        } else {
            current_part += c;
        }
    }
    
    if (!current_part.empty()) {
        parts.push_back(current_part);
    }
    
    return parts;
}

// JSON conversion utilities
Json::Value CoordinateToJson(const Coordinate& coord) {
    Json::Value json(Json::objectValue);
    json["x"] = coord.x;
    json["y"] = coord.y;
    json["z"] = coord.z;
    return json;
}

Coordinate JsonToCoordinate(const Json::Value& json, const Coordinate& default_value) {
    if (!json.isObject()) {
        return default_value;
    }
    
    Coordinate result = default_value;
    
    if (json.isMember("x") && json["x"].isInt()) {
        result.x = json["x"].asInt();
    }
    if (json.isMember("y") && json["y"].isInt()) {
        result.y = json["y"].asInt();
    }
    if (json.isMember("z") && json["z"].isInt()) {
        result.z = json["z"].asInt();
    }
    
    return result;
}

Json::Value RectangleToJson(const Rectangle& rect) {
    Json::Value json(Json::objectValue);
    json["min"] = CoordinateToJson(rect.min);
    json["max"] = CoordinateToJson(rect.max);
    return json;
}

Rectangle JsonToRectangle(const Json::Value& json, const Rectangle& default_value) {
    if (!json.isObject()) {
        return default_value;
    }
    
    Rectangle result = default_value;
    
    if (json.isMember("min")) {
        result.min = JsonToCoordinate(json["min"], result.min);
    }
    if (json.isMember("max")) {
        result.max = JsonToCoordinate(json["max"], result.max);
    }
    
    return result;
}

Json::Value StringVectorToJson(const std::vector<std::string>& strings) {
    Json::Value json(Json::arrayValue);
    for (const std::string& str : strings) {
        json.append(str);
    }
    return json;
}

std::vector<std::string> JsonToStringVector(const Json::Value& json) {
    std::vector<std::string> result;
    
    if (json.isArray()) {
        for (const Json::Value& item : json) {
            if (item.isString()) {
                result.push_back(item.asString());
            }
        }
    }
    
    return result;
}

Json::Value IntVectorToJson(const std::vector<int>& integers) {
    Json::Value json(Json::arrayValue);
    for (int value : integers) {
        json.append(value);
    }
    return json;
}

std::vector<int> JsonToIntVector(const Json::Value& json) {
    std::vector<int> result;
    
    if (json.isArray()) {
        for (const Json::Value& item : json) {
            if (item.isInt()) {
                result.push_back(item.asInt());
            }
        }
    }
    
    return result;
}

// JSON merging and manipulation
Json::Value MergeJson(const Json::Value& base, const Json::Value& overlay) {
    if (!base.isObject() || !overlay.isObject()) {
        return overlay;
    }
    
    Json::Value result = base;
    
    for (const std::string& key : overlay.getMemberNames()) {
        if (result.isMember(key) && result[key].isObject() && overlay[key].isObject()) {
            result[key] = MergeJson(result[key], overlay[key]);
        } else {
            result[key] = overlay[key];
        }
    }
    
    return result;
}

Json::Value FilterJsonKeys(const Json::Value& json, const std::vector<std::string>& allowed_keys) {
    if (!json.isObject()) {
        return json;
    }
    
    Json::Value result(Json::objectValue);
    
    for (const std::string& key : allowed_keys) {
        if (json.isMember(key)) {
            result[key] = json[key];
        }
    }
    
    return result;
}

Json::Value RemoveJsonKeys(const Json::Value& json, const std::vector<std::string>& keys_to_remove) {
    if (!json.isObject()) {
        return json;
    }
    
    Json::Value result = json;
    
    for (const std::string& key : keys_to_remove) {
        result.removeMember(key);
    }
    
    return result;
}

} // namespace data
} // namespace utils
} // namespace dfai
