{"$schema": "http://json-schema.org/draft-04/schema", "id": "https://ben.lubar.me/df-ai-schemas/enums.json", "room_status": {"id": "#room_status", "enum": ["plan", "dig", "dug", "finished"]}, "room_type": {"id": "#room_type", "enum": ["corridor", "barracks", "bedroom", "cemetery", "cistern", "dininghall", "farmplot", "furnace", "garbagedump", "infirmary", "jail", "location", "nobleroom", "outpost", "pasture", "pitcage", "pond", "releasecage", "stockpile", "tradedepot", "windmill", "workshop"]}, "corridor_type": {"id": "#corridor_type", "enum": ["corridor", "veinshaft", "aqueduct", "outpost", "walkable"]}, "farm_type": {"id": "#farm_type", "enum": ["food", "cloth"]}, "stockpile_type": {"id": "#stockpile_type", "enum": ["food", "furniture", "wood", "stone", "refuse", "animals", "corpses", "gems", "finished_goods", "cloth", "bars_blocks", "leather", "ammo", "armor", "weapons", "coins", "sheets", "fresh_raw_hide"]}, "nobleroom_type": {"id": "#nobleroom_type", "enum": ["tomb", "dining", "bedroom", "office"]}, "outpost_type": {"id": "#outpost_type", "enum": ["cavern", "mining"]}, "location_type": {"id": "#location_type", "enum": ["guildhall", "library", "tavern", "temple"]}, "cistern_type": {"id": "#cistern_type", "enum": ["well", "reserve"]}, "layout_type": {"id": "#layout_type", "enum": ["none", "archery_target", "armor_stand", "bed", "bookcase", "cabinet", "cage", "cage_trap", "chair", "chest", "coffin", "door", "floodgate", "gear_assembly", "hatch", "hive", "lever", "nest_box", "offering_place", "pedestal", "restraint", "roller", "statue", "table", "track_stop", "traction_bench", "vertical_axle", "weapon_rack", "well"]}, "task_type": {"id": "#task_type", "enum": ["check_construct", "check_furnish", "check_idle", "check_rooms", "construct_activityzone", "construct_farmplot", "construct_furnace", "construct_stockpile", "construct_tradedepot", "construct_windmill", "construct_workshop", "dig_cistern", "dig_garbage", "dig_room", "dig_room_immediate", "furnish", "monitor_cistern", "monitor_farm_irrigation", "setup_farmplot", "want_dig"]}, "construction_type": {"id": "#construction_type", "enum": ["NONE", "Fortification", "Wall", "Floor", "UpStair", "DownStair", "UpDownStair", "<PERSON><PERSON>", "TrackN", "TrackS", "TrackE", "TrackW", "TrackNS", "TrackNE", "TrackNW", "TrackSE", "TrackSW", "TrackEW", "TrackNSE", "TrackNSW", "TrackNEW", "TrackSEW", "TrackNSEW", "TrackRampN", "TrackRampS", "TrackRampE", "TrackRampW", "TrackRampNS", "TrackRampNE", "TrackRampNW", "TrackRampSE", "TrackRampSW", "TrackRampEW", "TrackRampNSE", "TrackRampNSW", "TrackRampNEW", "TrackRampSEW", "TrackRampNSEW"]}, "workshop_type": {"id": "#workshop_type", "enum": ["<PERSON><PERSON>", "Farmers", "<PERSON>s", "Craftsdwarfs", "Jewelers", "MetalsmithsForge", "MagmaForge", "<PERSON><PERSON>", "Mechanics", "Siege", "Butchers", "Leatherworks", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Fishery", "Still", "Loom", "Quern", "Kennels", "Kitchen", "<PERSON><PERSON>", "<PERSON><PERSON>", "Millstone", "Custom", "Tool"]}, "furnace_type": {"id": "#furnace_type", "enum": ["Wood<PERSON>urnace", "Smelter", "GlassFurnace", "Kiln", "MagmaSmelter", "MagmaGlassFurnace", "MagmaKiln", "Custom"]}, "stockpile_list": {"id": "#stockpile_list", "enum": ["FoodMeat", "FoodFish", "FoodUnpreparedFish", "FoodEgg", "FoodPlants", "FoodDrinkPlant", "FoodDrinkAnimal", "FoodCheesePlant", "FoodCheeseAnimal", "FoodSeeds", "FoodLeaves", "FoodMilledPlant", "FoodBoneMeal", "FoodFat", "FoodPaste", "FoodPressedMaterial", "FoodExtractPlant", "FoodExtractAnimal", "FoodMiscLiquid", "FurnitureType", "FurnitureStoneClay", "FurnitureMetal", "FurnitureOtherMaterials", "RefuseItems", "RefuseCorpses", "RefuseParts", "RefuseSkulls", "RefuseBones", "RefuseShells", "RefuseTeeth", "RefuseHorns", "RefuseHair", "StoneOres", "StoneEconomic", "<PERSON><PERSON><PERSON>", "StoneClay", "AmmoType", "AmmoMetal", "AmmoOther", "BarsMetal", "BarsOther", "BlocksStone", "BlocksMetal", "BlocksOther", "RoughGem", "RoughGlass", "CutGem", "CutGlass", "CutStone", "GoodsType", "GoodsStone", "GoodsMetal", "GoodsGem", "GoodsOther", "ThreadSilk", "ThreadPlant", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ThreadMetal", "ClothSilk", "ClothPlant", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ClothMetal", "WeaponsType", "WeaponsTrapcomp", "WeaponsMetal", "WeaponsStone", "WeaponsOther", "ArmorBody", "ArmorHead", "ArmorFeet", "ArmorHands", "ArmorLegs", "ArmorShield", "ArmorMetal", "ArmorOther", "SheetPaper", "SheetParchment"]}, "tile_dig_designation": {"id": "#tile_dig_designation", "enum": ["No", "<PERSON><PERSON><PERSON>", "UpDownStair", "Channel", "<PERSON><PERSON>", "DownStair", "UpStair"]}}