{"$schema": "https://ben.lubar.me/df-ai-schemas/room-template.json", "f": [{"type": "door", "x": 7, "y": 0, "z": 0, "ignore": true}, {"type": "door", "x": 7, "y": 2, "z": 0, "ignore": true}], "r": [{"type": "corridor", "corridor_type": "corridor", "min": [-7, -1, 0], "max": [-1, 1, 0], "layout": [0, 1], "exits": [["generic01_corridor_any", -1, 1, 0], ["generic01_generic", 1, -1, 0], ["generic01_generic", 1, 3, 0], ["generic01_in_corridor", 3, 0, 0], ["generic01_in_corridor", 3, 2, 0], ["generic01_generic", 5, -1, 0], ["generic01_generic", 5, 3, 0], ["generic01_in_corridor", 0, 1, 0], ["generic01_in_corridor", 6, 1, 0]], "remove_if_unused": true}]}