#pragma once

#include "../utils/common/common.h"
#include "../debug/logging.h"
#include "../utils/performance/performance_monitor.h"

#include <unordered_map>
#include <unordered_set>
#include <queue>
#include <memory>

namespace dfai {
namespace resources {

/**
 * @brief Resource types for advanced resource management
 */
enum class ResourceType {
    WOOD,
    STONE,
    METAL_ORE,
    METAL_BAR,
    FOOD,
    CLOTH,
    LEATHER,
    GEMS,
    FINISHED_GOODS,
    WEAPONS,
    ARMOR,
    TOOLS,
    FUEL,
    SEEDS,
    CUSTOM
};

/**
 * @brief Resource priority levels
 */
enum class ResourcePriority {
    CRITICAL = 1,
    HIGH = 2,
    NORMAL = 3,
    LOW = 4,
    OPTIONAL = 5
};

/**
 * @brief Resource consumption prediction
 */
struct ResourceConsumption {
    ResourceType type;
    int32_t current_amount;
    int32_t required_amount;
    int32_t predicted_consumption_per_season;
    int32_t estimated_production_per_season;
    int32_t safety_buffer;
    ResourcePriority priority;
    
    bool is_critical() const { return current_amount < required_amount; }
    bool needs_attention() const { return current_amount < (required_amount + safety_buffer); }
    int32_t shortage() const { return std::max(0, required_amount - current_amount); }
};

/**
 * @brief Production task for resource management
 */
struct ProductionTask {
    int32_t id;
    ResourceType output_type;
    std::vector<std::pair<ResourceType, int32_t>> input_requirements;
    int32_t target_quantity;
    ResourcePriority priority;
    std::string workshop_type;
    std::string job_type;
    std::chrono::steady_clock::time_point created_time;
    bool is_active;
    bool is_completed;
    
    std::string description() const;
};

/**
 * @brief Advanced resource management system
 * 
 * Provides intelligent resource tracking, prediction, and automated
 * production management to ensure fortress sustainability.
 */
class ResourceManager {
public:
    ResourceManager();
    ~ResourceManager();
    
    // Lifecycle
    bool Initialize(color_ostream& out);
    void Shutdown();
    void Update(color_ostream& out);
    
    // Resource tracking
    void UpdateResourceCounts(color_ostream& out);
    int32_t GetResourceCount(ResourceType type) const;
    void SetResourceCount(ResourceType type, int32_t count);
    
    // Resource requirements
    void SetResourceRequirement(ResourceType type, int32_t required, ResourcePriority priority = ResourcePriority::NORMAL);
    void SetSafetyBuffer(ResourceType type, int32_t buffer);
    ResourceConsumption GetResourceStatus(ResourceType type) const;
    
    // Consumption prediction
    void RecordResourceConsumption(ResourceType type, int32_t amount);
    void RecordResourceProduction(ResourceType type, int32_t amount);
    void UpdateConsumptionPredictions();
    
    // Production management
    int32_t CreateProductionTask(const ProductionTask& task);
    bool CancelProductionTask(int32_t task_id);
    void UpdateProductionTasks(color_ostream& out);
    
    std::vector<ProductionTask> GetActiveProductionTasks() const;
    std::vector<ProductionTask> GetPendingProductionTasks() const;
    
    // Intelligent production planning
    void PlanResourceProduction(color_ostream& out);
    void OptimizeProductionQueue();
    bool CanCreateProductionChain(ResourceType target_type, int32_t quantity) const;
    
    // Resource allocation
    bool ReserveResource(ResourceType type, int32_t amount, const std::string& requester);
    void ReleaseReservedResource(ResourceType type, int32_t amount, const std::string& requester);
    int32_t GetAvailableResource(ResourceType type) const;
    
    // Trade management integration
    struct TradeRecommendation {
        ResourceType type;
        int32_t quantity;
        bool should_buy;
        bool should_sell;
        ResourcePriority priority;
        std::string reason;
    };
    
    std::vector<TradeRecommendation> GetTradeRecommendations() const;
    void ProcessTradeCaravan(color_ostream& out);
    
    // Stockpile optimization
    struct StockpileOptimization {
        df::coord location;
        ResourceType preferred_type;
        int32_t recommended_size;
        ResourcePriority priority;
        std::string reason;
    };
    
    std::vector<StockpileOptimization> GetStockpileOptimizations() const;
    void OptimizeStockpileLayout(color_ostream& out);
    
    // Emergency management
    void HandleResourceCrisis(ResourceType type, color_ostream& out);
    std::vector<ResourceType> GetCriticalResources() const;
    bool IsInResourceCrisis() const;
    
    // Reporting and analysis
    void GenerateResourceReport(color_ostream& out, bool detailed = false) const;
    void GenerateProductionReport(color_ostream& out) const;
    std::string GetResourceSummary() const;
    
    // Configuration
    struct ResourceManagerConfig {
        bool auto_production_enabled = true;
        bool emergency_production_enabled = true;
        bool trade_automation_enabled = false;
        bool stockpile_optimization_enabled = true;
        
        int32_t prediction_seasons = 4;
        double safety_buffer_multiplier = 1.5;
        int32_t max_concurrent_production_tasks = 20;
        
        std::string to_string() const;
    };
    
    void SetConfig(const ResourceManagerConfig& config);
    const ResourceManagerConfig& GetConfig() const;
    
private:
    debug::Logger& logger_;
    utils::PerformanceMonitor& performance_monitor_;
    bool initialized_;
    
    // Resource tracking
    std::unordered_map<ResourceType, int32_t> resource_counts_;
    std::unordered_map<ResourceType, int32_t> resource_requirements_;
    std::unordered_map<ResourceType, int32_t> safety_buffers_;
    std::unordered_map<ResourceType, ResourcePriority> resource_priorities_;
    
    // Resource reservations
    std::unordered_map<ResourceType, std::unordered_map<std::string, int32_t>> resource_reservations_;
    
    // Consumption tracking
    struct ConsumptionHistory {
        std::queue<std::pair<std::chrono::steady_clock::time_point, int32_t>> consumption_events;
        std::queue<std::pair<std::chrono::steady_clock::time_point, int32_t>> production_events;
        int32_t seasonal_consumption_prediction = 0;
        int32_t seasonal_production_prediction = 0;
    };
    
    std::unordered_map<ResourceType, ConsumptionHistory> consumption_history_;
    
    // Production management
    std::unordered_map<int32_t, ProductionTask> production_tasks_;
    std::priority_queue<std::pair<ResourcePriority, int32_t>, 
                       std::vector<std::pair<ResourcePriority, int32_t>>,
                       std::greater<std::pair<ResourcePriority, int32_t>>> production_queue_;
    
    int32_t next_production_task_id_;
    
    // Configuration
    ResourceManagerConfig config_;
    
    // Update tracking
    std::chrono::steady_clock::time_point last_update_time_;
    std::chrono::steady_clock::time_point last_prediction_update_;
    
    // Helper methods
    void UpdateResourceCountsFromGame(color_ostream& out);
    void PredictResourceConsumption(ResourceType type);
    void CreateAutomaticProductionTasks(color_ostream& out);
    bool HasProductionCapability(const std::string& workshop_type) const;
    std::vector<ResourceType> GetProductionInputs(ResourceType output_type) const;
    
    // Resource chain analysis
    struct ResourceChain {
        ResourceType target;
        std::vector<std::pair<ResourceType, int32_t>> inputs;
        std::string workshop_required;
        int32_t efficiency_rating;
    };
    
    std::vector<ResourceChain> AnalyzeProductionChains(ResourceType target) const;
    ResourceChain GetOptimalProductionChain(ResourceType target) const;
    
    // Crisis management
    void TriggerEmergencyProduction(ResourceType type, color_ostream& out);
    void RequestEmergencyTrade(ResourceType type, color_ostream& out);
    void ImplementResourceRationing(ResourceType type, color_ostream& out);
    
    // Utility
    std::string ResourceTypeToString(ResourceType type) const;
    ResourceType StringToResourceType(const std::string& type_str) const;
    double GetElapsedSeasons(std::chrono::steady_clock::time_point from) const;
};

} // namespace resources
} // namespace dfai
