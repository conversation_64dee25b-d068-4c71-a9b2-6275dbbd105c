#include "spatial_utils.h"
#include "../common/types.h"
#include <algorithm>
#include <cmath>

namespace dfai {
namespace utils {
namespace algorithms {

// Distance calculations
float CalculateDistance(const Coordinate& a, const Coordinate& b) {
    return static_cast<float>(a.DistanceTo(b));
}

int CalculateManhattanDistance(const Coordinate& a, const Coordinate& b) {
    return a.ManhattanDistanceTo(b);
}

float CalculateChebyshevDistance(const Coordinate& a, const Coordinate& b) {
    int dx = std::abs(a.x - b.x);
    int dy = std::abs(a.y - b.y);
    int dz = std::abs(a.z - b.z);
    return static_cast<float>(std::max({dx, dy, dz}));
}

// Area calculations
int CalculateArea(const Rectangle& rect) {
    if (!rect.IsValid()) {
        return 0;
    }
    return rect.Width() * rect.Height();
}

int CalculateVolume(const Rectangle& rect) {
    if (!rect.IsValid()) {
        return 0;
    }
    return rect.Volume();
}

int CalculatePerimeter(const Rectangle& rect) {
    if (!rect.IsValid()) {
        return 0;
    }
    return 2 * (rect.Width() + rect.Height());
}

// Geometric operations
Coordinate FindCenter(const std::vector<Coordinate>& points) {
    if (points.empty()) {
        return {0, 0, 0};
    }
    
    int sum_x = 0, sum_y = 0, sum_z = 0;
    for (const Coordinate& point : points) {
        sum_x += point.x;
        sum_y += point.y;
        sum_z += point.z;
    }
    
    int count = static_cast<int>(points.size());
    return {sum_x / count, sum_y / count, sum_z / count};
}

Rectangle FindBoundingBox(const std::vector<Coordinate>& points) {
    if (points.empty()) {
        return {{0, 0, 0}, {-1, -1, -1}}; // Invalid rectangle
    }
    
    Coordinate min_coord = points[0];
    Coordinate max_coord = points[0];
    
    for (const Coordinate& point : points) {
        min_coord.x = std::min(min_coord.x, point.x);
        min_coord.y = std::min(min_coord.y, point.y);
        min_coord.z = std::min(min_coord.z, point.z);
        
        max_coord.x = std::max(max_coord.x, point.x);
        max_coord.y = std::max(max_coord.y, point.y);
        max_coord.z = std::max(max_coord.z, point.z);
    }
    
    return {min_coord, max_coord};
}

Coordinate FindClosestPoint(const Coordinate& target, const std::vector<Coordinate>& points) {
    if (points.empty()) {
        return target;
    }
    
    Coordinate closest = points[0];
    float min_distance = CalculateDistance(target, closest);
    
    for (const Coordinate& point : points) {
        float distance = CalculateDistance(target, point);
        if (distance < min_distance) {
            min_distance = distance;
            closest = point;
        }
    }
    
    return closest;
}

std::vector<Coordinate> FindPointsInRadius(const Coordinate& center, float radius, 
                                          const std::vector<Coordinate>& points) {
    std::vector<Coordinate> result;
    
    for (const Coordinate& point : points) {
        if (CalculateDistance(center, point) <= radius) {
            result.push_back(point);
        }
    }
    
    return result;
}

std::vector<Coordinate> FindPointsInRectangle(const Rectangle& rect, 
                                             const std::vector<Coordinate>& points) {
    std::vector<Coordinate> result;
    
    for (const Coordinate& point : points) {
        if (rect.Contains(point)) {
            result.push_back(point);
        }
    }
    
    return result;
}

// Line operations
std::vector<Coordinate> GenerateLine(const Coordinate& start, const Coordinate& end) {
    std::vector<Coordinate> line;
    
    int dx = std::abs(end.x - start.x);
    int dy = std::abs(end.y - start.y);
    int dz = std::abs(end.z - start.z);
    
    int x_step = (start.x < end.x) ? 1 : -1;
    int y_step = (start.y < end.y) ? 1 : -1;
    int z_step = (start.z < end.z) ? 1 : -1;
    
    int max_delta = std::max({dx, dy, dz});
    
    if (max_delta == 0) {
        line.push_back(start);
        return line;
    }
    
    // 3D Bresenham-like algorithm
    Coordinate current = start;
    line.push_back(current);
    
    int x_error = dx - max_delta;
    int y_error = dy - max_delta;
    int z_error = dz - max_delta;
    
    for (int i = 0; i < max_delta; ++i) {
        x_error += dx;
        if (x_error >= 0) {
            current.x += x_step;
            x_error -= max_delta;
        }
        
        y_error += dy;
        if (y_error >= 0) {
            current.y += y_step;
            y_error -= max_delta;
        }
        
        z_error += dz;
        if (z_error >= 0) {
            current.z += z_step;
            z_error -= max_delta;
        }
        
        line.push_back(current);
    }
    
    return line;
}

bool IsPointOnLine(const Coordinate& point, const Coordinate& line_start, const Coordinate& line_end) {
    // Check if point is collinear with line_start and line_end
    Coordinate vec1 = point - line_start;
    Coordinate vec2 = line_end - line_start;
    
    // Cross product should be zero for collinear points
    // For 3D, we check if the point lies on the line segment
    if (vec2.x == 0 && vec2.y == 0 && vec2.z == 0) {
        return point == line_start;
    }
    
    // Check if vectors are parallel (cross product is zero)
    // and if point is within the line segment bounds
    float t = -1.0f;
    
    if (vec2.x != 0) {
        t = static_cast<float>(vec1.x) / vec2.x;
    } else if (vec2.y != 0) {
        t = static_cast<float>(vec1.y) / vec2.y;
    } else if (vec2.z != 0) {
        t = static_cast<float>(vec1.z) / vec2.z;
    }
    
    if (t < 0.0f || t > 1.0f) {
        return false;
    }
    
    // Check if the point matches the calculated position on the line
    Coordinate calculated = {
        line_start.x + static_cast<int>(t * vec2.x),
        line_start.y + static_cast<int>(t * vec2.y),
        line_start.z + static_cast<int>(t * vec2.z)
    };
    
    return point == calculated;
}

// Rectangle operations
std::vector<Coordinate> GenerateRectanglePerimeter(const Rectangle& rect) {
    std::vector<Coordinate> perimeter;
    
    if (!rect.IsValid()) {
        return perimeter;
    }
    
    // For 2D rectangle (assuming z is constant)
    int z = rect.min.z;
    
    // Top and bottom edges
    for (int x = rect.min.x; x <= rect.max.x; ++x) {
        perimeter.push_back({x, rect.min.y, z});
        if (rect.min.y != rect.max.y) {
            perimeter.push_back({x, rect.max.y, z});
        }
    }
    
    // Left and right edges (excluding corners already added)
    for (int y = rect.min.y + 1; y < rect.max.y; ++y) {
        perimeter.push_back({rect.min.x, y, z});
        if (rect.min.x != rect.max.x) {
            perimeter.push_back({rect.max.x, y, z});
        }
    }
    
    return perimeter;
}

std::vector<Coordinate> GenerateRectangleArea(const Rectangle& rect) {
    std::vector<Coordinate> area;
    
    if (!rect.IsValid()) {
        return area;
    }
    
    for (int z = rect.min.z; z <= rect.max.z; ++z) {
        for (int y = rect.min.y; y <= rect.max.y; ++y) {
            for (int x = rect.min.x; x <= rect.max.x; ++x) {
                area.push_back({x, y, z});
            }
        }
    }
    
    return area;
}

Rectangle ExpandRectangle(const Rectangle& rect, int expansion) {
    if (!rect.IsValid()) {
        return rect;
    }
    
    return {
        {rect.min.x - expansion, rect.min.y - expansion, rect.min.z - expansion},
        {rect.max.x + expansion, rect.max.y + expansion, rect.max.z + expansion}
    };
}

Rectangle ShrinkRectangle(const Rectangle& rect, int shrinkage) {
    if (!rect.IsValid()) {
        return rect;
    }
    
    Rectangle result = {
        {rect.min.x + shrinkage, rect.min.y + shrinkage, rect.min.z + shrinkage},
        {rect.max.x - shrinkage, rect.max.y - shrinkage, rect.max.z - shrinkage}
    };
    
    // Ensure the result is still valid
    if (!result.IsValid()) {
        return {{0, 0, 0}, {-1, -1, -1}}; // Invalid rectangle
    }
    
    return result;
}

// Sorting and filtering
void SortPointsByDistance(std::vector<Coordinate>& points, const Coordinate& reference) {
    std::sort(points.begin(), points.end(), 
        [&reference](const Coordinate& a, const Coordinate& b) {
            return CalculateDistance(reference, a) < CalculateDistance(reference, b);
        });
}

std::vector<Coordinate> FilterPointsByDistance(const std::vector<Coordinate>& points, 
                                              const Coordinate& reference, 
                                              float min_distance, float max_distance) {
    std::vector<Coordinate> filtered;
    
    for (const Coordinate& point : points) {
        float distance = CalculateDistance(reference, point);
        if (distance >= min_distance && distance <= max_distance) {
            filtered.push_back(point);
        }
    }
    
    return filtered;
}

} // namespace algorithms
} // namespace utils
} // namespace dfai
