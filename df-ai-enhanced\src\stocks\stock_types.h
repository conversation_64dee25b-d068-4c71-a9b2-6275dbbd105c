#pragma once

#include "../utils/common.h"
#include "../../stocks.h"

namespace df_ai {
namespace stocks {

/**
 * Stock item types from the original system
 */
using stock_item = stock_item;

/**
 * Helper structure for finding items
 */
struct FindItemInfo {
    df::item_type item_type;
    int16_t item_subtype;
    df::job_material_category material_category;
    int32_t material_index;
    std::function<bool(df::item*)> item_filter;
    std::function<bool(df::itemdef*)> itemdef_filter;
    bool allow_foreign;
    bool allow_forbidden;
    
    FindItemInfo(df::item_type type = df::item_type::NONE, int16_t subtype = -1)
        : item_type(type), item_subtype(subtype), material_index(-1), 
          allow_foreign(false), allow_forbidden(false) {
        material_category.whole = 0;
    }
    
    bool matches(df::item* item) const;
    bool matches_type(df::item* item) const;
    bool matches_material(df::item* item) const;
};

/**
 * Production order information
 */
struct ProductionOrder {
    df::job_type job_type;
    stock_item::item target_item;
    int32_t quantity;
    int32_t priority;
    df::job_material_category material_category;
    int32_t material_index;
    std::string reason;
    
    ProductionOrder(df::job_type job, stock_item::item item, int32_t qty = 1)
        : job_type(job), target_item(item), quantity(qty), priority(0), material_index(-1) {
        material_category.whole = 0;
    }
};

/**
 * Material requirement for production
 */
struct MaterialReq {
    df::item_type item_type;
    int16_t item_subtype;
    df::job_material_category material_category;
    int32_t material_index;
    int32_t quantity;
    bool essential; // true if production cannot proceed without this material
    
    MaterialReq(df::item_type type, int32_t qty = 1, bool required = true)
        : item_type(type), item_subtype(-1), material_index(-1), 
          quantity(qty), essential(required) {
        material_category.whole = 0;
    }
};

/**
 * Stock watch configuration for monitoring specific items
 */
struct StockWatch {
    stock_item::item item;
    int32_t threshold_low;
    int32_t threshold_high;
    bool auto_produce;
    bool alert_on_shortage;
    int32_t production_batch_size;
    
    StockWatch(stock_item::item i, int32_t low = 5, int32_t high = 20)
        : item(i), threshold_low(low), threshold_high(high), 
          auto_produce(true), alert_on_shortage(true), production_batch_size(5) {}
    
    void reset();
    Json::Value to_json() const;
    bool from_json(const Json::Value& json, std::string& error);
};

/**
 * Farm plot assignment tracking
 */
struct FarmAssignment {
    room* farmplot;
    int32_t plant_id;
    uint8_t season;
    bool assigned;
    
    FarmAssignment(room* plot, int32_t plant, uint8_t s)
        : farmplot(plot), plant_id(plant), season(s), assigned(false) {}
};

/**
 * Metal working preferences
 */
struct MetalPreferences {
    std::map<df::material_flags, std::set<int32_t>> preferred_metals;
    std::set<int32_t> forbidden_metals;
    int32_t default_weapon_metal;
    int32_t default_armor_metal;
    int32_t default_tool_metal;
    
    MetalPreferences() : default_weapon_metal(-1), default_armor_metal(-1), default_tool_metal(-1) {}
    
    bool is_preferred(int32_t metal_type, df::material_flags use_category) const;
    int32_t get_best_metal(df::material_flags use_category, const std::set<int32_t>& available) const;
};

/**
 * Production statistics
 */
struct ProductionStats {
    std::map<stock_item::item, int32_t> items_produced_this_year;
    std::map<stock_item::item, int32_t> items_consumed_this_year;
    std::map<df::job_type, int32_t> jobs_completed_this_year;
    std::map<df::job_type, int32_t> jobs_failed_this_year;
    int32_t total_manager_orders;
    int32_t completed_manager_orders;
    int32_t current_year;
    
    ProductionStats() : total_manager_orders(0), completed_manager_orders(0), current_year(-1) {}
    
    void reset_yearly_stats();
    double production_efficiency() const;
    int32_t net_production(stock_item::item item) const;
};

/**
 * Stock shortage alert
 */
struct StockAlert {
    stock_item::item item;
    int32_t current_count;
    int32_t required_count;
    std::string reason;
    int32_t severity; // 0=info, 1=warning, 2=critical
    
    StockAlert(stock_item::item i, int32_t current, int32_t required, const std::string& r, int32_t sev = 1)
        : item(i), current_count(current), required_count(required), reason(r), severity(sev) {}
};

/**
 * Stockpile configuration
 */
struct StockpileSettings {
    stockpile_type::type type;
    std::set<df::stockpile_list> enabled_categories;
    std::set<df::stockpile_list> disabled_categories;
    bool accept_from_links;
    bool give_to_links;
    bool quantum_stockpile;
    
    StockpileSettings(stockpile_type::type t) : type(t), accept_from_links(true), 
                                               give_to_links(true), quantum_stockpile(false) {}
};

} // namespace stocks
} // namespace df_ai
