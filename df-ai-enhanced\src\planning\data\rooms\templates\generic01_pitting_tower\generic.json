{"$schema": "https://ben.lubar.me/df-ai-schemas/room-template.json", "f": [{"dig": "Channel", "x": 0, "y": 0, "z": 0}, {"dig": "Channel", "x": 1, "y": 0, "z": 0}, {"construction": "Floor", "x": 0, "y": 0, "z": -1}, {"construction": "Floor", "x": 0, "y": 0, "z": 0}, {"construction": "Floor", "x": 1, "y": 0, "z": 0}, {"construction": "Floor", "x": 2, "y": 0, "z": 0}, {"construction": "Floor", "x": 0, "y": 1, "z": 0}, {"construction": "Floor", "x": 2, "y": 1, "z": 0}, {"construction": "Floor", "x": 0, "y": 2, "z": 0}, {"construction": "Floor", "x": 1, "y": 2, "z": 0}, {"construction": "Floor", "x": 2, "y": 2, "z": 0}, {"type": "hive", "construction": "Floor", "x": 3, "y": 1, "z": 0}, {"construction": "DownStair", "x": -1, "y": 1, "z": 0}, {"construction": "UpDownStair", "x": 3, "y": 5, "z": 9}, {"construction": "UpDownStair", "x": 3, "y": 5, "z": 8}, {"construction": "UpDownStair", "x": 3, "y": 5, "z": 7}, {"construction": "UpDownStair", "x": 3, "y": 5, "z": 6}, {"construction": "UpDownStair", "x": 3, "y": 5, "z": 5}, {"construction": "UpDownStair", "x": 3, "y": 5, "z": 4}, {"construction": "UpDownStair", "x": 3, "y": 5, "z": 3}, {"construction": "UpDownStair", "x": 3, "y": 5, "z": 2}, {"construction": "UpDownStair", "x": 3, "y": 5, "z": 1}, {"construction": "UpStair", "x": 3, "y": 5, "z": 0}, {"dig": "Channel", "x": 5, "y": 5, "z": 0}, {"dig": "Channel", "x": 6, "y": 5, "z": 0}, {"type": "hatch", "dig": "Channel", "x": 0, "y": 0, "z": 0}, {"dig": "<PERSON><PERSON>", "construction": "<PERSON><PERSON>", "x": 1, "y": 0, "z": 0}], "r": [{"type": "garbagedump", "min": [-1, 0, 0], "max": [-1, 0, 0], "accesspath": [1, 2], "outdoor": true}, {"type": "corridor", "corridor_type": "corridor", "min": [0, 0, 0], "max": [1, 0, 0], "layout": [0, 1, 2], "comment": "garbage pit", "outdoor": true}, {"type": "corridor", "corridor_type": "corridor", "min": [0, 0, -1], "max": [1, 0, -1], "layout": [26], "accesspath": [1], "comment": "garbage pit (underground)"}, {"type": "stockpile", "stockpile_type": "animals", "comment": "pitting queue", "level": 0, "stock_specific1": true, "stock_specific2": true, "min": [-1, -1, 10], "max": [1, 1, 10], "outdoor": true, "require_floor": false, "layout": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12]}, {"type": "corridor", "corridor_type": "corridor", "min": [-5, -5, 0], "max": [5, 5, 9], "outdoor": true, "require_floor": false, "accesspath": [0, 3], "comment": "pitting tower", "layout": [13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]}, {"type": "pitcage", "min": [0, 0, 10], "max": [0, 0, 10], "outdoor": true, "require_floor": false, "accesspath": [4], "layout": [25]}]}