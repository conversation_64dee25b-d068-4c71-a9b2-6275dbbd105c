#pragma once

#include "../utils/common.h"
#include "../utils/logging.h"
#include "stock_types.h"

#include <map>
#include <set>
#include <vector>

namespace df_ai {

// Forward declaration
namespace core { class AIController; }

namespace stocks {

/**
 * Manages all stock-related activities including items, materials, and production
 */
class StockManager {
private:
    core::AIController& ai_;
    utils::Logger& logger_;
    
    // Stock tracking
    std::map<stock_item::item, int32_t> free_count_;
    std::map<stock_item::item, int32_t> total_count_;
    std::map<stock_item::item, std::map<int16_t, std::pair<int32_t, int32_t>>> subtype_count_;
    std::map<stock_item::item, std::ostringstream> action_reasons_;
    std::map<int32_t, int32_t> metal_ingots_;
    
    // Update management
    OnupdateCallback* onupdate_handle_;
    std::vector<stock_item::item> update_queue_;
    std::vector<stock_item::item> counting_queue_;
    size_t last_update_index_;
    
    // Farming
    std::map<std::pair<uint8_t, int32_t>, size_t> farmplot_assignments_;
    std::map<int32_t, size_t> seed_counts_;
    std::map<int32_t, size_t> plant_counts_;
    std::vector<room*> farmplot_update_queue_;
    
    // Production tracking
    int32_t last_unforbid_year_;
    int32_t last_manager_stall_;
    df::job_type last_manager_order_;
    
    // Update flags
    bool updating_seeds_;
    bool updating_plants_;
    bool updating_corpses_;
    bool updating_slabs_;
    bool updating_ingots_;
    
    // Material databases
    std::map<int32_t, int16_t> drink_plants_;
    std::map<int32_t, int16_t> drink_fruits_;
    std::map<int32_t, int16_t> thread_plants_;
    std::map<int32_t, int16_t> mill_plants_;
    std::map<int32_t, int16_t> bag_plants_;
    std::map<int32_t, int16_t> dye_plants_;
    std::map<int32_t, int16_t> slurry_plants_;
    std::map<int32_t, int16_t> grow_plants_;
    std::map<int32_t, int16_t> milk_creatures_;
    std::set<int32_t> clay_stones_;
    std::map<int32_t, std::string> raw_coke_;
    std::map<std::string, int32_t> raw_coke_inverse_;
    
    // Metal preferences and ores
    std::map<df::material_flags, std::set<int32_t>> metal_preferences_;
    std::vector<std::set<int32_t>> simple_metal_ores_;
    
    // Error tracking
    std::set<std::tuple<farm_type::type, df::biome_type, int8_t>> plant_complaints_;
    bool cant_make_pickaxe_;
    
    // Tree management
    std::set<df::coord, std::function<bool(df::coord, df::coord)>> last_tree_list_;
    df::coord last_cut_position_;
    int32_t cut_wait_counter_;
    int32_t last_food_warning_year_;
    
public:
    StockManager(core::AIController& ai, utils::Logger& logger);
    ~StockManager();
    
    // Lifecycle management
    command_result startup(color_ostream& out);
    command_result shutdown(color_ostream& out);
    command_result register_updates(color_ostream& out);
    command_result unregister_updates(color_ostream& out);
    void reset();
    
    // Main update loop
    void update(color_ostream& out);
    
    // Stock counting and tracking
    void count_stocks(color_ostream& out, stock_item::item type);
    void count_stocks_by_subtype(color_ostream& out, stock_item::item type, FindItemInfo& helper);
    void update_ingots(color_ostream& out);
    void update_corpses(color_ostream& out);
    void update_slabs(color_ostream& out);
    
    // Stock queries
    int32_t count_free(stock_item::item type) const;
    int32_t count_total(stock_item::item type) const;
    int32_t count_subtype(stock_item::item type, int16_t subtype) const;
    bool has_enough(stock_item::item type, int32_t required_count) const;
    bool need_more(stock_item::item type) const;
    
    // Stock requirements
    int32_t calculate_needed_count(stock_item::item type);
    int16_t min_subtype_for_item(stock_item::item type);
    void queue_production(color_ostream& out, stock_item::item type, int32_t amount, std::ostream& reason);
    void execute_stock_action(color_ostream& out, stock_item::item type);
    
    // Item finding
    FindItemInfo create_item_finder_ammo(df::job_skill skill = df::job_skill::NONE, bool training = false, bool metal = true);
    FindItemInfo create_item_finder_armor(df::items_other_id index);
    FindItemInfo create_item_finder_clothes(df::items_other_id index);
    FindItemInfo create_item_finder_tool(df::tool_uses use, std::function<bool(df::itemdef_toolst*)> predicate = nullptr);
    
    // Item utilities
    bool is_item_free(df::item* item, bool allow_nonempty = false);
    bool is_metal_ore(int32_t material_index);
    bool is_metal_ore(df::item* item);
    
    // Farming management
    void setup_farmplot(color_ostream& out, room* farmplot_room, bool initial = true);
    void count_seeds(color_ostream& out);
    void count_plants(color_ostream& out);
    void update_kitchen_settings(color_ostream& out);
    void update_plant_database(color_ostream& out);
    
    // Tree management
    df::coord cut_trees(color_ostream& out, int32_t amount, std::ostream& reason);
    std::set<df::coord, std::function<bool(df::coord, df::coord)>> get_tree_list();
    
    // Manager orders
    void add_manager_order(color_ostream& out, const df::manager_order_template& order_template, int32_t amount);
    int32_t count_manager_orders_by_material(const df::job_material_category& material_category, df::job_type exclude_job = df::job_type::NONE);
    
    // Production queuing
    void queue_need_ammo(color_ostream& out, std::ostream& reason);
    void queue_need_anvil(color_ostream& out, std::ostream& reason);
    void queue_need_cage(color_ostream& out, std::ostream& reason);
    void queue_need_coffin(color_ostream& out, std::ostream& reason);
    void queue_need_armor(color_ostream& out, df::items_other_id armor_type, std::ostream& reason);
    void queue_need_weapon(color_ostream& out, df::items_other_id weapon_type, std::ostream& reason);
    void queue_need_furniture(color_ostream& out, stock_item::item furniture_type, std::ostream& reason);
    
    // Slab management
    void queue_slab(color_ostream& out, int32_t historical_figure_id);
    
    // Material database access
    const std::map<int32_t, int16_t>& drink_plants() const { return drink_plants_; }
    const std::map<int32_t, int16_t>& thread_plants() const { return thread_plants_; }
    const std::map<int32_t, int16_t>& dye_plants() const { return dye_plants_; }
    const std::set<int32_t>& clay_stones() const { return clay_stones_; }
    const std::map<df::material_flags, std::set<int32_t>>& metal_preferences() const { return metal_preferences_; }
    const std::vector<std::set<int32_t>>& simple_metal_ores() const { return simple_metal_ores_; }
    
    // Trade support
    bool willing_to_trade_item(color_ostream& out, df::item* item, bool check_container = true);
    bool want_trader_item(color_ostream& out, df::item* item, const std::vector<df::item*>& already_wanted);
    
    // Stock counts access (for external access)
    const std::map<stock_item::item, int32_t>& free_counts() const { return free_count_; }
    const std::map<stock_item::item, int32_t>& total_counts() const { return total_count_; }
    const std::map<int32_t, int32_t>& metal_ingots() const { return metal_ingots_; }
    
    // Status and reporting
    std::string status() const;
    void report(std::ostream& out, bool html = false) const;
    
private:
    // Helper methods
    void initialize_material_databases();
    void update_simple_metal_ores(color_ostream& out);
    void process_update_queue(color_ostream& out);
    bool select_most_abundant_metal(const std::map<int32_t, int32_t>& potential_bars, 
                                   const std::map<int32_t, int32_t>& actual_bars, 
                                   int32_t& chosen_type);
    void check_food_supplies(color_ostream& out);
    void manage_stockpile_settings(color_ostream& out);
    void unforbid_items(color_ostream& out);
};

} // namespace stocks
} // namespace df_ai
