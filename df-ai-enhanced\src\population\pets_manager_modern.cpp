#include "pets_manager.h"
#include "../debug/logging.h"
#include "../utils/common/common.h"

#include "modules/Units.h"
#include "modules/Buildings.h"
#include "modules/Maps.h"

#include "df/world.h"
#include "df/unit.h"
#include "df/building_civzonest.h"
#include "df/creature_raw.h"
#include "df/caste_raw.h"
#include "df/manager_order.h"
#include "df/manager_order_template.h"
#include "df/job_type.h"

REQUIRE_GLOBAL(world);

namespace dfai {
namespace population {

PetsManager::PetsManager()
    : logger_(debug::Logger::GetInstance())
    , initialized_(false)
    , pets_()
    , stray_pets_()
    , pet_assignment_queue_()
    , update_counter_(0)
{
    logger_.Info("PetsManager created");
}

PetsManager::~PetsManager() {
    if (initialized_) {
        Shutdown();
    }
}

bool PetsManager::Initialize(color_ostream& out) {
    if (initialized_) {
        logger_.Warning("PetsManager already initialized");
        return true;
    }
    
    logger_.Info("Initializing PetsManager...");
    
    try {
        ScanForNewPets(out);
        initialized_ = true;
        logger_.Info("PetsManager initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        logger_.Error("Exception during PetsManager initialization: {}", e.what());
        return false;
    }
}

void PetsManager::Shutdown() {
    if (!initialized_) {
        return;
    }
    
    logger_.Info("Shutting down PetsManager...");
    
    pets_.clear();
    stray_pets_.clear();
    pet_assignment_queue_ = std::queue<int32_t>();
    
    initialized_ = false;
    logger_.Info("PetsManager shutdown complete");
}

void PetsManager::Update(color_ostream& out) {
    if (!initialized_) {
        logger_.Warning("PetsManager::Update called before initialization");
        return;
    }
    
    try {
        update_counter_++;
        
        // Scan for new pets
        ScanForNewPets(out);
        
        // Update existing pets
        UpdatePets(out);
        
        // Handle stray pets
        HandleStrayPets(out);
        
        // Process pet assignment queue
        ProcessPetQueue(out);
        
        // Manage livestock
        ManageLivestock(out);
        
        // Clean up dead pets
        CleanupDeadPets();
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in PetsManager::Update: {}", e.what());
    }
}

void PetsManager::UpdatePets(color_ostream& out) {
    logger_.Debug("Updating pet status and assignments");
    
    if (!world) {
        logger_.Warning("World data not available for pet updates");
        return;
    }
    
    try {
        int32_t pets_updated = 0;
        
        for (auto& [pet_id, flags] : pets_) {
            auto* unit = df::unit::find(pet_id);
            if (!unit || Units::isDead(unit)) {
                continue;
            }
            
            UpdatePetStatus(out, pet_id);
            pets_updated++;
        }
        
        logger_.Debug("Updated {} pets", pets_updated);
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in UpdatePets: {}", e.what());
    }
}

void PetsManager::AssignPetToZone(color_ostream& out, df::unit* pet, df::building_civzonest* zone) {
    if (!pet || !zone) {
        logger_.Warning("Invalid pet or zone for assignment");
        return;
    }
    
    try {
        // Check if pet is already assigned to this zone
        if (auto ref = Units::getGeneralRef(pet, general_ref_type::BUILDING_CIVZONE_ASSIGNED)) {
            if (ref->getBuilding() == zone) {
                logger_.Debug("Pet {} already assigned to zone {}", pet->id, zone->id);
                return;
            }
        }
        
        // Create new assignment reference
        auto new_ref = new df::general_ref_building_civzone_assignedst();
        new_ref->building_id = zone->id;
        
        // Add reference to unit
        pet->general_refs.push_back(new_ref);
        
        // Add unit to zone's assigned units list
        if (std::find(zone->assigned_units.begin(), zone->assigned_units.end(), pet->id) == zone->assigned_units.end()) {
            zone->assigned_units.push_back(pet->id);
        }
        
        logger_.Info("Assigned pet {} to zone {}", pet->id, zone->id);
        
    } catch (const std::exception& e) {
        logger_.Error("Exception assigning pet {} to zone {}: {}", pet->id, zone->id, e.what());
    }
}

void PetsManager::HandleStrayPets(color_ostream& out) {
    if (stray_pets_.empty()) {
        return;
    }
    
    logger_.Debug("Handling {} stray pets", stray_pets_.size());
    
    try {
        auto it = stray_pets_.begin();
        while (it != stray_pets_.end()) {
            auto* unit = df::unit::find(*it);
            if (!unit || Units::isDead(unit)) {
                it = stray_pets_.erase(it);
                continue;
            }
            
            // Try to find suitable zone for this pet
            auto* zone = FindSuitableZone(unit);
            if (zone) {
                AssignPetToZone(out, unit, zone);
                it = stray_pets_.erase(it);
            } else {
                ++it;
            }
        }
        
    } catch (const std::exception& e) {
        logger_.Error("Exception handling stray pets: {}", e.what());
    }
}

void PetsManager::ProcessPetQueue(color_ostream& out) {
    if (pet_assignment_queue_.empty()) {
        return;
    }
    
    logger_.Debug("Processing pet assignment queue with {} items", pet_assignment_queue_.size());
    
    try {
        // Process a limited number of assignments per update to avoid performance issues
        const int32_t MAX_ASSIGNMENTS_PER_UPDATE = 5;
        int32_t assignments_processed = 0;
        
        while (!pet_assignment_queue_.empty() && assignments_processed < MAX_ASSIGNMENTS_PER_UPDATE) {
            int32_t pet_id = pet_assignment_queue_.front();
            pet_assignment_queue_.pop();
            
            auto* unit = df::unit::find(pet_id);
            if (!unit || Units::isDead(unit)) {
                assignments_processed++;
                continue;
            }
            
            // Try to assign this pet
            auto* zone = FindSuitableZone(unit);
            if (zone) {
                AssignPetToZone(out, unit, zone);
            } else {
                // No suitable zone found, add to stray pets
                stray_pets_.insert(pet_id);
            }
            
            assignments_processed++;
        }
        
    } catch (const std::exception& e) {
        logger_.Error("Exception processing pet queue: {}", e.what());
    }
}

bool PetsManager::IsPet(int32_t unit_id) const {
    return pets_.find(unit_id) != pets_.end();
}

PetFlags PetsManager::GetPetFlags(int32_t unit_id) const {
    auto it = pets_.find(unit_id);
    return (it != pets_.end()) ? it->second : PetFlags();
}

std::vector<int32_t> PetsManager::GetPets() const {
    std::vector<int32_t> result;
    result.reserve(pets_.size());
    
    for (const auto& [pet_id, flags] : pets_) {
        result.push_back(pet_id);
    }
    
    return result;
}

std::vector<int32_t> PetsManager::GetStrayPets() const {
    return std::vector<int32_t>(stray_pets_.begin(), stray_pets_.end());
}

void PetsManager::ManageLivestock(color_ostream& out) {
    logger_.Debug("Managing livestock production and needs");
    
    if (!world) {
        logger_.Warning("World data not available for livestock management");
        return;
    }
    
    try {
        int32_t milking_needed = 0;
        int32_t shearing_needed = 0;
        
        // Check current manager orders
        for (auto* order : world->manager_orders) {
            if (!order) continue;
            
            if (order->job_type == job_type::MilkCreature) {
                milking_needed -= order->amount_left;
            } else if (order->job_type == job_type::ShearCreature) {
                shearing_needed -= order->amount_left;
            }
        }
        
        // Check pets for milking and shearing needs
        for (const auto& [pet_id, flags] : pets_) {
            auto* unit = df::unit::find(pet_id);
            if (!unit || Units::isDead(unit) || Units::isChild(unit) || Units::isBaby(unit)) {
                continue;
            }
            
            if (flags.bits.milkable) {
                // Check if unit needs milking
                bool needs_milking = true;
                for (auto* trait : unit->status.misc_traits) {
                    if (trait->id == misc_trait_type::MilkCounter) {
                        needs_milking = false;
                        break;
                    }
                }
                if (needs_milking) {
                    milking_needed++;
                }
            }
            
            if (flags.bits.shearable) {
                // Check if unit needs shearing
                bool needs_shearing = false;
                if (unit->race >= 0 && unit->caste >= 0) {
                    auto* creature = df::creature_raw::find(unit->race);
                    if (creature && unit->caste < (int32_t)creature->caste.size()) {
                        auto* caste = creature->caste[unit->caste];
                        for (auto* stl : caste->shearable_tissue_layer) {
                            for (auto bpi : stl->bp_modifiers_idx) {
                                if (bpi < unit->appearance.bp_modifiers.size() &&
                                    unit->appearance.bp_modifiers[bpi] >= stl->length) {
                                    needs_shearing = true;
                                    break;
                                }
                            }
                            if (needs_shearing) break;
                        }
                    }
                }
                if (needs_shearing) {
                    shearing_needed++;
                }
            }
        }
        
        // Create manager orders if needed
        if (milking_needed > 0) {
            logger_.Info("Livestock needs milking: {} animals", milking_needed);
            // This would create manager orders for milking
        }
        
        if (shearing_needed > 0) {
            logger_.Info("Livestock needs shearing: {} animals", shearing_needed);
            // This would create manager orders for shearing
        }
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in ManageLivestock: {}", e.what());
    }
}

void PetsManager::AssignAnimalToJob(color_ostream& out, df::unit* animal, const std::string& job_type) {
    if (!animal) {
        logger_.Warning("Cannot assign null animal to job");
        return;
    }
    
    logger_.Info("Assigning animal {} to job type: {}", animal->id, job_type);
    
    // This would implement specific job assignments for animals
    // e.g., hunting, war training, etc.
}

std::string PetsManager::GetStatusReport() const {
    std::ostringstream report;
    
    report << "Pets Manager Status:\n";
    report << "  Total pets: " << pets_.size() << "\n";
    report << "  Stray pets: " << stray_pets_.size() << "\n";
    report << "  Pending assignments: " << pet_assignment_queue_.size() << "\n";
    report << "  Update counter: " << update_counter_ << "\n";
    
    if (!pets_.empty()) {
        int32_t milkable = 0, shearable = 0, trainable = 0, grazers = 0;
        for (const auto& [pet_id, flags] : pets_) {
            if (flags.bits.milkable) milkable++;
            if (flags.bits.shearable) shearable++;
            if (flags.bits.trainable) trainable++;
            if (flags.bits.grazer) grazers++;
        }
        
        report << "  Milkable: " << milkable << "\n";
        report << "  Shearable: " << shearable << "\n";
        report << "  Trainable: " << trainable << "\n";
        report << "  Grazers: " << grazers << "\n";
    }
    
    return report.str();
}

// Private helper methods
void PetsManager::ScanForNewPets(color_ostream& out) {
    if (!world) return;
    
    try {
        for (auto* unit : world->units.active) {
            if (!ShouldManagePet(unit)) continue;
            
            if (pets_.find(unit->id) == pets_.end()) {
                // New pet detected
                PetFlags flags = AnalyzePetCapabilities(unit);
                pets_[unit->id] = flags;
                pet_assignment_queue_.push(unit->id);
                
                logger_.Info("New pet detected: {} ({})", unit->id, Units::getReadableName(unit));
            }
        }
        
    } catch (const std::exception& e) {
        logger_.Error("Exception scanning for new pets: {}", e.what());
    }
}

void PetsManager::UpdatePetStatus(color_ostream& out, int32_t unit_id) {
    auto* unit = df::unit::find(unit_id);
    if (!unit) return;
    
    // Update pet flags if needed
    PetFlags new_flags = AnalyzePetCapabilities(unit);
    if (pets_[unit_id].whole != new_flags.whole) {
        pets_[unit_id] = new_flags;
        logger_.Debug("Updated flags for pet {}", unit_id);
    }
}

void PetsManager::CleanupDeadPets() {
    auto it = pets_.begin();
    while (it != pets_.end()) {
        auto* unit = df::unit::find(it->first);
        if (!unit || Units::isDead(unit)) {
            logger_.Debug("Removing dead pet {}", it->first);
            stray_pets_.erase(it->first);
            it = pets_.erase(it);
        } else {
            ++it;
        }
    }
}

bool PetsManager::ShouldManagePet(df::unit* unit) const {
    if (!unit || Units::isDead(unit) || Units::isOpposedToLife(unit)) {
        return false;
    }
    
    // Skip intelligent units
    if (Units::isIntelligent(unit)) {
        return false;
    }
    
    // Skip citizens, visitors, residents
    if (Units::isCitizen(unit) || Units::isVisitor(unit) || Units::isResident(unit)) {
        return false;
    }
    
    // Check if this is a domesticated animal or livestock
    if (unit->race >= 0 && unit->caste >= 0) {
        auto* creature = df::creature_raw::find(unit->race);
        if (creature && unit->caste < (int32_t)creature->caste.size()) {
            auto* caste = creature->caste[unit->caste];
            
            // Check if it's a domestic animal
            if (caste->flags.is_set(caste_raw_flags::MILKABLE) ||
                caste->flags.is_set(caste_raw_flags::GRAZER) ||
                caste->flags.is_set(caste_raw_flags::LAYS_EGGS) ||
                !caste->shearable_tissue_layer.empty() ||
                caste->flags.is_set(caste_raw_flags::TRAINABLE_HUNTING) ||
                caste->flags.is_set(caste_raw_flags::TRAINABLE_WAR)) {
                return true;
            }
        }
    }
    
    return false;
}

df::building_civzonest* PetsManager::FindSuitableZone(df::unit* pet) const {
    if (!pet || !world) return nullptr;
    
    // Look for appropriate zones for this pet
    for (auto* building : world->buildings.all) {
        if (!building || building->getType() != building_type::Civzone) continue;
        
        auto* zone = virtual_cast<df::building_civzonest>(building);
        if (!zone || zone->getBuildStage() != zone->getMaxBuildStage()) continue;
        
        // Check zone type
        if (zone->zone_flags.bits.pen_pasture) {
            // This is a pasture - suitable for grazers
            PetFlags flags = AnalyzePetCapabilities(pet);
            if (flags.bits.grazer) {
                // Check if pasture has capacity
                if (zone->assigned_units.size() < 10) { // Arbitrary capacity limit
                    return zone;
                }
            }
        }
    }
    
    return nullptr;
}

PetFlags PetsManager::AnalyzePetCapabilities(df::unit* unit) const {
    PetFlags flags;
    
    if (!unit || unit->race < 0 || unit->caste < 0) {
        return flags;
    }
    
    auto* creature = df::creature_raw::find(unit->race);
    if (!creature || unit->caste >= (int32_t)creature->caste.size()) {
        return flags;
    }
    
    auto* caste = creature->caste[unit->caste];
    
    flags.bits.milkable = caste->flags.is_set(caste_raw_flags::MILKABLE) ? 1 : 0;
    flags.bits.shearable = !caste->shearable_tissue_layer.empty() ? 1 : 0;
    flags.bits.hunts_vermin = caste->flags.is_set(caste_raw_flags::HUNTS_VERMIN) ? 1 : 0;
    flags.bits.trainable = (caste->flags.is_set(caste_raw_flags::TRAINABLE_HUNTING) || 
                           caste->flags.is_set(caste_raw_flags::TRAINABLE_WAR)) ? 1 : 0;
    flags.bits.grazer = caste->flags.is_set(caste_raw_flags::GRAZER) ? 1 : 0;
    flags.bits.lays_eggs = caste->flags.is_set(caste_raw_flags::LAYS_EGGS) ? 1 : 0;
    
    return flags;
}

} // namespace population
} // namespace dfai
