#pragma once

#include "../utils/common/common.h"
#include "../debug/logging.h"

#include <memory>
#include <unordered_set>
#include <mutex>
#include <atomic>

namespace dfai {
namespace memory {

/**
 * @brief Memory safety and leak detection system
 * 
 * Provides RAII wrappers, leak detection, and memory pool management
 * to improve memory safety throughout the DF-AI system.
 */
class MemorySafetyManager {
public:
    static MemorySafetyManager& GetInstance();
    
    // Lifecycle
    bool Initialize(color_ostream& out);
    void Shutdown();
    
    // Memory tracking
    void RegisterAllocation(void* ptr, size_t size, const std::string& type, const char* file, int line);
    void UnregisterAllocation(void* ptr);
    bool IsValidPointer(void* ptr) const;
    
    // Memory pools for common allocations
    template<typename T>
    class MemoryPool {
    public:
        MemoryPool(size_t initial_size = 100);
        ~MemoryPool();
        
        std::unique_ptr<T> Acquire();
        void Release(std::unique_ptr<T> ptr);
        
        size_t GetPoolSize() const { return pool_.size(); }
        size_t GetAllocatedCount() const { return allocated_count_.load(); }
        
    private:
        std::vector<std::unique_ptr<T>> pool_;
        std::mutex pool_mutex_;
        std::atomic<size_t> allocated_count_;
    };
    
    // RAII wrappers for automatic cleanup
    template<typename T>
    class SafePointer {
    public:
        SafePointer() : ptr_(nullptr), manager_(nullptr) {}
        SafePointer(T* ptr, MemorySafetyManager* manager) : ptr_(ptr), manager_(manager) {
            if (manager_ && ptr_) {
                manager_->RegisterAllocation(ptr_, sizeof(T), typeid(T).name(), __FILE__, __LINE__);
            }
        }
        
        ~SafePointer() {
            reset();
        }
        
        // Move semantics
        SafePointer(SafePointer&& other) noexcept : ptr_(other.ptr_), manager_(other.manager_) {
            other.ptr_ = nullptr;
            other.manager_ = nullptr;
        }
        
        SafePointer& operator=(SafePointer&& other) noexcept {
            if (this != &other) {
                reset();
                ptr_ = other.ptr_;
                manager_ = other.manager_;
                other.ptr_ = nullptr;
                other.manager_ = nullptr;
            }
            return *this;
        }
        
        // Disable copy semantics
        SafePointer(const SafePointer&) = delete;
        SafePointer& operator=(const SafePointer&) = delete;
        
        T* get() const { return ptr_; }
        T& operator*() const { return *ptr_; }
        T* operator->() const { return ptr_; }
        
        bool is_valid() const {
            return ptr_ != nullptr && (manager_ == nullptr || manager_->IsValidPointer(ptr_));
        }
        
        void reset() {
            if (ptr_ && manager_) {
                manager_->UnregisterAllocation(ptr_);
                delete ptr_;
            }
            ptr_ = nullptr;
            manager_ = nullptr;
        }
        
        T* release() {
            T* result = ptr_;
            ptr_ = nullptr;
            return result;
        }
        
    private:
        T* ptr_;
        MemorySafetyManager* manager_;
    };
    
    // Smart pointer factory methods
    template<typename T, typename... Args>
    SafePointer<T> MakeSafe(Args&&... args) {
        T* ptr = new T(std::forward<Args>(args)...);
        return SafePointer<T>(ptr, this);
    }
    
    template<typename T>
    std::shared_ptr<T> MakeShared() {
        return std::make_shared<T>();
    }
    
    template<typename T, typename... Args>
    std::shared_ptr<T> MakeShared(Args&&... args) {
        return std::make_shared<T>(std::forward<Args>(args)...);
    }
    
    // Array wrappers
    template<typename T>
    class SafeArray {
    public:
        SafeArray(size_t size) : size_(size), data_(std::make_unique<T[]>(size)) {}
        
        T& operator[](size_t index) {
            if (index >= size_) {
                throw std::out_of_range("SafeArray index out of bounds: " + std::to_string(index) + " >= " + std::to_string(size_));
            }
            return data_[index];
        }
        
        const T& operator[](size_t index) const {
            if (index >= size_) {
                throw std::out_of_range("SafeArray index out of bounds: " + std::to_string(index) + " >= " + std::to_string(size_));
            }
            return data_[index];
        }
        
        T* data() { return data_.get(); }
        const T* data() const { return data_.get(); }
        size_t size() const { return size_; }
        
        T* begin() { return data_.get(); }
        T* end() { return data_.get() + size_; }
        const T* begin() const { return data_.get(); }
        const T* end() const { return data_.get() + size_; }
        
    private:
        size_t size_;
        std::unique_ptr<T[]> data_;
    };
    
    template<typename T>
    SafeArray<T> MakeSafeArray(size_t size) {
        return SafeArray<T>(size);
    }
    
    // Memory leak detection
    struct AllocationInfo {
        size_t size;
        std::string type;
        std::string file;
        int line;
        std::chrono::steady_clock::time_point timestamp;
        
        AllocationInfo(size_t alloc_size, const std::string& alloc_type, 
                      const std::string& alloc_file, int alloc_line)
            : size(alloc_size), type(alloc_type), file(alloc_file), line(alloc_line)
            , timestamp(std::chrono::steady_clock::now()) {}
    };
    
    std::vector<AllocationInfo> GetActiveAllocations() const;
    size_t GetTotalAllocatedMemory() const;
    size_t GetAllocationCount() const;
    
    // Leak detection and reporting
    void CheckForLeaks(color_ostream& out) const;
    std::string GenerateMemoryReport() const;
    void DumpMemoryStatistics(color_ostream& out) const;
    
    // Memory debugging
    void EnableDebugMode(bool enabled) { debug_mode_ = enabled; }
    void SetAllocationLogging(bool enabled) { allocation_logging_ = enabled; }
    void SetLeakDetectionThreshold(size_t threshold) { leak_detection_threshold_ = threshold; }
    
    // Pool management
    template<typename T>
    MemoryPool<T>& GetPool() {
        static MemoryPool<T> pool;
        return pool;
    }
    
private:
    MemorySafetyManager();
    ~MemorySafetyManager();
    
    debug::Logger& logger_;
    mutable std::mutex allocations_mutex_;
    
    // Allocation tracking
    std::unordered_map<void*, AllocationInfo> active_allocations_;
    std::atomic<size_t> total_allocated_memory_;
    std::atomic<size_t> allocation_count_;
    
    // Configuration
    bool debug_mode_;
    bool allocation_logging_;
    size_t leak_detection_threshold_;
    
    // Statistics
    std::atomic<size_t> total_allocations_made_;
    std::atomic<size_t> total_deallocations_made_;
    std::atomic<size_t> peak_memory_usage_;
};

// Convenient macros for safe memory management
#define SAFE_NEW(type, ...) \
    dfai::memory::MemorySafetyManager::GetInstance().MakeSafe<type>(__VA_ARGS__)

#define SAFE_ARRAY(type, size) \
    dfai::memory::MemorySafetyManager::GetInstance().MakeSafeArray<type>(size)

#define SAFE_SHARED(type, ...) \
    dfai::memory::MemorySafetyManager::GetInstance().MakeShared<type>(__VA_ARGS__)

#define CHECK_MEMORY_LEAKS(out) \
    dfai::memory::MemorySafetyManager::GetInstance().CheckForLeaks(out)

// Pool allocation macros
#define POOL_ACQUIRE(type) \
    dfai::memory::MemorySafetyManager::GetInstance().GetPool<type>().Acquire()

#define POOL_RELEASE(type, ptr) \
    dfai::memory::MemorySafetyManager::GetInstance().GetPool<type>().Release(std::move(ptr))

} // namespace memory
} // namespace dfai
