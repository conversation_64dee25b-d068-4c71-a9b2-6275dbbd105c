{"$schema": "https://ben.lubar.me/df-ai-schemas/room-template.json", "f": [{"type": "door", "x": -1, "y": 4, "z": 0, "ignore": true}, {"type": "door", "x": -1, "y": 6, "z": 0, "ignore": true}], "r": [{"type": "corridor", "corridor_type": "corridor", "min": [1, -5, 0], "max": [3, 1, 0], "layout": [0, 1], "exits": [["generic01_corridor_any", 1, -1, 0], ["generic01_generic", -1, 1, 0], ["generic01_generic", 3, 1, 0], ["generic01_in_corridor", 0, 3, 0], ["generic01_in_corridor", 2, 3, 0], ["generic01_generic", 3, 5, 0], ["generic01_in_corridor", 1, 0, 0], ["generic01_in_corridor", 1, 6, 0]], "remove_if_unused": true}]}