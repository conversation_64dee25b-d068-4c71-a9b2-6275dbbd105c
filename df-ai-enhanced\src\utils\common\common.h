#pragma once

/**
 * Common includes and forward declarations for the DF-AI project
 */

// Standard library includes
#include <algorithm>
#include <array>
#include <chrono>
#include <cstdint>
#include <fstream>
#include <functional>
#include <iostream>
#include <list>
#include <map>
#include <memory>
#include <random>
#include <set>
#include <string>
#include <vector>

// DFHack includes
#include "Core.h"
#include "ColorText.h"
#include "Console.h"
#include "PluginManager.h"
#include "modules/Gui.h"
#include "modules/Maps.h"
#include "modules/Screen.h"
#include "modules/Units.h"

// DF structure includes
#include "df/coord.h"
#include "df/interface_key.h"
#include "df/language_name.h"
#include "df/viewscreen.h"
#include "df/world.h"

// JSON library
#include "json/json.h"

// Namespace organization
namespace df_ai {
    // Forward declarations
    class AIController;
    
    namespace core {
        class AIController;
        class LifecycleManager;
        class CommandProcessor;
    }
    
    namespace config {
        class ConfigManager;
        class SettingsValidator;
    }
    
    namespace debug {
        class DebugManager;
        class Logger;
    }
    
    namespace events {
        class EventManager;
        class CallbackManager;
        class HooksManager;
    }
    
    namespace interface {
        class CameraController;
        class TradeManager;
        class ScreenManager;
    }
    
    namespace planning {
        class PlanManager;
        class TaskManager;
        class RoomManager;
        class BlueprintManager;
    }
    
    namespace population {
        class PopulationManager;
        class MilitaryManager;
        class JobManager;
        class HealthManager;
    }
    
    namespace stocks {
        class StockManager;
        class ProductionManager;
        class EquipmentManager;
        class FoodManager;
    }
    
    namespace utils {
        // Utility functions and classes
        namespace algorithms {
            // Pathfinding, search, optimization
        }
        
        namespace data {
            // Data structures, serialization
        }
        
        namespace df {
            // DF-specific utilities
        }
    }
}

// Common using declarations
using color_ostream = DFHack::color_ostream;
using CoreSuspender = DFHack::CoreSuspender;

// Global macros
#define DF_AI_VERSION_MAJOR 1
#define DF_AI_VERSION_MINOR 0
#define DF_AI_VERSION_PATCH 0

// Debugging macros
#include "macros.h"
#define DF_AI_DEBUG_PRINT(x) DF_AI_LOG_DEBUG(x)
