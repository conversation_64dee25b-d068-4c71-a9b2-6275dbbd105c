#include "plan_manager.h"
#include "../debug/logging.h"
#include "../config/config_manager.h"

namespace dfai {
namespace planning {

PlanManager::PlanManager()
    : logger_(debug::Logger::GetInstance())
    , initialized_(false)
    , task_manager_(std::make_unique<TaskManager>())
    , blueprint_manager_(nullptr) // Will be implemented later
    , rooms_()
    , corridors_()
    , priorities_()
    , fort_entrance_(nullptr)
    , update_counter_(0)
{
    logger_.Info("PlanManager created");
}

PlanManager::~PlanManager() {
    Shutdown();
    logger_.Info("PlanManager destroyed");
}

bool PlanManager::Initialize(color_ostream& out) {
    if (initialized_) {
        logger_.Warning("PlanManager already initialized");
        return true;
    }
    
    logger_.Info("Initializing PlanManager...");
    
    try {
        // Initialize task manager
        if (!task_manager_->Initialize(out)) {
            logger_.Error("Failed to initialize task manager");
            return false;
        }
          // Initialize blueprint manager
        blueprint_manager_ = std::make_unique<BlueprintManager>();
        if (!blueprint_manager_->Initialize(out)) {
            logger_.Error("Failed to initialize blueprint manager");
            return false;
        }
        
        // Load configuration
        LoadConfiguration();
        
        // Initialize room tracking
        InitializeRoomTracking();
        
        initialized_ = true;
        logger_.Info("PlanManager initialization complete");
        return true;
        
    } catch (const std::exception& e) {
        logger_.Error("Exception during PlanManager initialization: {}", e.what());
        return false;
    }
}

void PlanManager::Shutdown() {
    if (!initialized_) {
        return;
    }
    
    logger_.Info("Shutting down PlanManager...");
    
    // Shutdown subsystems
    if (task_manager_) {
        task_manager_->Shutdown();
        task_manager_.reset();
    }
    
    blueprint_manager_.reset();
    
    // Clear data structures
    rooms_.clear();
    corridors_.clear();
    priorities_.clear();
    fort_entrance_ = nullptr;
    
    initialized_ = false;
    logger_.Info("PlanManager shutdown complete");
}

void PlanManager::Update(color_ostream& out) {
    if (!initialized_) {
        logger_.Warning("PlanManager::Update called before initialization");
        return;
    }
    
    try {
        ++update_counter_;
        
        // Update task manager
        if (task_manager_) {
            task_manager_->Update(out);
        }
        
        // Update room management
        UpdateRooms(out);
        
        // Update construction priorities
        UpdatePriorities(out);
        
        // Plan new constructions
        PlanNewConstructions(out);
        
        // Update fort layout
        UpdateFortLayout(out);
        
        // Periodic maintenance
        if (update_counter_ % 100 == 0) {
            AnalyzeFortNeeds(out);
            OptimizeLayout(out);
        }
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in PlanManager::Update: {}", e.what());
    }
}

std::string PlanManager::GetStatusReport() const {
    std::ostringstream report;
    
    report << "=== Plan Manager Status ===\n";
    report << "Initialized: " << (initialized_ ? "Yes" : "No") << "\n";
    report << "Update cycles: " << update_counter_ << "\n";
    report << "Rooms: " << rooms_.size() << "\n";
    report << "Corridors: " << corridors_.size() << "\n";
    report << "Priorities: " << priorities_.size() << "\n";
    report << "Fort entrance: " << (fort_entrance_ ? "Set" : "Not set") << "\n";
    
    if (task_manager_) {
        report << "\n--- Task Manager ---\n";
        report << task_manager_->GetStatusReport();
    }
    
    return report.str();
}

void PlanManager::AddRoom(std::unique_ptr<Room> room) {
    if (!room) {
        logger_.Warning("Attempted to add null room");
        return;
    }
    
    logger_.Info("Adding room: {}", room->GetName());
    rooms_.push_back(std::move(room));
}

void PlanManager::RemoveRoom(int32_t room_id) {
    auto it = std::find_if(rooms_.begin(), rooms_.end(),
        [room_id](const std::unique_ptr<Room>& room) {
            return room->GetId() == room_id;
        });
    
    if (it != rooms_.end()) {
        logger_.Info("Removing room: {}", (*it)->GetName());
        rooms_.erase(it);
    } else {
        logger_.Warning("Room with ID {} not found for removal", room_id);
    }
}

Room* PlanManager::FindRoom(RoomType type) const {
    for (const auto& room : rooms_) {
        if (room->GetType() == type) {
            return room.get();
        }
    }
    return nullptr;
}

std::vector<Room*> PlanManager::FindRooms(RoomType type) const {
    std::vector<Room*> result;
    
    for (const auto& room : rooms_) {
        if (room->GetType() == type) {
            result.push_back(room.get());
        }
    }
    
    return result;
}

Room* PlanManager::FindRoomAt(df::coord position) const {
    for (const auto& room : rooms_) {
        if (room->ContainsPosition(position)) {
            return room.get();
        }
    }
    return nullptr;
}

void PlanManager::AddTask(std::unique_ptr<Task> task) {
    if (task_manager_) {
        task_manager_->AddTask(std::move(task));
    } else {
        logger_.Error("Cannot add task: task manager not initialized");
    }
}

void PlanManager::AddPriorityTask(std::unique_ptr<PriorityTask> task) {
    if (task_manager_) {
        task_manager_->AddPriorityTask(std::move(task));
    } else {
        logger_.Error("Cannot add priority task: task manager not initialized");
    }
}

// Private helper methods
void PlanManager::LoadConfiguration() {
    auto& config = config::ConfigManager::GetInstance();
    
    // Load planning configuration
    // This would read various planning parameters from config
    logger_.Debug("Loading planning configuration");
}

void PlanManager::InitializeRoomTracking() {
    // Initialize room tracking systems
    logger_.Debug("Initializing room tracking");
}

void PlanManager::UpdateRooms(color_ostream& out) {
    // Update all rooms
    for (auto& room : rooms_) {
        if (room) {
            room->Update(out);
        }
    }
}

void PlanManager::UpdatePriorities(color_ostream& out) {
    // Update construction priorities
    logger_.Debug("Updating construction priorities");
}

void PlanManager::PlanNewConstructions(color_ostream& out) {
    // Plan new constructions based on needs
    logger_.Debug("Planning new constructions");
}

void PlanManager::UpdateFortLayout(color_ostream& out) {
    // Update overall fort layout
    logger_.Debug("Updating fort layout");
}

void PlanManager::AnalyzeFortNeeds(color_ostream& out) {
    // Analyze what the fort needs
    logger_.Info("Analyzing fort needs");
}

void PlanManager::OptimizeLayout(color_ostream& out) {
    // Optimize fort layout for efficiency
    logger_.Info("Optimizing fort layout");
}

} // namespace planning
} // namespace dfai
