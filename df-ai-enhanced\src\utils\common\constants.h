#pragma once

#include "types.h"

namespace df_ai {
namespace utils {

/**
 * Game constants and configuration values
 */

// Game dimensions
constexpr int32_t MAX_MAP_X = 48;
constexpr int32_t MAX_MAP_Y = 48;
constexpr int32_t MAX_MAP_Z = 256;

// Timing constants
constexpr int32_t TICKS_PER_DAY = 1200;
constexpr int32_t TICKS_PER_SEASON = 28 * TICKS_PER_DAY;
constexpr int32_t TICKS_PER_YEAR = 12 * TICKS_PER_SEASON;

// Population limits
constexpr int32_t MIN_POPULATION = 7;
constexpr int32_t MAX_DWARVES = 200;
constexpr int32_t OPTIMAL_MILITARY_SIZE = 20;

// Room size limits
constexpr int32_t MIN_ROOM_SIZE = 1;
constexpr int32_t MAX_ROOM_SIZE = 100;
constexpr int32_t OPTIMAL_BEDROOM_SIZE = 16;
constexpr int32_t OPTIMAL_DINING_HALL_SIZE = 64;

// Stock management
constexpr int32_t MIN_FOOD_STOCK = 50;
constexpr int32_t MIN_DRINK_STOCK = 100;
constexpr int32_t MIN_WOOD_STOCK = 20;
constexpr int32_t MIN_STONE_STOCK = 50;

// Task priorities
constexpr Priority PRIORITY_EMERGENCY = 1000;
constexpr Priority PRIORITY_HIGH = 100;
constexpr Priority PRIORITY_NORMAL = 50;
constexpr Priority PRIORITY_LOW = 10;
constexpr Priority PRIORITY_BACKGROUND = 1;

// AI behavior constants
constexpr int32_t MAX_IDLE_TICKS = 100;
constexpr int32_t PATHFINDING_MAX_DISTANCE = 50;
constexpr int32_t MAX_CONSTRUCTION_ATTEMPTS = 5;

// Resource thresholds
constexpr ResourceAmount RESOURCE_CRITICAL_THRESHOLD = 5;
constexpr ResourceAmount RESOURCE_LOW_THRESHOLD = 20;
constexpr ResourceAmount RESOURCE_ADEQUATE_THRESHOLD = 50;
constexpr ResourceAmount RESOURCE_ABUNDANT_THRESHOLD = 100;

// File paths
const std::string CONFIG_FILE_PATH = "df-ai-config.json";
const std::string LOG_FILE_PATH = "df-ai.log";
const std::string DEBUG_LOG_PATH = "df-ai-debug.log";
const std::string EVENTS_LOG_PATH = "df-ai-events.json";
const std::string REPORT_FILE_PATH = "df-ai-report.log";

// Blueprint paths
const std::string BLUEPRINTS_BASE_PATH = "df-ai-blueprints";
const std::string ROOMS_PATH = BLUEPRINTS_BASE_PATH + "/rooms";
const std::string PLANS_PATH = BLUEPRINTS_BASE_PATH + "/plans";

// Default configuration values
namespace defaults {
    constexpr bool RANDOM_EMBARK = false;
    constexpr bool WRITE_CONSOLE = true;
    constexpr bool WRITE_LOG = true;
    constexpr bool RECORD_MOVIE = false;
    constexpr bool NO_QUIT = false;
    constexpr bool CAMERA_ENABLED = true;
    constexpr bool FPS_METER = false;
    constexpr bool MANAGE_NOBLES = true;
    constexpr bool LOCKSTEP = false;
    constexpr bool ALLOW_PAUSE = true;
    constexpr int32_t WORLD_SIZE = 2; // Medium world
}

// Error messages
namespace errors {
    const std::string INVALID_COORDINATE = "Invalid coordinate provided";
    const std::string RESOURCE_NOT_FOUND = "Required resource not found";
    const std::string TASK_FAILED = "Task execution failed";
    const std::string CONFIG_LOAD_FAILED = "Failed to load configuration";
    const std::string BLUEPRINT_INVALID = "Blueprint validation failed";
    const std::string INSUFFICIENT_RESOURCES = "Insufficient resources for operation";
    const std::string UNIT_NOT_FOUND = "Unit not found or invalid";
    const std::string BUILDING_CONSTRUCTION_FAILED = "Building construction failed";
}

} // namespace utils
} // namespace df_ai
