#include "callback_manager.h"
#include <algorithm>
#include <sstream>
#include <iomanip>

namespace dfai {
namespace events {

CallbackManager::CallbackManager()
    : initialized_(false)
    , logger_(debug::Logger::GetInstance())
{
    logger_.Info("CallbackManager created");
}

CallbackManager::~CallbackManager() {
    if (initialized_) {
        Shutdown();
    }
}

bool CallbackManager::Initialize() {
    if (initialized_) {
        logger_.Warning("CallbackManager already initialized");
        return true;
    }

    logger_.Info("Initializing CallbackManager...");
    
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    callbacks_.clear();
    callbacks_by_type_.clear();
    
    initialized_ = true;
    logger_.Info("CallbackManager initialization complete");
    return true;
}

void CallbackManager::Shutdown() {
    if (!initialized_) {
        return;
    }

    logger_.Info("Shutting down CallbackManager...");
    
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    
    // Log final statistics
    LogCallbackStatistics();
    
    // Clear all callbacks
    callbacks_.clear();
    callbacks_by_type_.clear();
    
    initialized_ = false;
    logger_.Info("CallbackManager shutdown complete");
}

std::string CallbackManager::RegisterCallback(EventType type, EventHandler handler, 
                                             const std::string& description, int32_t priority) {
    if (!initialized_) {
        logger_.Error("CallbackManager not initialized");
        return "";
    }

    if (!ValidateCallback(type, handler)) {
        logger_.Error("Invalid callback parameters");
        return "";
    }

    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    
    std::string callback_id = GenerateCallbackId();
    auto callback_info = std::make_unique<CallbackInfo>(callback_id, type, handler, description, priority);
    
    callbacks_[callback_id] = std::move(callback_info);
    AddCallbackToTypeMap(callback_id, type);
    
    logger_.Info("Registered callback '{}' for event type {} with priority {}", 
                callback_id, static_cast<int>(type), priority);
    
    return callback_id;
}

bool CallbackManager::UnregisterCallback(const std::string& callback_id) {
    if (!initialized_) {
        logger_.Error("CallbackManager not initialized");
        return false;
    }

    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    
    auto it = callbacks_.find(callback_id);
    if (it == callbacks_.end()) {
        logger_.Warning("Callback '{}' not found for unregistration", callback_id);
        return false;
    }

    EventType type = it->second->type;
    RemoveCallbackFromTypeMap(callback_id, type);
    callbacks_.erase(it);
    
    logger_.Info("Unregistered callback '{}'", callback_id);
    return true;
}

bool CallbackManager::UnregisterCallbacksByType(EventType type) {
    if (!initialized_) {
        logger_.Error("CallbackManager not initialized");
        return false;
    }

    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    
    auto type_it = callbacks_by_type_.find(type);
    if (type_it == callbacks_by_type_.end()) {
        return true; // No callbacks of this type
    }

    std::vector<std::string> callback_ids = type_it->second;
    for (const std::string& callback_id : callback_ids) {
        callbacks_.erase(callback_id);
    }
    
    callbacks_by_type_.erase(type_it);
    
    logger_.Info("Unregistered {} callbacks for event type {}", 
                callback_ids.size(), static_cast<int>(type));
    return true;
}

void CallbackManager::UnregisterAllCallbacks() {
    if (!initialized_) {
        return;
    }

    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    
    size_t count = callbacks_.size();
    callbacks_.clear();
    callbacks_by_type_.clear();
    
    logger_.Info("Unregistered all {} callbacks", count);
}

bool CallbackManager::EnableCallback(const std::string& callback_id) {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    
    auto it = callbacks_.find(callback_id);
    if (it == callbacks_.end()) {
        return false;
    }

    it->second->enabled = true;
    logger_.Info("Enabled callback '{}'", callback_id);
    return true;
}

bool CallbackManager::DisableCallback(const std::string& callback_id) {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    
    auto it = callbacks_.find(callback_id);
    if (it == callbacks_.end()) {
        return false;
    }

    it->second->enabled = false;
    logger_.Info("Disabled callback '{}'", callback_id);
    return true;
}

bool CallbackManager::SetCallbackPriority(const std::string& callback_id, int32_t priority) {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    
    auto it = callbacks_.find(callback_id);
    if (it == callbacks_.end()) {
        return false;
    }

    it->second->priority = priority;
    logger_.Info("Set callback '{}' priority to {}", callback_id, priority);
    return true;
}

bool CallbackManager::IsCallbackRegistered(const std::string& callback_id) const {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    return callbacks_.find(callback_id) != callbacks_.end();
}

bool CallbackManager::IsCallbackEnabled(const std::string& callback_id) const {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    
    auto it = callbacks_.find(callback_id);
    return it != callbacks_.end() && it->second->enabled;
}

void CallbackManager::ExecuteCallbacks(EventType type, const EventData& event_data, color_ostream& out) {
    if (!initialized_) {
        return;
    }

    std::vector<std::string> callback_ids;
    
    {
        std::lock_guard<std::mutex> lock(callbacks_mutex_);
        
        auto type_it = callbacks_by_type_.find(type);
        if (type_it == callbacks_by_type_.end()) {
            return; // No callbacks for this event type
        }
        
        callback_ids = type_it->second;
        SortCallbacksByPriority(callback_ids);
    }

    // Execute callbacks outside of lock to avoid deadlock
    for (const std::string& callback_id : callback_ids) {
        std::unique_ptr<CallbackInfo> callback_copy;
        
        {
            std::lock_guard<std::mutex> lock(callbacks_mutex_);
            auto it = callbacks_.find(callback_id);
            if (it == callbacks_.end() || !it->second->enabled) {
                continue;
            }
            
            // Create a copy for execution
            callback_copy = std::make_unique<CallbackInfo>(*it->second);
        }
        
        if (callback_copy) {
            ExecuteSingleCallback(*callback_copy, event_data, out);
            
            // Update statistics
            {
                std::lock_guard<std::mutex> lock(callbacks_mutex_);
                auto it = callbacks_.find(callback_id);
                if (it != callbacks_.end()) {
                    it->second->execution_count = callback_copy->execution_count;
                    it->second->total_execution_time = callback_copy->total_execution_time;
                }
            }
        }
    }
}

void CallbackManager::ExecuteCallback(const std::string& callback_id, const EventData& event_data, color_ostream& out) {
    if (!initialized_) {
        return;
    }

    std::unique_ptr<CallbackInfo> callback_copy;
    
    {
        std::lock_guard<std::mutex> lock(callbacks_mutex_);
        auto it = callbacks_.find(callback_id);
        if (it == callbacks_.end() || !it->second->enabled) {
            return;
        }
        
        callback_copy = std::make_unique<CallbackInfo>(*it->second);
    }
    
    if (callback_copy) {
        ExecuteSingleCallback(*callback_copy, event_data, out);
        
        // Update statistics
        {
            std::lock_guard<std::mutex> lock(callbacks_mutex_);
            auto it = callbacks_.find(callback_id);
            if (it != callbacks_.end()) {
                it->second->execution_count = callback_copy->execution_count;
                it->second->total_execution_time = callback_copy->total_execution_time;
            }
        }
    }
}

std::vector<CallbackManager::CallbackInfo> CallbackManager::GetCallbacksForType(EventType type) const {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    
    std::vector<CallbackInfo> result;
    
    auto type_it = callbacks_by_type_.find(type);
    if (type_it != callbacks_by_type_.end()) {
        for (const std::string& callback_id : type_it->second) {
            auto it = callbacks_.find(callback_id);
            if (it != callbacks_.end()) {
                result.push_back(*it->second);
            }
        }
    }
    
    return result;
}

std::vector<CallbackManager::CallbackInfo> CallbackManager::GetAllCallbacks() const {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    
    std::vector<CallbackInfo> result;
    result.reserve(callbacks_.size());
    
    for (const auto& pair : callbacks_) {
        result.push_back(*pair.second);
    }
    
    return result;
}

CallbackManager::CallbackInfo CallbackManager::GetCallbackInfo(const std::string& callback_id) const {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    
    auto it = callbacks_.find(callback_id);
    if (it != callbacks_.end()) {
        return *it->second;
    }
    
    // Return empty callback info if not found
    return CallbackInfo("", EventType::UNKNOWN, nullptr, "", 0);
}

size_t CallbackManager::GetCallbackCount() const {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    return callbacks_.size();
}

size_t CallbackManager::GetCallbackCount(EventType type) const {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    
    auto type_it = callbacks_by_type_.find(type);
    return type_it != callbacks_by_type_.end() ? type_it->second.size() : 0;
}

// Private method implementations

std::string CallbackManager::GenerateCallbackId() {
    static std::atomic<uint64_t> counter{1};
    return "callback_" + std::to_string(counter.fetch_add(1));
}

void CallbackManager::SortCallbacksByPriority(std::vector<std::string>& callback_ids) const {
    std::sort(callback_ids.begin(), callback_ids.end(),
        [this](const std::string& a, const std::string& b) {
            auto it_a = callbacks_.find(a);
            auto it_b = callbacks_.find(b);

            if (it_a == callbacks_.end()) return false;
            if (it_b == callbacks_.end()) return true;

            return it_a->second->priority > it_b->second->priority; // Higher priority first
        });
}

bool CallbackManager::ExecuteSingleCallback(const CallbackInfo& callback, const EventData& event_data, color_ostream& out) {
    auto start_time = std::chrono::steady_clock::now();

    try {
        if (callback.handler) {
            callback.handler(event_data, out);
        }

        auto end_time = std::chrono::steady_clock::now();
        auto execution_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        // Note: This modifies the copy, statistics update happens in caller
        const_cast<CallbackInfo&>(callback).execution_count++;
        const_cast<CallbackInfo&>(callback).total_execution_time += execution_time;

        return true;

    } catch (const std::exception& e) {
        logger_.Error("Exception in callback '{}': {}", callback.id, e.what());
        return false;
    }
}

void CallbackManager::UpdateExecutionStatistics(CallbackInfo& callback, std::chrono::milliseconds execution_time) {
    callback.execution_count++;
    callback.total_execution_time += execution_time;
}

std::unique_lock<std::mutex> CallbackManager::AcquireLock() const {
    return std::unique_lock<std::mutex>(callbacks_mutex_);
}

void CallbackManager::AddCallbackToTypeMap(const std::string& callback_id, EventType type) {
    callbacks_by_type_[type].push_back(callback_id);
}

void CallbackManager::RemoveCallbackFromTypeMap(const std::string& callback_id, EventType type) {
    auto type_it = callbacks_by_type_.find(type);
    if (type_it != callbacks_by_type_.end()) {
        auto& callback_list = type_it->second;
        callback_list.erase(
            std::remove(callback_list.begin(), callback_list.end(), callback_id),
            callback_list.end()
        );

        if (callback_list.empty()) {
            callbacks_by_type_.erase(type_it);
        }
    }
}

bool CallbackManager::ValidateCallback(EventType type, const EventHandler& handler) const {
    return handler != nullptr && type != EventType::UNKNOWN;
}

bool CallbackManager::ValidateCallbackId(const std::string& callback_id) const {
    return !callback_id.empty();
}

void CallbackManager::LogCallbackStatistics() const {
    logger_.Info("Callback Statistics:");
    logger_.Info("  Total callbacks: {}", callbacks_.size());

    for (const auto& pair : callbacks_by_type_) {
        logger_.Info("  Event type {}: {} callbacks",
                    static_cast<int>(pair.first), pair.second.size());
    }
}

std::string CallbackManager::GetStatusReport() const {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);

    std::ostringstream oss;
    oss << "CallbackManager Status:\n";
    oss << "  Initialized: " << (initialized_ ? "Yes" : "No") << "\n";
    oss << "  Total callbacks: " << callbacks_.size() << "\n";
    oss << "  Event types: " << callbacks_by_type_.size() << "\n";

    for (const auto& pair : callbacks_by_type_) {
        oss << "    Type " << static_cast<int>(pair.first) << ": " << pair.second.size() << " callbacks\n";
    }

    return oss.str();
}

void CallbackManager::DumpCallbackInfo(color_ostream& out) const {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);

    out << "Registered Callbacks:\n";

    for (const auto& pair : callbacks_) {
        const CallbackInfo& info = *pair.second;
        out << "  " << info.id << ":\n";
        out << "    Type: " << static_cast<int>(info.type) << "\n";
        out << "    Description: " << info.description << "\n";
        out << "    Priority: " << info.priority << "\n";
        out << "    Enabled: " << (info.enabled ? "Yes" : "No") << "\n";
        out << "    Executions: " << info.execution_count << "\n";
        out << "    Total time: " << info.total_execution_time.count() << "ms\n";

        if (info.execution_count > 0) {
            auto avg_time = info.total_execution_time.count() / info.execution_count;
            out << "    Average time: " << avg_time << "ms\n";
        }
        out << "\n";
    }
}

// ScopedCallback implementation

ScopedCallback::ScopedCallback(CallbackManager& manager, EventType type, EventHandler handler,
                              const std::string& description, int32_t priority)
    : manager_(&manager)
    , callback_id_(manager.RegisterCallback(type, handler, description, priority))
{
}

ScopedCallback::~ScopedCallback() {
    if (manager_ && !callback_id_.empty()) {
        manager_->UnregisterCallback(callback_id_);
    }
}

ScopedCallback::ScopedCallback(ScopedCallback&& other) noexcept
    : manager_(other.manager_)
    , callback_id_(std::move(other.callback_id_))
{
    other.manager_ = nullptr;
    other.callback_id_.clear();
}

ScopedCallback& ScopedCallback::operator=(ScopedCallback&& other) noexcept {
    if (this != &other) {
        // Unregister current callback
        if (manager_ && !callback_id_.empty()) {
            manager_->UnregisterCallback(callback_id_);
        }

        // Move from other
        manager_ = other.manager_;
        callback_id_ = std::move(other.callback_id_);

        // Clear other
        other.manager_ = nullptr;
        other.callback_id_.clear();
    }
    return *this;
}

bool ScopedCallback::Enable() {
    return manager_ && manager_->EnableCallback(callback_id_);
}

bool ScopedCallback::Disable() {
    return manager_ && manager_->DisableCallback(callback_id_);
}

bool ScopedCallback::SetPriority(int32_t priority) {
    return manager_ && manager_->SetCallbackPriority(callback_id_, priority);
}

void ScopedCallback::Release() {
    manager_ = nullptr;
    callback_id_.clear();
}

void ScopedCallback::Unregister() {
    if (manager_ && !callback_id_.empty()) {
        manager_->UnregisterCallback(callback_id_);
        callback_id_.clear();
    }
}

// CallbackContext implementation

CallbackContext::CallbackContext(const EventData& event_data, color_ostream& output)
    : event_data_(event_data)
    , output_(output)
    , start_time_(std::chrono::steady_clock::now())
    , stop_requested_(false)
{
}

void CallbackContext::Log(const std::string& message) {
    output_ << "df-ai: " << message << std::endl;
}

void CallbackContext::LogError(const std::string& message) {
    output_.printerr("df-ai error: %s\n", message.c_str());
}

void CallbackContext::LogWarning(const std::string& message) {
    output_.print("df-ai warning: %s\n", message.c_str());
}

void CallbackContext::LogInfo(const std::string& message) {
    output_.print("df-ai: %s\n", message.c_str());
}

std::chrono::milliseconds CallbackContext::GetElapsedTime() const {
    auto now = std::chrono::steady_clock::now();
    return std::chrono::duration_cast<std::chrono::milliseconds>(now - start_time_);
}

} // namespace events
} // namespace dfai
