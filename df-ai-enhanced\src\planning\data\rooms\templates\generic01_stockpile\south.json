{"$schema": "https://ben.lubar.me/df-ai-schemas/room-template.json", "f": [{"type": "door", "x": 2, "y": -1, "z": 0}, {"type": "door", "x": 4, "y": -1, "z": 0}], "r": [{"type": "stockpile", "min": [-3, 1, 0], "max": [3, 2, 0], "level": 1, "layout": [0, 1], "placeholder": 0}, {"type": "stockpile", "min": [-3, 3, 0], "max": [3, 9, 0], "level": 2, "accesspath": [0], "placeholder": 0}, {"type": "stockpile", "min": [-3, 10, 0], "max": [3, 19, 0], "level": 3, "accesspath": [1], "placeholder": 0}]}