#pragma once

#include "../utils/common/common.h"
#include "../debug/logging.h"

#include <memory>
#include <vector>
#include <map>
#include <set>
#include <string>

// Forward declarations
class color_ostream;
namespace df {
    struct coord;
    struct building;
}

namespace dfai {
namespace planning {

// Room types enumeration
enum class RoomType {
    CORRIDOR,
    BARRACKS,
    BEDROOM,
    CEMETERY,
    CISTERN,
    DINING_HALL,
    FARM_PLOT,
    FURNACE,
    GARBAGE_DUMP,
    INFIRMARY,
    JAIL,
    LOCATION,
    NOBLE_ROOM,
    OUTPOST,
    PASTURE,
    PIT_CAGE,
    STOCKPILE,
    TAVERN,
    TEMPLE,
    LIBRARY,
    WORKSHOP,
    WELL,
    UNKNOWN
};

// Room status enumeration
enum class RoomStatus {
    PLANNED,
    DIGGING,
    DUG,
    CONSTRUCTING,
    FINISHED,
    ABANDONED
};

/**
 * Modern room representation
 */
class Room {
public:
    Room(int32_t id, RoomType type, const std::string& name);
    virtual ~Room();
    
    // Basic properties
    int32_t GetId() const { return id_; }
    RoomType GetType() const { return type_; }
    const std::string& GetName() const { return name_; }
    RoomStatus GetStatus() const { return status_; }
    
    void SetName(const std::string& name) { name_ = name; }
    void SetStatus(RoomStatus status);
    
    // Position and bounds
    void SetMin(df::coord min) { min_ = min; }
    void SetMax(df::coord max) { max_ = max; }
    df::coord GetMin() const { return min_; }
    df::coord GetMax() const { return max_; }
    df::coord GetCenter() const;
    int32_t GetArea() const;
    
    // Position queries
    bool ContainsPosition(df::coord position) const;
    bool IntersectsWith(const Room& other) const;
    int32_t GetDistanceTo(df::coord position) const;
    
    // Building management
    void AddBuilding(df::building* building);
    void RemoveBuilding(df::building* building);
    std::vector<df::building*> GetBuildings() const;
    
    // Accessors and connections
    void AddAccessor(std::shared_ptr<Room> room);
    void RemoveAccessor(std::shared_ptr<Room> room);
    std::vector<std::shared_ptr<Room>> GetAccessors() const;
    
    // Update and management
    virtual void Update(color_ostream& out);
    virtual void Plan(color_ostream& out);
    virtual void Dig(color_ostream& out);
    virtual void Construct(color_ostream& out);
    virtual void Furnish(color_ostream& out);
    
    // Status reporting
    std::string GetStatusReport() const;
    virtual std::string GetDetailedInfo() const;

protected:
    // Core properties
    int32_t id_;
    RoomType type_;
    std::string name_;
    RoomStatus status_;
    
    // Spatial properties
    df::coord min_;
    df::coord max_;
    
    // Associated buildings
    std::vector<df::building*> buildings_;
    
    // Room connections
    std::vector<std::weak_ptr<Room>> accessors_;
    
    // Internal state
    bool needs_update_;
    
    // Helper methods
    virtual void OnStatusChanged(RoomStatus old_status, RoomStatus new_status);
    void MarkForUpdate() { needs_update_ = true; }
};

/**
 * Specialized workshop room
 */
class WorkshopRoom : public Room {
public:
    WorkshopRoom(int32_t id, const std::string& name);
    ~WorkshopRoom() override;
    
    // Workshop-specific methods
    void SetWorkshopType(const std::string& workshop_type);
    const std::string& GetWorkshopType() const { return workshop_type_; }
    
    void Update(color_ostream& out) override;
    std::string GetDetailedInfo() const override;

private:
    std::string workshop_type_;
};

/**
 * Specialized stockpile room
 */
class StockpileRoom : public Room {
public:
    StockpileRoom(int32_t id, const std::string& name);
    ~StockpileRoom() override;
    
    // Stockpile-specific methods
    void SetStockpileType(const std::string& stockpile_type);
    const std::string& GetStockpileType() const { return stockpile_type_; }
    
    void Update(color_ostream& out) override;
    std::string GetDetailedInfo() const override;

private:
    std::string stockpile_type_;
};

/**
 * Room manager coordinates all room operations
 */
class RoomManager {
public:
    RoomManager();
    ~RoomManager();
    
    // Core lifecycle
    bool Initialize(color_ostream& out);
    void Shutdown();
    void Update(color_ostream& out);
    
    // Room creation and management
    std::shared_ptr<Room> CreateRoom(RoomType type, const std::string& name);
    std::shared_ptr<WorkshopRoom> CreateWorkshop(const std::string& name, const std::string& workshop_type);
    std::shared_ptr<StockpileRoom> CreateStockpile(const std::string& name, const std::string& stockpile_type);
    
    void DestroyRoom(int32_t room_id);
    void DestroyRoom(std::shared_ptr<Room> room);
    
    // Room queries
    std::shared_ptr<Room> GetRoom(int32_t room_id) const;
    std::vector<std::shared_ptr<Room>> GetRooms() const;
    std::vector<std::shared_ptr<Room>> GetRoomsByType(RoomType type) const;
    std::vector<std::shared_ptr<Room>> GetRoomsByStatus(RoomStatus status) const;
    std::shared_ptr<Room> FindRoomAt(df::coord position) const;
    std::vector<std::shared_ptr<Room>> FindRoomsNear(df::coord position, int32_t radius) const;
    
    // Room operations
    void PlanRoom(std::shared_ptr<Room> room, df::coord min, df::coord max);
    void StartDigging(std::shared_ptr<Room> room);
    void StartConstruction(std::shared_ptr<Room> room);
    void CompleteRoom(std::shared_ptr<Room> room);
    
    // Connection management
    void ConnectRooms(std::shared_ptr<Room> room1, std::shared_ptr<Room> room2);
    void DisconnectRooms(std::shared_ptr<Room> room1, std::shared_ptr<Room> room2);
    
    // Status reporting
    std::string GetStatusReport() const;
    void GenerateReport(std::ostream& out, bool html = false) const;

private:
    // Core state
    debug::Logger& logger_;
    bool initialized_;
    
    // Room storage
    std::map<int32_t, std::shared_ptr<Room>> rooms_;
    std::map<RoomType, std::vector<std::shared_ptr<Room>>> rooms_by_type_;
    
    // ID generation
    int32_t next_room_id_;
    
    // Helper methods
    int32_t GenerateRoomId();
    void RegisterRoom(std::shared_ptr<Room> room);
    void UnregisterRoom(std::shared_ptr<Room> room);
    void UpdateRoomIndices();
};

// Utility functions
std::string RoomTypeToString(RoomType type);
std::string RoomStatusToString(RoomStatus status);
RoomType StringToRoomType(const std::string& type_str);

} // namespace planning
} // namespace dfai
