#pragma once

#include "../utils/common/common.h"
#include "../debug/logging.h"
#include <functional>
#include <map>

namespace dfai {
namespace core {

class AIController;

/**
 * @brief Processes and handles AI commands
 * 
 * The CommandProcessor handles all command-line interface commands for the AI,
 * providing a clean interface for user interaction and system control.
 */
class CommandProcessor {
public:
    enum class CommandResult {
        SUCCESS,
        FAILURE,
        INVALID_COMMAND,
        INVALID_PARAMETERS,
        SYSTEM_NOT_READY,
        PERMISSION_DENIED
    };

    struct CommandInfo {
        std::string name;
        std::string description;
        std::string usage;
        std::function<CommandResult(color_ostream&, const std::vector<std::string>&)> handler;
        bool requires_initialization;
        bool admin_only;
    };

    explicit CommandProcessor(AIController& controller);
    ~CommandProcessor();

    // Core command processing
    CommandResult ProcessCommand(color_ostream& out, const std::string& command, 
                               const std::vector<std::string>& parameters);
    
    // Command registration
    void RegisterCommand(const CommandInfo& command_info);
    void UnregisterCommand(const std::string& command_name);
    bool IsCommandRegistered(const std::string& command_name) const;
    
    // Help and information
    void ShowHelp(color_ostream& out, const std::string& command = "") const;
    void ShowVersion(color_ostream& out) const;
    void ShowStatus(color_ostream& out) const;
    
    // Command validation
    bool ValidateCommand(const std::string& command, 
                        const std::vector<std::string>& parameters) const;
    
    // Initialization
    void Initialize();

private:
    AIController& controller_;
    debug::Logger& logger_;
    
    std::map<std::string, CommandInfo> registered_commands_;
    bool initialized_;
    
    // Built-in command handlers
    CommandResult HandleStatusCommand(color_ostream& out, const std::vector<std::string>& params);
    CommandResult HandleVersionCommand(color_ostream& out, const std::vector<std::string>& params);
    CommandResult HandleHelpCommand(color_ostream& out, const std::vector<std::string>& params);
    CommandResult HandleReportCommand(color_ostream& out, const std::vector<std::string>& params);
    CommandResult HandleConfigCommand(color_ostream& out, const std::vector<std::string>& params);
    CommandResult HandleDebugCommand(color_ostream& out, const std::vector<std::string>& params);
    CommandResult HandlePauseCommand(color_ostream& out, const std::vector<std::string>& params);
    CommandResult HandleResumeCommand(color_ostream& out, const std::vector<std::string>& params);
    CommandResult HandleShutdownCommand(color_ostream& out, const std::vector<std::string>& params);
    CommandResult HandleReloadCommand(color_ostream& out, const std::vector<std::string>& params);
    CommandResult HandleTestCommand(color_ostream& out, const std::vector<std::string>& params);
    
    // Event system commands
    CommandResult HandleEventsCommand(color_ostream& out, const std::vector<std::string>& params);
    CommandResult HandleLockstepCommand(color_ostream& out, const std::vector<std::string>& params);
    
    // Subsystem commands
    CommandResult HandlePopulationCommand(color_ostream& out, const std::vector<std::string>& params);
    CommandResult HandlePlanningCommand(color_ostream& out, const std::vector<std::string>& params);
    CommandResult HandleStocksCommand(color_ostream& out, const std::vector<std::string>& params);
    CommandResult HandleCameraCommand(color_ostream& out, const std::vector<std::string>& params);
    CommandResult HandleTradeCommand(color_ostream& out, const std::vector<std::string>& params);
    
    // Utility methods
    void RegisterBuiltinCommands();
    std::string CommandResultToString(CommandResult result) const;
    bool CheckPermissions(const CommandInfo& command) const;
    bool CheckSystemReady(const CommandInfo& command) const;
    
    // Parameter parsing helpers
    bool ParseBoolParameter(const std::string& param, bool& result) const;
    bool ParseIntParameter(const std::string& param, int& result) const;
    bool ParseFloatParameter(const std::string& param, float& result) const;
    
    // Command formatting
    void FormatCommandList(color_ostream& out) const;
    void FormatCommandHelp(color_ostream& out, const CommandInfo& command) const;
};

} // namespace core
} // namespace dfai
