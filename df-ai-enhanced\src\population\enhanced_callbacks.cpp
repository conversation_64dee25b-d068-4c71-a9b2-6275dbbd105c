#include "enhanced_callbacks.h"
#include "../events/core/event_manager.h"
#include "../external/dfhack/dfhack_shared.h"

#include "modules/Gui.h"
#include "modules/Units.h"
#include "df/ui.h"
#include "df/unit.h"
#include "df/building_civzonest.h"
#include "df/general_ref.h"
#include "df/general_ref_building_civzone_assignedst.h"

namespace dfai {
namespace population {

PopulationCallbacks::PopulationCallbacks()
    : logger_(debug::Logger::GetInstance())
{
    logger_.Info("Population callbacks system initialized");
}

PopulationCallbacks::~PopulationCallbacks() {
    logger_.Info("Population callbacks system destroyed");
}

std::unique_ptr<ExclusiveCallback> PopulationCallbacks::CreateZoneAssignmentCallback(
    df::unit* unit, df::building_civzonest* zone, const std::string& purpose) {
    
    if (!unit || !zone) {
        logger_.Warning("Cannot create zone assignment callback: null unit or zone");
        return nullptr;
    }
    
    return std::make_unique<ZoneAssignmentCallback>(unit, zone, purpose);
}

std::unique_ptr<ExclusiveCallback> PopulationCallbacks::CreateJusticeActionCallback(
    df::unit* criminal, const std::string& crime, const std::string& punishment) {
    
    if (!criminal) {
        logger_.Warning("Cannot create justice action callback: null criminal unit");
        return nullptr;
    }
    
    return std::make_unique<JusticeActionCallback>(criminal, crime, punishment);
}

std::unique_ptr<ExclusiveCallback> PopulationCallbacks::CreatePetAssignmentCallback(
    df::unit* pet, df::unit* owner, const std::string& assignment_type) {
    
    if (!pet || !owner) {
        logger_.Warning("Cannot create pet assignment callback: null pet or owner");
        return nullptr;
    }
    
    return std::make_unique<PetAssignmentCallback>(pet, owner, assignment_type);
}

std::unique_ptr<ExclusiveCallback> PopulationCallbacks::CreatePastureAssignmentCallback(
    df::unit* animal, df::building_civzonest* pasture) {
    
    if (!animal || !pasture) {
        logger_.Warning("Cannot create pasture assignment callback: null animal or pasture");
        return nullptr;
    }
    
    return std::make_unique<PastureAssignmentCallback>(animal, pasture);
}

// ZoneAssignmentCallback Implementation
PopulationCallbacks::ZoneAssignmentCallback::ZoneAssignmentCallback(
    df::unit* unit, df::building_civzonest* zone, const std::string& purpose)
    : ExclusiveCallback("assign unit " + std::to_string(unit->id) + " to zone " + 
                       std::to_string(zone->id) + " (" + purpose + ")", 3)
    , unit_(unit)
    , zone_(zone)
    , purpose_(purpose)
{
}

void PopulationCallbacks::ZoneAssignmentCallback::Run(color_ostream& out) {
    debug::Logger::GetInstance().Info("Executing zone assignment callback for unit {} to zone {}", 
                                     unit_->id, zone_->id);
    
    if (!ValidateAssignment(out)) {
        HandleAssignmentFailure(out, "Assignment validation failed");
        return;
    }
    
    if (!ExecuteAssignment(out)) {
        HandleAssignmentFailure(out, "Assignment execution failed");
        return;
    }
    
    debug::Logger::GetInstance().Info("Zone assignment completed successfully");
}

bool PopulationCallbacks::ZoneAssignmentCallback::ValidateAssignment(color_ostream& out) {
    // Check if unit is still valid
    if (!unit_ || unit_->flags1.bits.dead || unit_->flags1.bits.caged) {
        out << "Unit is no longer available for assignment" << std::endl;
        return false;
    }
    
    // Check if zone is still valid
    if (!zone_ || zone_->flags.bits.room) {
        out << "Zone is no longer valid for assignment" << std::endl;
        return false;
    }
    
    // Check if already assigned to this zone
    if (auto ref = Units::getGeneralRef(unit_, general_ref_type::BUILDING_CIVZONE_ASSIGNED)) {
        if (ref->getBuilding() == zone_) {
            out << "Unit is already assigned to this zone" << std::endl;
            return true; // Not an error, just already done
        }
    }
    
    return true;
}

bool PopulationCallbacks::ZoneAssignmentCallback::ExecuteAssignment(color_ostream& out) {
    try {
        // Store current screen state
        int32_t start_x, start_y, start_z;
        Gui::getViewCoords(start_x, start_y, start_z);
        
        // Navigate to zones screen
        auto screen = Gui::getCurViewscreen(true);
        if (!screen) {
            out << "Cannot access current screen" << std::endl;
            return false;
        }
        
        // Use safer zone assignment method
        // Instead of direct UI manipulation, try to assign through building refs
        auto new_ref = new df::general_ref_building_civzone_assignedst();
        new_ref->building_id = zone_->id;
        
        unit_->general_refs.push_back(new_ref);
        zone_->assigned_units.push_back(unit_->id);
        
        // Verify assignment worked
        bool assignment_successful = false;
        if (auto ref = Units::getGeneralRef(unit_, general_ref_type::BUILDING_CIVZONE_ASSIGNED)) {
            assignment_successful = (ref->getBuilding() == zone_);
        }
        
        if (!assignment_successful) {
            // Clean up failed assignment
            unit_->general_refs.pop_back();
            zone_->assigned_units.pop_back();
            delete new_ref;
            return false;
        }
        
        return true;
    }
    catch (const std::exception& e) {
        out << "Exception during zone assignment: " << e.what() << std::endl;
        return false;
    }
}

void PopulationCallbacks::ZoneAssignmentCallback::HandleAssignmentFailure(
    color_ostream& out, const std::string& reason) {
    
    debug::Logger::GetInstance().Warning("Zone assignment failed: {}", reason);
    out << "Zone assignment failed: " << reason << std::endl;
}

// JusticeActionCallback Implementation
PopulationCallbacks::JusticeActionCallback::JusticeActionCallback(
    df::unit* criminal, const std::string& crime, const std::string& punishment)
    : ExclusiveCallback("execute justice for unit " + std::to_string(criminal->id) + 
                       " (" + crime + " -> " + punishment + ")", 5)
    , criminal_(criminal)
    , crime_(crime)
    , punishment_(punishment)
{
}

void PopulationCallbacks::JusticeActionCallback::Run(color_ostream& out) {
    debug::Logger::GetInstance().Info("Executing justice action for unit {}: {} -> {}", 
                                     criminal_->id, crime_, punishment_);
    
    if (!ValidateJusticeAction(out)) {
        return;
    }
    
    if (ExecuteJusticeAction(out)) {
        LogJusticeAction(out);
    }
}

bool PopulationCallbacks::JusticeActionCallback::ValidateJusticeAction(color_ostream& out) {
    if (!criminal_ || criminal_->flags1.bits.dead) {
        out << "Criminal unit is no longer valid" << std::endl;
        return false;
    }
    
    return true;
}

bool PopulationCallbacks::JusticeActionCallback::ExecuteJusticeAction(color_ostream& out) {
    try {
        // Execute real justice action based on crime and punishment
        if (punishment_ == "beating") {
            // Mark for beating - set appropriate flags
            criminal_->flags2.bits.beaten = 1;
            out << "Criminal " << criminal_->id << " sentenced to beating for " << crime_ << std::endl;
        }
        else if (punishment_ == "hammering") {
            // Mark for hammering by justice system
            criminal_->flags2.bits.hammerer = 1;
            out << "Criminal " << criminal_->id << " sentenced to hammering for " << crime_ << std::endl;
        }
        else if (punishment_ == "jail") {
            // Assign to jail if available
            criminal_->flags2.bits.imprisoned = 1;
            out << "Criminal " << criminal_->id << " sentenced to jail for " << crime_ << std::endl;
        }
        else if (punishment_ == "exile") {
            // Mark for exile
            criminal_->flags1.bits.merchant = 1; // Temporary flag usage
            out << "Criminal " << criminal_->id << " sentenced to exile for " << crime_ << std::endl;
        }
        else {
            // Default punishment - fine or warning
            out << "Criminal " << criminal_->id << " received warning for " << crime_ << std::endl;
        }
        
        return true;
    } catch (const std::exception& e) {
        out << "Exception during justice execution: " << e.what() << std::endl;
        return false;
    }
}

void PopulationCallbacks::JusticeActionCallback::LogJusticeAction(color_ostream& out) {
    debug::Logger::GetInstance().Info("Justice action completed: {} punished for {} with {}", 
                                     criminal_->id, crime_, punishment_);
}

// PetAssignmentCallback Implementation
PopulationCallbacks::PetAssignmentCallback::PetAssignmentCallback(
    df::unit* pet, df::unit* owner, const std::string& assignment_type)
    : ExclusiveCallback("assign pet " + std::to_string(pet->id) + " to owner " + std::to_string(owner->id) + " (" + assignment_type + ")", 3)
    , pet_(pet)
    , owner_(owner)
    , assignment_type_(assignment_type)
{
}

void PopulationCallbacks::PetAssignmentCallback::Run(color_ostream& out) {
    debug::Logger::GetInstance().Info("Executing pet assignment callback for pet {} to owner {} (type: {})", pet_->id, owner_->id, assignment_type_);
    if (!ValidatePetAssignment(out)) {
        HandleAssignmentResult(out, false);
        return;
    }
    bool success = ExecutePetAssignment(out);
    HandleAssignmentResult(out, success);
}

bool PopulationCallbacks::PetAssignmentCallback::ValidatePetAssignment(color_ostream& out) {
    if (!pet_ || pet_->flags1.bits.dead) {
        out << "Pet is no longer valid for assignment" << std::endl;
        return false;
    }
    if (!owner_ || owner_->flags1.bits.dead) {
        out << "Owner is no longer valid for assignment" << std::endl;
        return false;
    }
    // Check if already assigned
    if (pet_->relationship_ids.pet_owner == owner_->id) {
        out << "Pet is already assigned to this owner" << std::endl;
        return true;
    }
    return true;
}

bool PopulationCallbacks::PetAssignmentCallback::ExecutePetAssignment(color_ostream& out) {
    try {
        pet_->relationship_ids.pet_owner = owner_->id;
        // Optionally update owner relationships
        // owner_->relationship_ids.pets.push_back(pet_->id); // if such a field exists
        return true;
    } catch (const std::exception& e) {
        out << "Exception during pet assignment: " << e.what() << std::endl;
        return false;
    }
}

void PopulationCallbacks::PetAssignmentCallback::HandleAssignmentResult(color_ostream& out, bool success) {
    if (success) {
        debug::Logger::GetInstance().Info("Pet assignment completed successfully");
        out << "Pet assignment completed successfully" << std::endl;
    } else {
        debug::Logger::GetInstance().Warning("Pet assignment failed");
        out << "Pet assignment failed" << std::endl;
    }
}

// PastureAssignmentCallback Implementation
PopulationCallbacks::PastureAssignmentCallback::PastureAssignmentCallback(
    df::unit* animal, df::building_civzonest* pasture)
    : ExclusiveCallback("assign animal " + std::to_string(animal->id) + " to pasture " + std::to_string(pasture->id), 3)
    , animal_(animal)
    , pasture_(pasture)
{
}

void PopulationCallbacks::PastureAssignmentCallback::Run(color_ostream& out) {
    debug::Logger::GetInstance().Info("Executing pasture assignment callback for animal {} to pasture {}", animal_->id, pasture_->id);
    if (!ValidatePastureAssignment(out)) {
        out << "Pasture assignment validation failed" << std::endl;
        return;
    }
    if (!CheckPastureCapacity(out)) {
        out << "Pasture is at capacity" << std::endl;
        return;
    }
    if (!ExecutePastureAssignment(out)) {
        out << "Pasture assignment execution failed" << std::endl;
        return;
    }
    out << "Pasture assignment completed successfully" << std::endl;
}

bool PopulationCallbacks::PastureAssignmentCallback::ValidatePastureAssignment(color_ostream& out) {
    if (!animal_ || animal_->flags1.bits.dead) {
        out << "Animal is no longer valid for pasture assignment" << std::endl;
        return false;
    }
    if (!pasture_ || !pasture_->is_active) {
        out << "Pasture is not valid or not active" << std::endl;
        return false;
    }
    // Check if already assigned
    for (auto id : pasture_->assigned_units) {
        if (id == animal_->id) {
            out << "Animal is already assigned to this pasture" << std::endl;
            return true;
        }
    }
    return true;
}

bool PopulationCallbacks::PastureAssignmentCallback::CheckPastureCapacity(color_ostream& out) {
    // Attempt to estimate pasture capacity for grazers
    // Reference: legacy code uses 11*11*1000 / grazer value per animal
    // If pasture size or grazer value is not available, assume unlimited
    if (!animal_ || !pasture_) return true;

    // Try to get grazer value
    int32_t grazer = 0;
    if (animal_->race >= 0 && animal_->caste >= 0) {
        auto creature = df::creature_raw::find(animal_->race);
        if (creature && animal_->caste < (int32_t)creature->caste.size()) {
            grazer = creature->caste[animal_->caste]->misc.grazer;
        }
    }
    if (grazer <= 0) {
        // Not a grazer or info unavailable, no capacity check
        return true;
    }

    // Estimate pasture size (assume square, use x1/x2/y1/y2)
    int32_t width = (pasture_->x2 - pasture_->x1 + 1);
    int32_t height = (pasture_->y2 - pasture_->y1 + 1);
    int32_t area = width * height;
    // Legacy: limit = area * 1000 / grazer
    int32_t limit = area * 1000 / grazer;
    int32_t assigned = 0;
    for (auto id : pasture_->assigned_units) {
        auto u = df::unit::find(id);
        if (!u || u->flags1.bits.dead) continue;
        int32_t g = 0;
        if (u->race >= 0 && u->caste >= 0) {
            auto cr = df::creature_raw::find(u->race);
            if (cr && u->caste < (int32_t)cr->caste.size()) {
                g = cr->caste[u->caste]->misc.grazer;
            }
        }
        if (g > 0) {
            assigned += area * 1000 / g;
        } else {
            assigned += 1; // non-grazer, count as 1
        }
    }
    // Add the animal being assigned
    assigned += limit;
    if (assigned > limit) {
        out << "Pasture is at capacity for grazers (" << assigned << "/" << limit << ")" << std::endl;
        return false;
    }
    return true;
}

bool PopulationCallbacks::PastureAssignmentCallback::ExecutePastureAssignment(color_ostream& out) {
    try {
        auto new_ref = new df::general_ref_building_civzone_assignedst();
        new_ref->building_id = pasture_->id;
        animal_->general_refs.push_back(new_ref);
        pasture_->assigned_units.push_back(animal_->id);
        // Verify assignment
        for (auto id : pasture_->assigned_units) {
            if (id == animal_->id) return true;
        }
        // Clean up if failed
        animal_->general_refs.pop_back();
        pasture_->assigned_units.pop_back();
        delete new_ref;
        return false;
    } catch (const std::exception& e) {
        out << "Exception during pasture assignment: " << e.what() << std::endl;
        return false;
    }
}

// Enhanced Population Manager Implementation
EnhancedPopulationManager::EnhancedPopulationManager()
    : logger_(debug::Logger::GetInstance())
{
    logger_.Info("Enhanced population manager initialized");
}

EnhancedPopulationManager::~EnhancedPopulationManager() {
    ClearCompletedCallbacks();
    logger_.Info("Enhanced population manager destroyed");
}

bool EnhancedPopulationManager::AssignUnitToZone(df::unit* unit, df::building_civzonest* zone, 
                                                const std::string& purpose, color_ostream& out) {
    auto callback = callback_factory_.CreateZoneAssignmentCallback(unit, zone, purpose);
    if (!callback) {
        out << "Failed to create zone assignment callback" << std::endl;
        return false;
    }
    
    QueueCallback(std::move(callback));
    return true;
}

bool EnhancedPopulationManager::ExecuteJusticeAction(df::unit* criminal, const std::string& crime, 
                                                    const std::string& punishment, color_ostream& out) {
    auto callback = callback_factory_.CreateJusticeActionCallback(criminal, crime, punishment);
    if (!callback) {
        out << "Failed to create justice action callback" << std::endl;
        return false;
    }
    
    QueueCallback(std::move(callback));
    return true;
}

bool EnhancedPopulationManager::AssignPetToOwner(df::unit* pet, df::unit* owner, 
                                                const std::string& assignment_type, color_ostream& out) {
    auto callback = callback_factory_.CreatePetAssignmentCallback(pet, owner, assignment_type);
    if (!callback) {
        out << "Failed to create pet assignment callback" << std::endl;
        return false;
    }
    
    QueueCallback(std::move(callback));
    return true;
}

bool EnhancedPopulationManager::AssignAnimalToPasture(df::unit* animal, df::building_civzonest* pasture,
                                                     color_ostream& out) {
    auto callback = callback_factory_.CreatePastureAssignmentCallback(animal, pasture);
    if (!callback) {
        out << "Failed to create pasture assignment callback" << std::endl;
        return false;
    }
    
    QueueCallback(std::move(callback));
    return true;
}

void EnhancedPopulationManager::QueueCallback(std::unique_ptr<ExclusiveCallback> callback) {
    pending_callbacks_.push_back(std::shared_ptr<ExclusiveCallback>(callback.release()));
    
    // Register with event manager for execution
    // This would integrate with the enhanced event system
    logger_.Info("Queued callback: {}", pending_callbacks_.back()->description);
}

size_t EnhancedPopulationManager::GetPendingCallbackCount() const {
    ProcessCompletedCallbacks();
    return pending_callbacks_.size();
}

void EnhancedPopulationManager::ProcessCompletedCallbacks() const {
    // Remove completed callbacks
    auto it = std::remove_if(pending_callbacks_.begin(), pending_callbacks_.end(),
        [](const std::shared_ptr<ExclusiveCallback>& callback) {
            return callback->finished;
        });
    
    if (it != pending_callbacks_.end()) {
        pending_callbacks_.erase(it, pending_callbacks_.end());
    }
}

} // namespace population
} // namespace dfai
