{"$schema": "https://ben.lubar.me/df-ai-schemas/room-template.json", "f": [{"type": "door", "x": 3, "y": -1, "z": 0}, {"type": "armor_stand", "x": 5, "y": 0, "z": 0, "has_users": 1, "ignore": true}, {"type": "bed", "x": 6, "y": 0, "z": 0, "has_users": 1, "ignore": true}, {"type": "cabinet", "x": 0, "y": 0, "z": 0, "has_users": 1, "ignore": true}, {"type": "chest", "x": 1, "y": 0, "z": 0, "has_users": 1, "ignore": true}, {"type": "armor_stand", "x": 5, "y": 1, "z": 0, "has_users": 1, "ignore": true}, {"type": "bed", "x": 6, "y": 1, "z": 0, "has_users": 1, "ignore": true}, {"type": "cabinet", "x": 0, "y": 1, "z": 0, "has_users": 1, "ignore": true}, {"type": "chest", "x": 1, "y": 1, "z": 0, "has_users": 1, "ignore": true}, {"type": "armor_stand", "x": 5, "y": 2, "z": 0, "has_users": 1, "ignore": true}, {"type": "bed", "x": 6, "y": 2, "z": 0, "has_users": 1, "ignore": true}, {"type": "cabinet", "x": 0, "y": 2, "z": 0, "has_users": 1, "ignore": true}, {"type": "chest", "x": 1, "y": 2, "z": 0, "has_users": 1, "ignore": true}, {"type": "armor_stand", "x": 5, "y": 3, "z": 0, "has_users": 1, "ignore": true}, {"type": "bed", "x": 6, "y": 3, "z": 0, "has_users": 1, "ignore": true}, {"type": "cabinet", "x": 0, "y": 3, "z": 0, "has_users": 1, "ignore": true}, {"type": "chest", "x": 1, "y": 3, "z": 0, "has_users": 1, "ignore": true}, {"type": "armor_stand", "x": 5, "y": 4, "z": 0, "has_users": 1, "ignore": true}, {"type": "bed", "x": 6, "y": 4, "z": 0, "has_users": 1, "ignore": true}, {"type": "cabinet", "x": 0, "y": 4, "z": 0, "has_users": 1, "ignore": true}, {"type": "chest", "x": 1, "y": 4, "z": 0, "has_users": 1, "ignore": true}, {"type": "armor_stand", "x": 5, "y": 5, "z": 0, "has_users": 1, "ignore": true}, {"type": "bed", "x": 6, "y": 5, "z": 0, "has_users": 1, "ignore": true}, {"type": "cabinet", "x": 0, "y": 5, "z": 0, "has_users": 1, "ignore": true}, {"type": "chest", "x": 1, "y": 5, "z": 0, "has_users": 1, "ignore": true}, {"type": "armor_stand", "x": 5, "y": 6, "z": 0, "has_users": 1, "ignore": true}, {"type": "bed", "x": 6, "y": 6, "z": 0, "has_users": 1, "ignore": true}, {"type": "cabinet", "x": 0, "y": 6, "z": 0, "has_users": 1, "ignore": true}, {"type": "chest", "x": 1, "y": 6, "z": 0, "has_users": 1, "ignore": true}, {"type": "armor_stand", "x": 5, "y": 7, "z": 0, "has_users": 1, "ignore": true}, {"type": "bed", "x": 6, "y": 7, "z": 0, "has_users": 1, "ignore": true}, {"type": "cabinet", "x": 0, "y": 7, "z": 0, "has_users": 1, "ignore": true}, {"type": "chest", "x": 1, "y": 7, "z": 0, "has_users": 1, "ignore": true}, {"type": "weapon_rack", "x": 4, "y": 7, "z": 0, "has_users": 5, "makeroom": true}, {"type": "weapon_rack", "x": 2, "y": 7, "z": 0, "has_users": 5, "ignore": true}, {"type": "archery_target", "x": 3, "y": 7, "z": 0, "has_users": 10, "ignore": true}], "r": [{"type": "barracks", "min": [-3, 1, 0], "max": [3, 8, 0], "layout": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35]}]}