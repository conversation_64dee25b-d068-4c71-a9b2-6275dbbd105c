#pragma once

#include "../utils/common/common.h"
#include "../debug/logging.h"
#include "../core/ai_controller.h"

#include <string>
#include <vector>
#include <functional>
#include <chrono>

namespace dfai {
namespace testing {

/**
 * @brief Integration testing framework for DF-AI Enhanced
 * 
 * Provides comprehensive testing for:
 * - System integration between modules
 * - Performance benchmarking
 * - Memory safety validation
 * - Error handling verification
 * - Thread safety confirmation
 */
class IntegrationTestFramework {
public:
    IntegrationTestFramework();
    ~IntegrationTestFramework();
    
    // Test execution
    bool RunAllTests(color_ostream& out);
    bool RunTestSuite(const std::string& suite_name, color_ostream& out);
    bool RunSingleTest(const std::string& test_name, color_ostream& out);
    
    // Test results
    struct TestResult {
        std::string name;
        bool passed;
        std::chrono::milliseconds execution_time;
        std::string error_message;
        std::string performance_notes;
    };
    
    std::vector<TestResult> GetTestResults() const;
    void GenerateReport(std::ostream& out, bool html = false) const;
    
    // Performance benchmarking
    struct BenchmarkResult {
        std::string operation;
        double average_time_ms;
        double min_time_ms;
        double max_time_ms;
        size_t iterations;
        double standard_deviation;
    };
    
    std::vector<BenchmarkResult> RunPerformanceBenchmarks(color_ostream& out);
    
private:
    debug::Logger& logger_;
    std::vector<TestResult> test_results_;
    
    // Test suites
    bool RunCoreSystemTests(color_ostream& out);
    bool RunEventSystemTests(color_ostream& out);
    bool RunResourceManagementTests(color_ostream& out);
    bool RunTaskSchedulingTests(color_ostream& out);
    bool RunMemorySafetyTests(color_ostream& out);
    bool RunConfigurationTests(color_ostream& out);
    bool RunPerformanceTests(color_ostream& out);
    bool RunThreadSafetyTests(color_ostream& out);
    
    // Individual test cases
    bool TestAIControllerInitialization(color_ostream& out);
    bool TestEventManagerIntegration(color_ostream& out);
    bool TestResourceManagerIntegration(color_ostream& out);
    bool TestTaskSchedulerIntegration(color_ostream& out);
    bool TestMemoryManagementIntegration(color_ostream& out);
    bool TestConfigurationValidation(color_ostream& out);
    bool TestErrorHandlingAndRecovery(color_ostream& out);
    bool TestPerformanceMonitoringIntegration(color_ostream& out);
    bool TestConcurrentAccess(color_ostream& out);
    bool TestSystemHealthMonitoring(color_ostream& out);
    
    // Performance benchmarks
    BenchmarkResult BenchmarkEventProcessing();
    BenchmarkResult BenchmarkTaskExecution();
    BenchmarkResult BenchmarkResourceAllocation();
    BenchmarkResult BenchmarkMemoryOperations();
    BenchmarkResult BenchmarkConfigurationLoading();
    
    // Utility methods
    TestResult CreateTestResult(const std::string& name, bool passed, 
                               std::chrono::milliseconds time,
                               const std::string& error = "",
                               const std::string& perf_notes = "");
    
    template<typename TestFunc>
    TestResult RunTest(const std::string& name, TestFunc&& test_func, color_ostream& out);
    
    bool ValidateSystemIntegration(color_ostream& out);
    bool CheckMemoryLeaks();
    bool ValidateThreadSafety();
    void LogTestProgress(const std::string& message);
};

/**
 * @brief System integration validator
 * 
 * Validates that all enhanced systems work together properly
 */
class SystemIntegrationValidator {
public:
    SystemIntegrationValidator();
    ~SystemIntegrationValidator();
    
    // Integration validation
    bool ValidateFullSystemIntegration(color_ostream& out);
    bool ValidateSubsystemCommunication(color_ostream& out);
    bool ValidateEventFlowIntegrity(color_ostream& out);
    bool ValidateResourceSharingConsistency(color_ostream& out);
    bool ValidateErrorPropagationCorrectness(color_ostream& out);
    
    // Performance validation
    bool ValidatePerformanceExpectations(color_ostream& out);
    bool ValidateMemoryEfficiency(color_ostream& out);
    bool ValidateResponseTimes(color_ostream& out);
    
    // Reliability validation
    bool ValidateSystemStability(color_ostream& out);
    bool ValidateErrorRecovery(color_ostream& out);
    bool ValidateGracefulDegradation(color_ostream& out);
    
    // Report generation
    void GenerateIntegrationReport(std::ostream& out, bool detailed = false) const;
    
private:
    debug::Logger& logger_;
    std::vector<std::string> validation_errors_;
    std::vector<std::string> performance_warnings_;
    
    // Validation helpers
    bool CheckAIControllerIntegration();
    bool CheckEventManagerIntegration();
    bool CheckResourceManagerIntegration();
    bool CheckTaskSchedulerIntegration();
    bool CheckMemoryManagerIntegration();
    bool CheckPerformanceMonitorIntegration();
    
    // Performance helpers
    bool CheckMemoryUsageExpectations();
    bool CheckCPUUsageExpectations();
    bool CheckResponseTimeExpectations();
    
    // Reliability helpers
    bool SimulateErrorConditions();
    bool TestRecoveryMechanisms();
    bool TestGracefulShutdown();
    
    void LogValidationError(const std::string& error);
    void LogPerformanceWarning(const std::string& warning);
};

/**
 * @brief Automated test runner for CI/CD integration
 */
class AutomatedTestRunner {
public:
    struct TestConfiguration {
        bool run_integration_tests = true;
        bool run_performance_tests = true;
        bool run_memory_tests = true;
        bool run_thread_safety_tests = true;
        bool generate_detailed_reports = false;
        std::string report_output_path = "test_results";
        int32_t performance_iterations = 100;
        std::chrono::seconds max_test_duration{300}; // 5 minutes
    };
    
    AutomatedTestRunner(const TestConfiguration& config);
    ~AutomatedTestRunner();
    
    // Automated execution
    bool RunAutomatedTestSuite(color_ostream& out);
    bool GenerateTestReports(const std::string& output_dir) const;
    
    // CI/CD integration
    int GetExitCode() const; // 0 = success, non-zero = failure
    std::string GetSummaryReport() const;
    
private:
    TestConfiguration config_;
    IntegrationTestFramework test_framework_;
    SystemIntegrationValidator integration_validator_;
    debug::Logger& logger_;
    
    bool all_tests_passed_;
    std::string summary_report_;
    
    void GenerateJUnitReport(const std::string& output_dir) const;
    void GenerateHTMLReport(const std::string& output_dir) const;
    void GenerateMarkdownReport(const std::string& output_dir) const;
};

} // namespace testing
} // namespace dfai
