#include "lifecycle_manager.h"
#include "ai_controller.h"
#include "../config/config_manager.h"
#include "../events/core/event_manager.h"

namespace dfai {
namespace core {

LifecycleManager::LifecycleManager(AIController& controller)
    : controller_(controller)
    , logger_(debug::Logger::GetInstance())
    , current_state_(State::UNINITIALIZED)
    , previous_state_(State::UNINITIALIZED)
    , current_phase_(InitializationPhase::CONFIGURATION)
{
    logger_.Info("LifecycleManager created");
}

LifecycleManager::~LifecycleManager() {
    if (current_state_ != State::SHUTDOWN && current_state_ != State::UNINITIALIZED) {
        logger_.Warning("LifecycleManager destroyed without proper shutdown");
        Shutdown();
    }
}

bool LifecycleManager::Initialize(color_ostream& out) {
    if (current_state_ != State::UNINITIALIZED) {
        logger_.Warning("Attempted to initialize already initialized LifecycleManager");
        return current_state_ == State::RUNNING;
    }

    logger_.Info("Starting AI system initialization...");
    initialization_start_ = std::chrono::steady_clock::now();
    
    if (!TransitionToState(State::INITIALIZING, out)) {
        return false;
    }

    try {
        // Phase 1: Configuration
        current_phase_ = InitializationPhase::CONFIGURATION;
        if (!InitializeConfiguration(out)) {
            HandleInitializationError("Configuration", "Failed to initialize configuration");
            return false;
        }

        // Phase 2: DFHack Integration
        current_phase_ = InitializationPhase::DFHACK_INTEGRATION;
        if (!InitializeDFHackIntegration(out)) {
            HandleInitializationError("DFHack Integration", "Failed to initialize DFHack integration");
            return false;
        }

        // Phase 3: Subsystems
        current_phase_ = InitializationPhase::SUBSYSTEMS;
        if (!InitializeSubsystems(out)) {
            HandleInitializationError("Subsystems", "Failed to initialize subsystems");
            return false;
        }

        // Phase 4: Event Handlers
        current_phase_ = InitializationPhase::EVENT_HANDLERS;
        if (!InitializeEventHandlers(out)) {
            HandleInitializationError("Event Handlers", "Failed to initialize event handlers");
            return false;
        }

        // Phase 5: Validation
        current_phase_ = InitializationPhase::VALIDATION;
        if (!ValidateInitialization(out)) {
            HandleInitializationError("Validation", "System validation failed");
            return false;
        }

        current_phase_ = InitializationPhase::COMPLETE;
        
        if (!TransitionToState(State::RUNNING, out)) {
            return false;
        }

        auto duration = std::chrono::steady_clock::now() - initialization_start_;
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
        
        logger_.Info("AI system initialization completed successfully in {}ms", ms);
        out << "df-ai: Initialization completed successfully in " << ms << "ms" << std::endl;
        
        return true;

    } catch (const std::exception& e) {
        HandleInitializationError("Exception", e.what());
        return false;
    }
}

void LifecycleManager::Shutdown() {
    if (current_state_ == State::SHUTDOWN || current_state_ == State::UNINITIALIZED) {
        return;
    }

    logger_.Info("Starting AI system shutdown...");
    
    try {
        TransitionToState(State::SHUTTING_DOWN, color_ostream::null());
        
        ShutdownEventHandlers();
        ShutdownSubsystems();
        ShutdownDFHackIntegration();
        CleanupResources();
        
        TransitionToState(State::SHUTDOWN, color_ostream::null());
        logger_.Info("AI system shutdown completed");
        
    } catch (const std::exception& e) {
        logger_.Error("Exception during shutdown: {}", e.what());
        current_state_ = State::ERROR_STATE;
    }
}

bool LifecycleManager::TransitionToState(State new_state, color_ostream& out) {
    if (!ValidateStateTransition(current_state_, new_state)) {
        logger_.Error("Invalid state transition from {} to {}", 
                     StateToString(current_state_), StateToString(new_state));
        return false;
    }

    State old_state = current_state_;
    previous_state_ = current_state_;
    current_state_ = new_state;
    last_state_change_ = std::chrono::steady_clock::now();
    
    OnStateChanged(old_state, new_state);
    
    logger_.Info("State transition: {} -> {}", StateToString(old_state), StateToString(new_state));
    
    return true;
}

bool LifecycleManager::CanTransitionTo(State target_state) const {
    return ValidateStateTransition(current_state_, target_state);
}

bool LifecycleManager::Pause(color_ostream& out) {
    if (current_state_ != State::RUNNING) {
        logger_.Warning("Cannot pause: system not running (current state: {})", 
                       StateToString(current_state_));
        return false;
    }

    return TransitionToState(State::PAUSED, out);
}

bool LifecycleManager::Resume(color_ostream& out) {
    if (current_state_ != State::PAUSED) {
        logger_.Warning("Cannot resume: system not paused (current state: {})", 
                       StateToString(current_state_));
        return false;
    }

    return TransitionToState(State::RUNNING, out);
}

void LifecycleManager::EmergencyShutdown(const std::string& reason) {
    logger_.Error("Emergency shutdown triggered: {}", reason);
    
    current_state_ = State::ERROR_STATE;
    
    try {
        ShutdownEventHandlers();
        ShutdownSubsystems();
        CleanupResources();
    } catch (const std::exception& e) {
        logger_.Error("Exception during emergency shutdown: {}", e.what());
    }
    
    current_state_ = State::SHUTDOWN;
}

bool LifecycleManager::AttemptRecovery(color_ostream& out) {
    if (current_state_ != State::ERROR_STATE) {
        logger_.Warning("Recovery attempted but system not in error state");
        return false;
    }

    logger_.Info("Attempting system recovery...");
    
    try {
        // Reset to uninitialized state
        current_state_ = State::UNINITIALIZED;
        current_phase_ = InitializationPhase::CONFIGURATION;
        
        // Attempt reinitialization
        return Initialize(out);
        
    } catch (const std::exception& e) {
        logger_.Error("Recovery failed: {}", e.what());
        current_state_ = State::ERROR_STATE;
        return false;
    }
}

std::string LifecycleManager::GetStateDescription() const {
    std::string desc = StateToString(current_state_);
    
    if (current_state_ == State::INITIALIZING) {
        desc += " (" + PhaseToString(current_phase_) + ")";
    }
    
    return desc;
}

std::string LifecycleManager::GetInitializationProgress() const {
    if (current_state_ != State::INITIALIZING) {
        return current_state_ == State::RUNNING ? "Complete" : "Not initializing";
    }
    
    return PhaseToString(current_phase_);
}

double LifecycleManager::GetInitializationProgress() const {
    if (current_state_ == State::RUNNING) {
        return 1.0;
    }
    
    if (current_state_ != State::INITIALIZING) {
        return 0.0;
    }
    
    switch (current_phase_) {
        case InitializationPhase::CONFIGURATION: return 0.2;
        case InitializationPhase::DFHACK_INTEGRATION: return 0.4;
        case InitializationPhase::SUBSYSTEMS: return 0.6;
        case InitializationPhase::EVENT_HANDLERS: return 0.8;
        case InitializationPhase::VALIDATION: return 0.9;
        case InitializationPhase::COMPLETE: return 1.0;
        default: return 0.0;
    }
}

void LifecycleManager::RegisterStateChangeCallback(std::function<void(State, State)> callback) {
    state_change_callbacks_.push_back(callback);
}

void LifecycleManager::UnregisterStateChangeCallbacks() {
    state_change_callbacks_.clear();
}

// Private implementation methods

bool LifecycleManager::InitializeConfiguration(color_ostream& out) {
    logger_.Info("Initializing configuration...");

    // Configuration initialization will be handled by ConfigManager
    // This is a placeholder for now
    return true;
}

bool LifecycleManager::InitializeDFHackIntegration(color_ostream& out) {
    logger_.Info("Initializing DFHack integration...");

    // DFHack integration initialization
    // This will be implemented when we have proper DFHack setup
    return true;
}

bool LifecycleManager::InitializeSubsystems(color_ostream& out) {
    logger_.Info("Initializing subsystems...");

    // Subsystem initialization will be delegated to AIController
    return true;
}

bool LifecycleManager::InitializeEventHandlers(color_ostream& out) {
    logger_.Info("Initializing event handlers...");

    // Event handler setup will be handled by EventManager
    return true;
}

bool LifecycleManager::ValidateInitialization(color_ostream& out) {
    logger_.Info("Validating initialization...");

    // Validation logic will be implemented
    return true;
}

void LifecycleManager::ShutdownEventHandlers() {
    logger_.Info("Shutting down event handlers...");
}

void LifecycleManager::ShutdownSubsystems() {
    logger_.Info("Shutting down subsystems...");
}

void LifecycleManager::ShutdownDFHackIntegration() {
    logger_.Info("Shutting down DFHack integration...");
}

void LifecycleManager::CleanupResources() {
    logger_.Info("Cleaning up resources...");
}

bool LifecycleManager::ValidateStateTransition(State from, State to) const {
    // Define valid state transitions
    switch (from) {
        case State::UNINITIALIZED:
            return to == State::INITIALIZING;
        case State::INITIALIZING:
            return to == State::RUNNING || to == State::ERROR_STATE;
        case State::RUNNING:
            return to == State::PAUSED || to == State::SHUTTING_DOWN || to == State::ERROR_STATE;
        case State::PAUSED:
            return to == State::RUNNING || to == State::SHUTTING_DOWN || to == State::ERROR_STATE;
        case State::SHUTTING_DOWN:
            return to == State::SHUTDOWN || to == State::ERROR_STATE;
        case State::SHUTDOWN:
            return to == State::UNINITIALIZED; // Allow restart
        case State::ERROR_STATE:
            return to == State::UNINITIALIZED || to == State::SHUTDOWN;
        default:
            return false;
    }
}

void LifecycleManager::OnStateChanged(State old_state, State new_state) {
    NotifyStateChangeCallbacks(old_state, new_state);
}

void LifecycleManager::NotifyStateChangeCallbacks(State old_state, State new_state) {
    for (auto& callback : state_change_callbacks_) {
        try {
            callback(old_state, new_state);
        } catch (const std::exception& e) {
            logger_.Error("Exception in state change callback: {}", e.what());
        }
    }
}

void LifecycleManager::HandleInitializationError(const std::string& phase, const std::string& error) {
    logger_.Error("Initialization failed in phase {}: {}", phase, error);
    current_state_ = State::ERROR_STATE;
}

void LifecycleManager::HandleRuntimeError(const std::string& error) {
    logger_.Error("Runtime error: {}", error);
    current_state_ = State::ERROR_STATE;
}

std::string LifecycleManager::StateToString(State state) const {
    switch (state) {
        case State::UNINITIALIZED: return "Uninitialized";
        case State::INITIALIZING: return "Initializing";
        case State::RUNNING: return "Running";
        case State::PAUSED: return "Paused";
        case State::SHUTTING_DOWN: return "Shutting Down";
        case State::SHUTDOWN: return "Shutdown";
        case State::ERROR_STATE: return "Error";
        default: return "Unknown";
    }
}

std::string LifecycleManager::PhaseToString(InitializationPhase phase) const {
    switch (phase) {
        case InitializationPhase::CONFIGURATION: return "Configuration";
        case InitializationPhase::DFHACK_INTEGRATION: return "DFHack Integration";
        case InitializationPhase::SUBSYSTEMS: return "Subsystems";
        case InitializationPhase::EVENT_HANDLERS: return "Event Handlers";
        case InitializationPhase::VALIDATION: return "Validation";
        case InitializationPhase::COMPLETE: return "Complete";
        default: return "Unknown";
    }
}

bool LifecycleManager::IsValidState(State state) const {
    return state >= State::UNINITIALIZED && state <= State::ERROR_STATE;
}

} // namespace core
} // namespace dfai
