#include "integration_tests.h"
#include "../core/ai_controller.h"
#include "../events/core/threadsafe_event_manager.h"
#include "../resources/intelligent_resource_manager.h"
#include "../scheduling/advanced_task_scheduler.h"
#include "../utils/memory/memory_safety_manager.h"
#include "../utils/performance/performance_monitor.h"
#include "../config/advanced_validator.h"

#include <thread>
#include <future>
#include <iomanip>
#include <sstream>

namespace dfai {
namespace testing {

IntegrationTestFramework::IntegrationTestFramework()
    : logger_(debug::Logger::GetInstance())
{
    logger_.Info("Integration test framework initialized");
}

IntegrationTestFramework::~IntegrationTestFramework() {
    logger_.Info("Integration test framework destroyed");
}

bool IntegrationTestFramework::RunAllTests(color_ostream& out) {
    logger_.Info("Starting comprehensive integration test suite");
    test_results_.clear();
    
    bool all_passed = true;
    
    // Run all test suites
    all_passed &= RunCoreSystemTests(out);
    all_passed &= RunEventSystemTests(out);
    all_passed &= RunResourceManagementTests(out);
    all_passed &= RunTaskSchedulingTests(out);
    all_passed &= RunMemorySafetyTests(out);
    all_passed &= RunConfigurationTests(out);
    all_passed &= RunPerformanceTests(out);
    all_passed &= RunThreadSafetyTests(out);
    
    // Final integration validation
    all_passed &= ValidateSystemIntegration(out);
    
    out << "Integration tests completed. " << test_results_.size() 
        << " tests run, " << (all_passed ? "ALL PASSED" : "SOME FAILED") << std::endl;
    
    return all_passed;
}

bool IntegrationTestFramework::RunCoreSystemTests(color_ostream& out) {
    logger_.Info("Running core system integration tests");
    
    bool all_passed = true;
    
    // Test AI Controller initialization and integration
    all_passed &= TestAIControllerInitialization(out);
    
    // Test system health monitoring
    all_passed &= TestSystemHealthMonitoring(out);
    
    // Test error handling and recovery
    all_passed &= TestErrorHandlingAndRecovery(out);
    
    return all_passed;
}

bool IntegrationTestFramework::TestAIControllerInitialization(color_ostream& out) {
    return RunTest("AIController Initialization", [&](color_ostream& out) -> bool {
        // Test AI controller singleton access
        auto& ai_controller = core::AIController::GetInstance();
        
        // Test initialization
        auto init_result = ai_controller.Initialize(out);
        if (init_result != core::AIController::InitializationResult::SUCCESS &&
            init_result != core::AIController::InitializationResult::ALREADY_INITIALIZED) {
            return false;
        }
        
        // Test state management
        auto state = ai_controller.GetCurrentGameState();
        if (state == core::AIController::GameState::INVALID) {
            return false;
        }
        
        // Test subsystem access
        auto* plan_manager = ai_controller.GetPlanManager();
        auto* population_manager = ai_controller.GetPopulationManager();
        auto* stocks_manager = ai_controller.GetStocksManager();
        auto* interface_manager = ai_controller.GetInterfaceManager();
        
        return plan_manager != nullptr && 
               population_manager != nullptr && 
               stocks_manager != nullptr && 
               interface_manager != nullptr;
    }, out);
}

bool IntegrationTestFramework::TestEventManagerIntegration(color_ostream& out) {
    return RunTest("Event Manager Integration", [&](color_ostream& out) -> bool {
        // Create thread-safe event manager
        events::ThreadSafeEventManager event_manager;
        
        // Test initialization
        if (!event_manager.Initialize(out)) {
            return false;
        }
        
        // Test event registration and dispatch
        bool callback_executed = false;
        auto callback_id = event_manager.RegisterCallback(
            events::EventType::TASK_COMPLETED,
            [&callback_executed](const events::EventData& event, color_ostream& out) {
                callback_executed = true;
            },
            "Test callback"
        );
        
        // Dispatch test event
        events::EventData test_event;
        test_event.type = events::EventType::TASK_COMPLETED;
        test_event.timestamp = std::chrono::steady_clock::now();
        test_event.description = "Test event";
        
        event_manager.DispatchEvent(test_event, out);
        
        // Wait for processing
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        event_manager.Shutdown();
        
        return callback_executed;
    }, out);
}

bool IntegrationTestFramework::TestResourceManagerIntegration(color_ostream& out) {
    return RunTest("Resource Manager Integration", [&](color_ostream& out) -> bool {
        resources::IntelligentResourceManager resource_manager;
        
        // Test initialization
        if (!resource_manager.Initialize(out)) {
            return false;
        }
        
        // Test resource allocation
        auto allocation_result = resource_manager.AllocateResource(
            resources::ResourceType::MATERIAL, "stone", 100
        );
        
        if (!allocation_result.success) {
            return false;
        }
        
        // Test resource availability check  
        auto availability = resource_manager.CheckResourceAvailability(
            resources::ResourceType::MATERIAL, "stone"
        );
        
        // Test resource release
        bool release_result = resource_manager.ReleaseResource(
            allocation_result.allocation_id
        );
        
        resource_manager.Shutdown();
        
        return availability.available_amount > 0 && release_result;
    }, out);
}

bool IntegrationTestFramework::TestTaskSchedulerIntegration(color_ostream& out) {
    return RunTest("Task Scheduler Integration", [&](color_ostream& out) -> bool {
        scheduling::AdvancedTaskScheduler scheduler;
        
        // Test initialization
        if (!scheduler.Initialize(out)) {
            return false;
        }
        
        // Create test task
        scheduling::TaskDefinition task_def;
        task_def.name = "Integration Test Task";
        task_def.priority = scheduling::TaskPriority::NORMAL;
        task_def.execution_function = [](scheduling::TaskContext& ctx) -> scheduling::TaskResult {
            // Simulate work
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            return scheduling::TaskResult::SUCCESS;
        };
        
        // Schedule task
        auto task_id = scheduler.ScheduleTask(task_def);
        if (task_id == scheduling::INVALID_TASK_ID) {
            return false;
        }
        
        // Wait for completion
        bool completed = false;
        for (int i = 0; i < 100 && !completed; ++i) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            auto status = scheduler.GetTaskStatus(task_id);
            completed = (status == scheduling::TaskStatus::COMPLETED);
        }
        
        scheduler.Shutdown();
        
        return completed;
    }, out);
}

bool IntegrationTestFramework::TestMemoryManagementIntegration(color_ostream& out) {
    return RunTest("Memory Management Integration", [&](color_ostream& out) -> bool {
        utils::MemorySafetyManager memory_manager;
        
        // Test initialization
        if (!memory_manager.Initialize()) {
            return false;
        }
        
        // Test safe pointer creation
        auto safe_ptr = memory_manager.CreateSafePointer<int>(42);
        if (!safe_ptr || *safe_ptr != 42) {
            return false;
        }
        
        // Test memory pool operations
        auto pool = memory_manager.CreateMemoryPool("test_pool", sizeof(int), 10);
        if (!pool) {
            return false;
        }
        
        void* allocated = memory_manager.AllocateFromPool("test_pool");
        if (!allocated) {
            return false;
        }
        
        memory_manager.DeallocateFromPool("test_pool", allocated);
        
        // Test leak detection (should show no leaks)
        auto leak_report = memory_manager.GenerateLeakReport();
        
        memory_manager.Shutdown();
        
        return leak_report.total_leaks == 0;
    }, out);
}

bool IntegrationTestFramework::ValidateSystemIntegration(color_ostream& out) {
    return RunTest("Full System Integration", [&](color_ostream& out) -> bool {
        SystemIntegrationValidator validator;
        
        bool validation_passed = true;
        
        validation_passed &= validator.ValidateFullSystemIntegration(out);
        validation_passed &= validator.ValidateSubsystemCommunication(out);
        validation_passed &= validator.ValidateEventFlowIntegrity(out);
        validation_passed &= validator.ValidateResourceSharingConsistency(out);
        validation_passed &= validator.ValidatePerformanceExpectations(out);
        validation_passed &= validator.ValidateSystemStability(out);
        
        return validation_passed;
    }, out);
}

template<typename TestFunc>
IntegrationTestFramework::TestResult IntegrationTestFramework::RunTest(
    const std::string& name, TestFunc&& test_func, color_ostream& out) {
    
    logger_.Info("Running test: {}", name);
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    try {
        bool passed = test_func(out);
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        auto result = CreateTestResult(name, passed, duration);
        test_results_.push_back(result);
        
        logger_.Info("Test {} {}: {} ms", name, (passed ? "PASSED" : "FAILED"), duration.count());
        
        return result;
    }
    catch (const std::exception& e) {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        auto result = CreateTestResult(name, false, duration, e.what());
        test_results_.push_back(result);
        
        logger_.Error("Test {} FAILED with exception: {}", name, e.what());
        
        return result;
    }
}

IntegrationTestFramework::TestResult IntegrationTestFramework::CreateTestResult(
    const std::string& name, bool passed, std::chrono::milliseconds time,
    const std::string& error, const std::string& perf_notes) {
    
    TestResult result;
    result.name = name;
    result.passed = passed;
    result.execution_time = time;
    result.error_message = error;
    result.performance_notes = perf_notes;
    
    return result;
}

std::vector<IntegrationTestFramework::TestResult> IntegrationTestFramework::GetTestResults() const {
    return test_results_;
}

void IntegrationTestFramework::GenerateReport(std::ostream& out, bool html) const {
    if (html) {
        out << "<html><head><title>DF-AI Integration Test Report</title></head><body>\n";
        out << "<h1>DF-AI Enhanced Integration Test Report</h1>\n";
        out << "<table border='1'>\n";
        out << "<tr><th>Test Name</th><th>Status</th><th>Time (ms)</th><th>Notes</th></tr>\n";
        
        for (const auto& result : test_results_) {
            out << "<tr>";
            out << "<td>" << result.name << "</td>";
            out << "<td style='color: " << (result.passed ? "green" : "red") << "'>";
            out << (result.passed ? "PASSED" : "FAILED") << "</td>";
            out << "<td>" << result.execution_time.count() << "</td>";
            out << "<td>" << (result.error_message.empty() ? result.performance_notes : result.error_message) << "</td>";
            out << "</tr>\n";
        }
        
        out << "</table></body></html>\n";
    } else {
        out << "DF-AI Enhanced Integration Test Report\n";
        out << "=====================================\n\n";
        
        size_t passed = 0;
        for (const auto& result : test_results_) {
            if (result.passed) passed++;
            
            out << std::setw(40) << std::left << result.name << " ";
            out << std::setw(8) << (result.passed ? "PASSED" : "FAILED") << " ";
            out << std::setw(8) << result.execution_time.count() << "ms";
            
            if (!result.error_message.empty()) {
                out << " ERROR: " << result.error_message;
            } else if (!result.performance_notes.empty()) {
                out << " " << result.performance_notes;
            }
            
            out << "\n";
        }
        
        out << "\nSummary: " << passed << "/" << test_results_.size() 
            << " tests passed (" << (100.0 * passed / test_results_.size()) << "%)\n";
    }
}

// Additional implementations for other test suites would go here...
// For brevity, showing the key structure and core implementation

} // namespace testing
} // namespace dfai
