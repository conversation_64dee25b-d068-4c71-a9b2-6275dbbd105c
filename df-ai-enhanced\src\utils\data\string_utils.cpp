#include "string_utils.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <cctype>

namespace dfai {
namespace utils {
namespace data {

// String manipulation
std::string Trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\n\r\f\v");
    if (start == std::string::npos) {
        return "";
    }
    
    size_t end = str.find_last_not_of(" \t\n\r\f\v");
    return str.substr(start, end - start + 1);
}

std::string TrimLeft(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\n\r\f\v");
    if (start == std::string::npos) {
        return "";
    }
    return str.substr(start);
}

std::string TrimRight(const std::string& str) {
    size_t end = str.find_last_not_of(" \t\n\r\f\v");
    if (end == std::string::npos) {
        return "";
    }
    return str.substr(0, end + 1);
}

std::string ToLower(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

std::string ToUpper(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::toupper);
    return result;
}

std::string Capitalize(const std::string& str) {
    if (str.empty()) {
        return str;
    }
    
    std::string result = ToLower(str);
    result[0] = ::toupper(result[0]);
    return result;
}

std::string Replace(const std::string& str, const std::string& from, const std::string& to) {
    if (from.empty()) {
        return str;
    }
    
    std::string result = str;
    size_t pos = 0;
    
    while ((pos = result.find(from, pos)) != std::string::npos) {
        result.replace(pos, from.length(), to);
        pos += to.length();
    }
    
    return result;
}

std::string ReplaceAll(const std::string& str, const std::vector<std::pair<std::string, std::string>>& replacements) {
    std::string result = str;
    
    for (const auto& replacement : replacements) {
        result = Replace(result, replacement.first, replacement.second);
    }
    
    return result;
}

// String splitting and joining
std::vector<std::string> Split(const std::string& str, const std::string& delimiter) {
    std::vector<std::string> result;
    
    if (str.empty() || delimiter.empty()) {
        result.push_back(str);
        return result;
    }
    
    size_t start = 0;
    size_t end = str.find(delimiter);
    
    while (end != std::string::npos) {
        result.push_back(str.substr(start, end - start));
        start = end + delimiter.length();
        end = str.find(delimiter, start);
    }
    
    result.push_back(str.substr(start));
    return result;
}

std::vector<std::string> SplitLines(const std::string& str) {
    return Split(str, "\n");
}

std::vector<std::string> SplitWhitespace(const std::string& str) {
    std::vector<std::string> result;
    std::istringstream iss(str);
    std::string token;
    
    while (iss >> token) {
        result.push_back(token);
    }
    
    return result;
}

std::string Join(const std::vector<std::string>& strings, const std::string& delimiter) {
    if (strings.empty()) {
        return "";
    }
    
    std::ostringstream oss;
    oss << strings[0];
    
    for (size_t i = 1; i < strings.size(); ++i) {
        oss << delimiter << strings[i];
    }
    
    return oss.str();
}

std::string JoinLines(const std::vector<std::string>& lines) {
    return Join(lines, "\n");
}

// String searching and matching
bool StartsWith(const std::string& str, const std::string& prefix) {
    if (prefix.length() > str.length()) {
        return false;
    }
    return str.substr(0, prefix.length()) == prefix;
}

bool EndsWith(const std::string& str, const std::string& suffix) {
    if (suffix.length() > str.length()) {
        return false;
    }
    return str.substr(str.length() - suffix.length()) == suffix;
}

bool Contains(const std::string& str, const std::string& substring) {
    return str.find(substring) != std::string::npos;
}

bool ContainsIgnoreCase(const std::string& str, const std::string& substring) {
    return Contains(ToLower(str), ToLower(substring));
}

size_t CountOccurrences(const std::string& str, const std::string& substring) {
    if (substring.empty()) {
        return 0;
    }
    
    size_t count = 0;
    size_t pos = 0;
    
    while ((pos = str.find(substring, pos)) != std::string::npos) {
        ++count;
        pos += substring.length();
    }
    
    return count;
}

std::vector<size_t> FindAllOccurrences(const std::string& str, const std::string& substring) {
    std::vector<size_t> positions;
    
    if (substring.empty()) {
        return positions;
    }
    
    size_t pos = 0;
    while ((pos = str.find(substring, pos)) != std::string::npos) {
        positions.push_back(pos);
        pos += substring.length();
    }
    
    return positions;
}

// String validation
bool IsEmpty(const std::string& str) {
    return str.empty();
}

bool IsWhitespace(const std::string& str) {
    return Trim(str).empty();
}

bool IsNumeric(const std::string& str) {
    if (str.empty()) {
        return false;
    }
    
    size_t start = 0;
    if (str[0] == '+' || str[0] == '-') {
        start = 1;
    }
    
    bool has_decimal = false;
    for (size_t i = start; i < str.length(); ++i) {
        if (str[i] == '.') {
            if (has_decimal) {
                return false; // Multiple decimal points
            }
            has_decimal = true;
        } else if (!std::isdigit(str[i])) {
            return false;
        }
    }
    
    return start < str.length(); // Must have at least one digit
}

bool IsInteger(const std::string& str) {
    if (str.empty()) {
        return false;
    }
    
    size_t start = 0;
    if (str[0] == '+' || str[0] == '-') {
        start = 1;
    }
    
    for (size_t i = start; i < str.length(); ++i) {
        if (!std::isdigit(str[i])) {
            return false;
        }
    }
    
    return start < str.length(); // Must have at least one digit
}

bool IsAlphabetic(const std::string& str) {
    if (str.empty()) {
        return false;
    }
    
    for (char c : str) {
        if (!std::isalpha(c)) {
            return false;
        }
    }
    
    return true;
}

bool IsAlphanumeric(const std::string& str) {
    if (str.empty()) {
        return false;
    }
    
    for (char c : str) {
        if (!std::isalnum(c)) {
            return false;
        }
    }
    
    return true;
}

// String conversion
int ToInt(const std::string& str, int default_value) {
    try {
        return std::stoi(str);
    } catch (const std::exception&) {
        return default_value;
    }
}

float ToFloat(const std::string& str, float default_value) {
    try {
        return std::stof(str);
    } catch (const std::exception&) {
        return default_value;
    }
}

double ToDouble(const std::string& str, double default_value) {
    try {
        return std::stod(str);
    } catch (const std::exception&) {
        return default_value;
    }
}

bool ToBool(const std::string& str, bool default_value) {
    std::string lower = ToLower(Trim(str));
    
    if (lower == "true" || lower == "1" || lower == "yes" || lower == "on") {
        return true;
    } else if (lower == "false" || lower == "0" || lower == "no" || lower == "off") {
        return false;
    }
    
    return default_value;
}

std::string ToString(int value) {
    return std::to_string(value);
}

std::string ToString(float value, int precision) {
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(precision) << value;
    return oss.str();
}

std::string ToString(double value, int precision) {
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(precision) << value;
    return oss.str();
}

std::string ToString(bool value) {
    return value ? "true" : "false";
}

// String formatting
std::string PadLeft(const std::string& str, size_t width, char fill_char) {
    if (str.length() >= width) {
        return str;
    }
    
    return std::string(width - str.length(), fill_char) + str;
}

std::string PadRight(const std::string& str, size_t width, char fill_char) {
    if (str.length() >= width) {
        return str;
    }
    
    return str + std::string(width - str.length(), fill_char);
}

std::string PadCenter(const std::string& str, size_t width, char fill_char) {
    if (str.length() >= width) {
        return str;
    }
    
    size_t total_padding = width - str.length();
    size_t left_padding = total_padding / 2;
    size_t right_padding = total_padding - left_padding;
    
    return std::string(left_padding, fill_char) + str + std::string(right_padding, fill_char);
}

std::string Truncate(const std::string& str, size_t max_length, const std::string& suffix) {
    if (str.length() <= max_length) {
        return str;
    }
    
    if (suffix.length() >= max_length) {
        return suffix.substr(0, max_length);
    }
    
    return str.substr(0, max_length - suffix.length()) + suffix;
}

std::string WrapText(const std::string& text, size_t line_width) {
    if (text.length() <= line_width) {
        return text;
    }
    
    std::vector<std::string> words = SplitWhitespace(text);
    std::vector<std::string> lines;
    std::string current_line;
    
    for (const std::string& word : words) {
        if (current_line.empty()) {
            current_line = word;
        } else if (current_line.length() + 1 + word.length() <= line_width) {
            current_line += " " + word;
        } else {
            lines.push_back(current_line);
            current_line = word;
        }
    }
    
    if (!current_line.empty()) {
        lines.push_back(current_line);
    }
    
    return JoinLines(lines);
}

} // namespace data
} // namespace utils
} // namespace dfai
