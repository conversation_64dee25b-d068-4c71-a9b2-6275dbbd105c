{"$schema": "https://ben.lubar.me/df-ai-schemas/room-template.json", "f": [{"dig": "DownStair", "construction": "DownStair", "x": 3, "y": 3, "z": 0}, {"dig": "DownStair", "construction": "DownStair", "x": 2, "y": 3, "z": 0}, {"dig": "DownStair", "construction": "DownStair", "x": 4, "y": 3, "z": 0}, {"dig": "DownStair", "construction": "DownStair", "x": 3, "y": 2, "z": 0}, {"dig": "DownStair", "construction": "DownStair", "x": 2, "y": 2, "z": 0}, {"dig": "DownStair", "construction": "DownStair", "x": 4, "y": 2, "z": 0}, {"dig": "DownStair", "construction": "DownStair", "x": 3, "y": 4, "z": 0}, {"dig": "DownStair", "construction": "DownStair", "x": 2, "y": 4, "z": 0}, {"dig": "DownStair", "construction": "DownStair", "x": 4, "y": 4, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 1, "y": 1, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 0, "y": 1, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 2, "y": 1, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 1, "y": 0, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 0, "y": 0, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 2, "y": 0, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 1, "y": 2, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 0, "y": 2, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 2, "y": 2, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 1, "y": 1, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 0, "y": 1, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 2, "y": 1, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 1, "y": 0, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 0, "y": 0, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 2, "y": 0, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 1, "y": 2, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 0, "y": 2, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 2, "y": 2, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 1, "y": 1, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 0, "y": 1, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 2, "y": 1, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 1, "y": 0, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 0, "y": 0, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 2, "y": 0, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 1, "y": 2, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 0, "y": 2, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 2, "y": 2, "z": 0}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 1, "y": 1, "z": 0, "stairs_special": true}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 0, "y": 1, "z": 0, "stairs_special": true}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 2, "y": 1, "z": 0, "stairs_special": true}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 1, "y": 0, "z": 0, "stairs_special": true}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 0, "y": 0, "z": 0, "stairs_special": true}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 2, "y": 0, "z": 0, "stairs_special": true}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 1, "y": 2, "z": 0, "stairs_special": true}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 0, "y": 2, "z": 0, "stairs_special": true}, {"dig": "UpDownStair", "construction": "UpDownStair", "x": 2, "y": 2, "z": 0, "stairs_special": true}, {"type": "cage_trap", "x": 3, "y": 1, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 3, "y": 5, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 4, "y": 1, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 2, "y": 5, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 5, "y": 1, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 1, "y": 5, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 5, "y": 2, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 1, "y": 4, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 5, "y": 3, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 1, "y": 3, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 5, "y": 4, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 1, "y": 2, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 5, "y": 5, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 1, "y": 1, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 4, "y": 5, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 2, "y": 1, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 3, "y": 0, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 3, "y": 6, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 4, "y": 0, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 2, "y": 6, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 5, "y": 0, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 1, "y": 6, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 6, "y": 0, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 0, "y": 6, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 6, "y": 1, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 0, "y": 5, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 6, "y": 2, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 0, "y": 4, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 6, "y": 3, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 0, "y": 3, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 6, "y": 4, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 0, "y": 2, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 6, "y": 5, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 0, "y": 1, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 6, "y": 6, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 0, "y": 0, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 5, "y": 6, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 1, "y": 0, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 4, "y": 6, "z": 0, "ignore": true}, {"type": "cage_trap", "x": 2, "y": 0, "z": 0, "ignore": true}, {"type": "door", "x": 2, "y": 3, "z": 0}, {"type": "chair", "x": 1, "y": 1, "z": 0, "makeroom": true}, {"type": "chair", "x": 1, "y": 1, "z": 0, "makeroom": true}, {"type": "door", "x": 3, "y": 0, "z": 0}, {"type": "door", "x": -1, "y": 0, "z": 0}, {"type": "lever", "x": 1, "y": 1, "z": 0}, {"type": "door", "x": 1, "y": 3, "z": 0}], "r": [{"type": "corridor", "corridor_type": "corridor", "min": [-3, -3, 0], "max": [3, 3, 0], "outdoor": true, "layout": [0, 1, 2, 3, 4, 5, 6, 7, 8, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84], "comment": "fort entrance"}, {"type": "corridor", "corridor_type": "corridor", "min": [-1, -1, -1], "max": [1, 1, -1], "accesspath": [0], "layout": [9, 10, 11, 12, 13, 14, 15, 16, 17], "exits": [["generic01_bedrooms", 1, 3, 0]], "comment": "staircase"}, {"type": "corridor", "corridor_type": "corridor", "min": [-1, -1, -2], "max": [1, 1, -2], "accesspath": [1], "layout": [18, 19, 20, 21, 22, 23, 24, 25, 26], "exits": [["generic01_corridor", -1, 1, 0], ["generic01_corridor", 3, 1, 0], ["generic01_corridor", 1, 3, 0]], "comment": "staircase"}, {"type": "corridor", "corridor_type": "corridor", "min": [-1, -1, -3], "max": [1, 1, -3], "accesspath": [2], "layout": [27, 28, 29, 30, 31, 32, 33, 34, 35], "exits": [["generic01_corridor", -1, 1, 0], ["generic01_corridor", 3, 1, 0], ["generic01_corridor", 1, -1, 0], ["generic01_corridor", 1, 3, 0]], "comment": "staircase"}, {"type": "corridor", "corridor_type": "corridor", "min": [-1, -1, -4], "max": [1, 1, -4], "accesspath": [3], "layout": [36, 37, 38, 39, 40, 41, 42, 43, 44], "exits": [["generic01_corridor", -1, 1, 0], ["generic01_corridor", 3, 1, 0], ["generic01_corridor", 1, -1, 0], ["generic01_corridor", 1, 3, 0], ["generic01_stair", 1, 1, -1]], "comment": "staircase"}, {"type": "stockpile", "stockpile_type": "food", "level": 1, "min": [-2, -5, -1], "max": [2, -3, -1], "accesspath": [1], "layout": [85], "exits": [["generic01_corridor", 2, -1, 0]]}, {"type": "workshop", "workshop_type": "<PERSON><PERSON>", "level": 1, "min": [-5, -5, -1], "max": [-3, -3, -1], "accesspath": [5, 8]}, {"type": "workshop", "workshop_type": "<PERSON>s", "level": 1, "min": [3, -5, -1], "max": [5, -3, -1], "accesspath": [5, 9]}, {"type": "stockpile", "stockpile_type": "wood", "min": [-5, -2, -1], "max": [-3, -2, -1], "workshop": 6}, {"type": "stockpile", "stockpile_type": "stone", "stock_disable": ["StoneOres", "StoneEconomic", "StoneClay"], "min": [3, -2, -1], "max": [5, -2, -1], "workshop": 7}, {"type": "nobleroom", "nobleroom_type": "office", "min": [-5, 0, -1], "max": [-3, 1, -1], "noblesuite": 0, "accesspath": [1], "layout": [86, 88]}, {"type": "nobleroom", "nobleroom_type": "office", "min": [3, 0, -1], "max": [5, 1, -1], "noblesuite": 1, "accesspath": [1], "layout": [87, 89]}, {"type": "releasecage", "min": [-1, -5, -2], "max": [1, -3, -2], "build_when_accessible": true, "accesspath": [2], "layout": [90, 91], "exits": [["generic01_corridor", 1, -1, 0], ["generic01_corridor", -1, 1, 0], ["generic01_corridor", 3, 1, 0]]}]}