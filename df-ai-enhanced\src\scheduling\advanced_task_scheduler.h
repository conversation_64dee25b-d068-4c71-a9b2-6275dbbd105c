#pragma once

#include "../utils/common/common.h"
#include "../debug/logging.h"
#include "../planning/plan_types.h"

#include <queue>
#include <map>
#include <functional>
#include <thread>
#include <mutex>
#include <condition_variable>

namespace dfai {
namespace scheduling {

/**
 * @brief Task execution context and state
 */
enum class TaskState {
    PENDING,
    READY,
    RUNNING,
    SUSPENDED,
    COMPLETED,
    FAILED,
    CANCELLED
};

/**
 * @brief Task dependency types
 */
enum class DependencyType {
    MUST_COMPLETE_BEFORE,  // This task must complete before dependent can start
    MUST_START_BEFORE,     // This task must start before dependent can start
    RESOURCE_DEPENDENCY,   // Tasks share a limited resource
    LOCATION_DEPENDENCY,   // Tasks affect the same location
    SEQUENTIAL_ORDER       // Tasks must execute in order
};

/**
 * @brief Task dependency relationship
 */
struct TaskDependency {
    int32_t prerequisite_task_id;
    int32_t dependent_task_id;
    DependencyType type;
    std::string description;
    
    // Optional parameters
    int32_t delay_ticks = 0;  // Delay between prerequisite completion and dependent start
    std::map<std::string, std::string> parameters;
};

/**
 * @brief Advanced task with scheduling metadata
 */
class ScheduledTask {
public:
    int32_t id;
    task_type::type type;
    TaskState state;
    
    // Scheduling properties
    int32_t priority;
    std::chrono::steady_clock::time_point created_time;
    std::chrono::steady_clock::time_point earliest_start_time;
    std::chrono::steady_clock::time_point deadline;
    std::chrono::steady_clock::time_point started_time;
    std::chrono::steady_clock::time_point completed_time;
    
    // Resource requirements
    std::map<std::string, int32_t> resource_requirements;
    std::vector<std::string> required_capabilities;
    
    // Execution tracking
    int32_t execution_attempts;
    int32_t max_execution_attempts;
    double estimated_duration_ticks;
    double actual_duration_ticks;
    
    // Dependency tracking
    std::vector<int32_t> prerequisite_tasks;
    std::vector<int32_t> dependent_tasks;
    
    // Task data
    room* target_room;
    furniture* target_furniture;
    std::string description;
    std::string failure_reason;
    
    // Execution function
    std::function<bool(color_ostream&, ScheduledTask&)> execution_function;
    
    ScheduledTask(int32_t task_id, task_type::type task_type);
    
    // State management
    bool CanExecute() const;
    bool IsReady() const;
    bool IsCompleted() const;
    bool HasFailed() const;
    bool HasPrerequisitesMet() const;
    bool IsOverdue() const;
    
    // Utility
    std::string GetStatusString() const;
    double GetProgressPercentage() const;
    std::chrono::milliseconds GetTimeToDeadline() const;
};

/**
 * @brief Resource pool for task scheduling
 */
class ResourcePool {
public:
    std::string name;
    int32_t total_capacity;
    int32_t available_capacity;
    std::map<int32_t, int32_t> task_allocations;  // task_id -> allocated_amount
    
    ResourcePool(const std::string& pool_name, int32_t capacity);
    
    bool CanAllocate(int32_t amount) const;
    bool Allocate(int32_t task_id, int32_t amount);
    void Release(int32_t task_id);
    void ReleaseAmount(int32_t task_id, int32_t amount);
    
    int32_t GetAllocatedAmount(int32_t task_id) const;
    double GetUtilizationPercentage() const;
    std::vector<int32_t> GetAllocatedTasks() const;
};

/**
 * @brief Advanced task scheduler with dependency management
 * 
 * Provides sophisticated task scheduling including dependency resolution,
 * resource management, priority-based execution, and deadline tracking.
 */
class AdvancedTaskScheduler {
public:
    AdvancedTaskScheduler();
    ~AdvancedTaskScheduler();
    
    // Lifecycle
    bool Initialize(color_ostream& out);
    void Shutdown();
    void Update(color_ostream& out);
    
    // Task management
    int32_t AddTask(std::unique_ptr<ScheduledTask> task);
    bool RemoveTask(int32_t task_id);
    bool CancelTask(int32_t task_id, const std::string& reason);
    void ClearAllTasks();
    
    // Task dependencies
    bool AddDependency(const TaskDependency& dependency);
    bool RemoveDependency(int32_t prerequisite_id, int32_t dependent_id);
    std::vector<TaskDependency> GetTaskDependencies(int32_t task_id) const;
    bool HasCircularDependencies() const;
    
    // Task queries
    std::shared_ptr<ScheduledTask> GetTask(int32_t task_id) const;
    std::vector<std::shared_ptr<ScheduledTask>> GetTasksByState(TaskState state) const;
    std::vector<std::shared_ptr<ScheduledTask>> GetTasksByType(task_type::type type) const;
    std::vector<std::shared_ptr<ScheduledTask>> GetOverdueTasks() const;
    std::vector<std::shared_ptr<ScheduledTask>> GetReadyTasks() const;
    
    // Resource management
    void AddResourcePool(const std::string& name, int32_t capacity);
    void RemoveResourcePool(const std::string& name);
    bool AllocateResource(int32_t task_id, const std::string& resource_name, int32_t amount);
    void ReleaseTaskResources(int32_t task_id);
    
    std::shared_ptr<ResourcePool> GetResourcePool(const std::string& name) const;
    std::vector<std::string> GetResourcePoolNames() const;
    
    // Scheduling strategies
    enum class SchedulingStrategy {
        PRIORITY_FIRST,     // Execute highest priority first
        DEADLINE_FIRST,     // Execute closest deadline first
        SHORTEST_JOB_FIRST, // Execute shortest estimated duration first
        ROUND_ROBIN,        // Cycle through task types
        WEIGHTED_FAIR       // Balance priority, deadline, and duration
    };
    
    void SetSchedulingStrategy(SchedulingStrategy strategy);
    SchedulingStrategy GetSchedulingStrategy() const;
    
    // Execution control
    void SetMaxConcurrentTasks(int32_t max_tasks);
    int32_t GetMaxConcurrentTasks() const;
    int32_t GetActiveTasks() const;
    
    void PauseScheduler();
    void ResumeScheduler();
    bool IsSchedulerPaused() const;
    
    // Performance tracking
    struct SchedulerStats {
        int32_t total_tasks_scheduled;
        int32_t total_tasks_completed;
        int32_t total_tasks_failed;
        int32_t current_pending_tasks;
        int32_t current_running_tasks;
        
        double average_task_completion_time;
        double average_task_wait_time;
        double scheduler_efficiency;
        
        std::map<task_type::type, int32_t> task_type_counts;
        std::map<TaskState, int32_t> task_state_counts;
    };
    
    SchedulerStats GetSchedulerStats() const;
    void ResetStats();
    
    // Optimization
    void OptimizeSchedule();
    void ReorderTasksByPriority();
    void BalanceResourceUtilization();
    std::vector<std::string> GetOptimizationSuggestions() const;
    
    // Reporting
    void GenerateScheduleReport(color_ostream& out, bool detailed = false) const;
    void GenerateResourceReport(color_ostream& out) const;
    void GenerateDependencyReport(color_ostream& out) const;
    void GeneratePerformanceReport(color_ostream& out) const;
    
    // Configuration
    struct SchedulerConfig {
        SchedulingStrategy strategy = SchedulingStrategy::WEIGHTED_FAIR;
        int32_t max_concurrent_tasks = 5;
        int32_t task_timeout_ticks = 1000;
        
        // Resource management
        bool enable_resource_management = true;
        bool auto_release_resources = true;
        double resource_allocation_timeout_seconds = 30.0;
        
        // Dependency management
        bool strict_dependency_checking = true;
        bool allow_dependency_override = false;
        int32_t max_dependency_depth = 10;
        
        // Performance tuning
        bool enable_task_preemption = false;
        bool enable_deadline_enforcement = true;
        double deadline_warning_threshold = 0.8;  // Warn when 80% of time to deadline used
        
        std::string to_string() const;
    };
    
    void SetConfig(const SchedulerConfig& config);
    const SchedulerConfig& GetConfig() const;
    
private:
    debug::Logger& logger_;
    bool initialized_;
    bool scheduler_paused_;
    
    // Task storage
    std::map<int32_t, std::shared_ptr<ScheduledTask>> tasks_;
    std::priority_queue<std::shared_ptr<ScheduledTask>, 
                       std::vector<std::shared_ptr<ScheduledTask>>,
                       std::function<bool(const std::shared_ptr<ScheduledTask>&, 
                                        const std::shared_ptr<ScheduledTask>&)>> task_queue_;
    
    // Dependencies
    std::vector<TaskDependency> task_dependencies_;
    std::map<int32_t, std::vector<int32_t>> dependency_graph_;  // task_id -> prerequisites
    
    // Resources
    std::map<std::string, std::shared_ptr<ResourcePool>> resource_pools_;
    
    // Execution tracking
    std::set<int32_t> running_tasks_;
    std::chrono::steady_clock::time_point last_update_time_;
    
    // Configuration
    SchedulerConfig config_;
    SchedulingStrategy current_strategy_;
    
    // Statistics
    mutable SchedulerStats stats_;
    std::chrono::steady_clock::time_point stats_start_time_;
    
    // Thread safety
    mutable std::mutex scheduler_mutex_;
    std::condition_variable task_available_cv_;
    
    // ID generation
    int32_t next_task_id_;
    
    // Internal methods
    void ExecuteSchedulingCycle(color_ostream& out);
    std::vector<std::shared_ptr<ScheduledTask>> SelectTasksForExecution();
    bool ExecuteTask(std::shared_ptr<ScheduledTask> task, color_ostream& out);
    void CompleteTask(std::shared_ptr<ScheduledTask> task, bool success);
    
    // Dependency resolution
    bool ResolveDependencies();
    std::vector<int32_t> GetTopologicalOrder() const;
    bool HasCircularDependency(int32_t task_id, std::set<int32_t>& visited, std::set<int32_t>& in_stack) const;
    void UpdateTaskReadiness();
    
    // Resource management
    bool AllocateTaskResources(std::shared_ptr<ScheduledTask> task);
    void ReleaseTaskResourcesInternal(int32_t task_id);
    bool CanAllocateResources(std::shared_ptr<ScheduledTask> task) const;
    
    // Scheduling algorithms
    std::function<bool(const std::shared_ptr<ScheduledTask>&, const std::shared_ptr<ScheduledTask>&)> 
        GetComparatorForStrategy(SchedulingStrategy strategy) const;
    
    void ApplyPriorityFirstScheduling();
    void ApplyDeadlineFirstScheduling();
    void ApplyShortestJobFirstScheduling();
    void ApplyRoundRobinScheduling();
    void ApplyWeightedFairScheduling();
    
    // Utility methods
    void UpdateTaskStatistics();
    void CleanupCompletedTasks();
    void CheckDeadlines(color_ostream& out);
    void HandleTaskTimeout(std::shared_ptr<ScheduledTask> task, color_ostream& out);
    
    // Optimization helpers
    double CalculateTaskScore(std::shared_ptr<ScheduledTask> task) const;
    void OptimizeResourceAllocation();
    void RebalanceTaskPriorities();
};

} // namespace scheduling
} // namespace dfai
