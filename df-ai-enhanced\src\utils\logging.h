#pragma once

#include "common.h"
#include <fstream>
#include <sstream>

namespace df_ai {
namespace utils {

/**
 * Centralized logging system
 */
class Logger {
private:
    mutable std::ofstream log_file_;
    mutable std::ofstream events_file_;
    bool console_output_;
    bool file_output_;
    
public:
    Logger(const std::string& log_filename = "df-ai.log", 
           const std::string& events_filename = "df-ai-events.json");
    ~Logger();
    
    // Configuration
    void set_console_output(bool enabled) { console_output_ = enabled; }
    void set_file_output(bool enabled) { file_output_ = enabled; }
    bool console_output() const { return console_output_; }
    bool file_output() const { return file_output_; }
    
    // Logging methods
    void debug(color_ostream& out, const std::string& message) const;
    void info(color_ostream& out, const std::string& message) const;
    void warning(color_ostream& out, const std::string& message) const;
    void error(color_ostream& out, const std::string& message) const;
    
    // Event logging
    void event(const std::string& type, const Json::Value& data = Json::Value()) const;
    
    // Timestamped logging
    void log_with_timestamp(color_ostream& out, const std::string& level, 
                           const std::string& message) const;
    
    // Utility methods
    static std::string timestamp(int32_t year = -1, int32_t tick = -1);
    static std::string format_message(const std::string& level, const std::string& message);
    
private:
    void write_to_file(const std::string& message) const;
    void write_event_to_file(const std::string& event_json) const;
};

} // namespace utils
} // namespace df_ai
