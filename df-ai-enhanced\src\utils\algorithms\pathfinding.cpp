#include "pathfinding.h"
#include "../common/types.h"
#include <queue>
#include <unordered_set>
#include <unordered_map>
#include <algorithm>

namespace dfai {
namespace utils {
namespace algorithms {

// PathNode implementation
PathNode::PathNode(const Coordinate& pos, float g_cost, float h_cost, const PathNode* parent)
    : position(pos), g_cost(g_cost), h_cost(h_cost), parent(parent) {}

float PathNode::f_cost() const {
    return g_cost + h_cost;
}

bool PathNode::operator<(const PathNode& other) const {
    return f_cost() > other.f_cost(); // For priority queue (min-heap)
}

// Path implementation
Path::Path() : total_cost_(0.0f) {}

Path::Path(const std::vector<Coordinate>& waypoints, float cost)
    : waypoints_(waypoints), total_cost_(cost) {}

bool Path::IsValid() const {
    return !waypoints_.empty();
}

size_t Path::Length() const {
    return waypoints_.size();
}

float Path::TotalCost() const {
    return total_cost_;
}

const std::vector<Coordinate>& Path::GetWaypoints() const {
    return waypoints_;
}

Coordinate Path::GetStart() const {
    return waypoints_.empty() ? Coordinate{0, 0, 0} : waypoints_.front();
}

Coordinate Path::GetEnd() const {
    return waypoints_.empty() ? Coordinate{0, 0, 0} : waypoints_.back();
}

Coordinate Path::GetNextWaypoint(const Coordinate& current_pos) const {
    if (waypoints_.empty()) {
        return current_pos;
    }
    
    // Find the closest waypoint ahead of current position
    for (size_t i = 0; i < waypoints_.size(); ++i) {
        if (waypoints_[i] == current_pos && i + 1 < waypoints_.size()) {
            return waypoints_[i + 1];
        }
    }
    
    // If not found, return the first waypoint
    return waypoints_.front();
}

void Path::Reverse() {
    std::reverse(waypoints_.begin(), waypoints_.end());
}

void Path::Optimize() {
    if (waypoints_.size() <= 2) {
        return;
    }
    
    // Simple optimization: remove redundant waypoints in straight lines
    std::vector<Coordinate> optimized;
    optimized.push_back(waypoints_[0]);
    
    for (size_t i = 1; i < waypoints_.size() - 1; ++i) {
        const Coordinate& prev = waypoints_[i - 1];
        const Coordinate& curr = waypoints_[i];
        const Coordinate& next = waypoints_[i + 1];
        
        // Check if current point is on the line between prev and next
        Coordinate dir1 = curr - prev;
        Coordinate dir2 = next - curr;
        
        // If directions are the same, skip the current waypoint
        if (!(dir1.x == dir2.x && dir1.y == dir2.y && dir1.z == dir2.z)) {
            optimized.push_back(curr);
        }
    }
    
    optimized.push_back(waypoints_.back());
    waypoints_ = optimized;
}

std::string Path::ToString() const {
    if (waypoints_.empty()) {
        return "Empty path";
    }
    
    std::string result = "Path: ";
    for (size_t i = 0; i < waypoints_.size(); ++i) {
        if (i > 0) result += " -> ";
        result += waypoints_[i].ToString();
    }
    result += " (cost: " + std::to_string(total_cost_) + ")";
    return result;
}

// Pathfinder implementation
Pathfinder::Pathfinder() : max_search_distance_(1000), allow_diagonal_(true) {}

void Pathfinder::SetMaxSearchDistance(int distance) {
    max_search_distance_ = distance;
}

void Pathfinder::SetAllowDiagonal(bool allow) {
    allow_diagonal_ = allow;
}

void Pathfinder::SetCostFunction(CostFunction cost_func) {
    cost_function_ = cost_func;
}

void Pathfinder::SetHeuristicFunction(HeuristicFunction heuristic_func) {
    heuristic_function_ = heuristic_func;
}

Path Pathfinder::FindPath(const Coordinate& start, const Coordinate& goal) {
    if (start == goal) {
        return Path({start}, 0.0f);
    }
    
    // Use default cost and heuristic functions if not set
    auto cost_func = cost_function_ ? cost_function_ : 
        [](const Coordinate& from, const Coordinate& to) -> float {
            return static_cast<float>(from.DistanceTo(to));
        };
    
    auto heuristic_func = heuristic_function_ ? heuristic_function_ :
        [](const Coordinate& from, const Coordinate& to) -> float {
            return static_cast<float>(from.ManhattanDistanceTo(to));
        };
    
    // A* algorithm implementation
    std::priority_queue<PathNode> open_set;
    std::unordered_set<std::string> closed_set;
    std::unordered_map<std::string, PathNode> all_nodes;
    
    // Helper function to convert coordinate to string key
    auto coord_to_key = [](const Coordinate& coord) -> std::string {
        return std::to_string(coord.x) + "," + std::to_string(coord.y) + "," + std::to_string(coord.z);
    };
    
    // Initialize start node
    PathNode start_node(start, 0.0f, heuristic_func(start, goal), nullptr);
    open_set.push(start_node);
    all_nodes[coord_to_key(start)] = start_node;
    
    while (!open_set.empty()) {
        PathNode current = open_set.top();
        open_set.pop();
        
        std::string current_key = coord_to_key(current.position);
        
        // Skip if already processed
        if (closed_set.find(current_key) != closed_set.end()) {
            continue;
        }
        
        closed_set.insert(current_key);
        
        // Check if we reached the goal
        if (current.position == goal) {
            return ReconstructPath(current, all_nodes, coord_to_key);
        }
        
        // Check search distance limit
        if (current.position.ManhattanDistanceTo(start) > max_search_distance_) {
            continue;
        }
        
        // Explore neighbors
        std::vector<Coordinate> neighbors = GetNeighbors(current.position);
        for (const Coordinate& neighbor : neighbors) {
            std::string neighbor_key = coord_to_key(neighbor);
            
            // Skip if already processed
            if (closed_set.find(neighbor_key) != closed_set.end()) {
                continue;
            }
            
            float tentative_g_cost = current.g_cost + cost_func(current.position, neighbor);
            
            auto existing_node_it = all_nodes.find(neighbor_key);
            if (existing_node_it == all_nodes.end() || tentative_g_cost < existing_node_it->second.g_cost) {
                PathNode neighbor_node(neighbor, tentative_g_cost, heuristic_func(neighbor, goal), &all_nodes[current_key]);
                all_nodes[neighbor_key] = neighbor_node;
                open_set.push(neighbor_node);
            }
        }
    }
    
    // No path found
    return Path();
}

std::vector<Coordinate> Pathfinder::GetNeighbors(const Coordinate& pos) const {
    std::vector<Coordinate> neighbors;
    
    // Basic 6-directional movement (N, S, E, W, Up, Down)
    std::vector<Coordinate> offsets = {
        {0, -1, 0}, {0, 1, 0}, {1, 0, 0}, {-1, 0, 0}, {0, 0, 1}, {0, 0, -1}
    };
    
    // Add diagonal movement if allowed
    if (allow_diagonal_) {
        std::vector<Coordinate> diagonal_offsets = {
            {1, -1, 0}, {-1, -1, 0}, {1, 1, 0}, {-1, 1, 0}
        };
        offsets.insert(offsets.end(), diagonal_offsets.begin(), diagonal_offsets.end());
    }
    
    for (const Coordinate& offset : offsets) {
        Coordinate neighbor = pos + offset;
        if (neighbor.IsValid()) {
            neighbors.push_back(neighbor);
        }
    }
    
    return neighbors;
}

Path Pathfinder::ReconstructPath(const PathNode& goal_node, 
                                const std::unordered_map<std::string, PathNode>& all_nodes,
                                std::function<std::string(const Coordinate&)> coord_to_key) const {
    std::vector<Coordinate> waypoints;
    float total_cost = goal_node.g_cost;
    
    const PathNode* current = &goal_node;
    while (current != nullptr) {
        waypoints.push_back(current->position);
        
        if (current->parent == nullptr) {
            break;
        }
        
        // Find parent node in the map
        std::string parent_key = coord_to_key(current->parent->position);
        auto parent_it = all_nodes.find(parent_key);
        if (parent_it != all_nodes.end()) {
            current = &parent_it->second;
        } else {
            break;
        }
    }
    
    std::reverse(waypoints.begin(), waypoints.end());
    return Path(waypoints, total_cost);
}

} // namespace algorithms
} // namespace utils
} // namespace dfai
