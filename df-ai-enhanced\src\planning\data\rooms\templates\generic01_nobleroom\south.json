{"$schema": "https://ben.lubar.me/df-ai-schemas/room-template.json", "f": [{"x": 2, "y": -1, "z": 0, "type": "door"}, {"x": 2, "y": 2, "z": 0, "type": "chair", "makeroom": true}, {"x": 2, "y": 1, "z": 0, "type": "table"}, {"x": 0, "y": 1, "z": 0, "type": "weapon_rack", "ignore": true}, {"x": 4, "y": 1, "z": 0, "type": "weapon_rack", "ignore": true}, {"x": 0, "y": 0, "z": 0, "type": "armor_stand", "ignore": true}, {"x": 4, "y": 0, "z": 0, "type": "armor_stand", "ignore": true}, {"x": 0, "y": 3, "z": 0, "type": "chest", "ignore": true}, {"x": 4, "y": 3, "z": 0, "type": "chest", "ignore": true}, {"x": -1, "y": 2, "z": 0, "type": "door", "internal": true}, {"x": 2, "y": 2, "z": 0, "type": "bed", "makeroom": true}, {"x": 2, "y": 1, "z": 0, "type": "cabinet", "ignore": true}, {"x": 2, "y": 3, "z": 0, "type": "cabinet", "ignore": true}, {"x": 5, "y": 0, "z": 0, "construction": "Wall", "dig": "No"}, {"x": 3, "y": 2, "z": 0, "type": "door", "internal": true}, {"x": 1, "y": 4, "z": 0, "type": "coffin", "makeroom": true}, {"x": 0, "y": -1, "z": 0, "type": "door", "internal": true}, {"x": -1, "y": 1, "z": 0, "type": "door", "internal": true}, {"x": 2, "y": -1, "z": 0, "type": "door", "internal": true}, {"x": 2, "y": 1, "z": 0, "type": "table", "makeroom": true}, {"x": 2, "y": 2, "z": 0, "type": "chair"}, {"x": 3, "y": 7, "z": 0, "type": "door", "internal": true}], "r": [{"min": [-2, 1, 0], "max": [2, 4, 0], "type": "nobleroom", "nobleroom_type": "office", "noblesuite": 0, "layout": [0, 1, 2, 3, 4, 5, 6, 7, 8], "require_stone": true}, {"min": [4, 1, 0], "max": [7, 5, 0], "type": "nobleroom", "nobleroom_type": "bedroom", "noblesuite": 0, "layout": [9, 10, 11, 12], "accesspath": [0], "require_stone": true}, {"min": [-2, 6, 0], "max": [3, 9, 0], "type": "nobleroom", "nobleroom_type": "dining", "noblesuite": 0, "layout": [13, 18, 19, 20], "accesspath": [0, 4], "require_stone": true}, {"min": [-6, 1, 0], "max": [-4, 9, 0], "type": "nobleroom", "nobleroom_type": "tomb", "noblesuite": 0, "layout": [14, 15, 21], "accesspath": [0], "require_stone": true}, {"min": [5, 7, 0], "max": [5, 8, 0], "type": "corridor", "corridor_type": "corridor", "layout": [16, 17], "accesspath": [1]}]}