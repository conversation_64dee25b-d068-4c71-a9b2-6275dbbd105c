{"$schema": "https://ben.lubar.me/df-ai-schemas/room-template.json", "f": [{"type": "door", "x": -1, "y": 3, "z": 0}, {"type": "restraint", "x": 1, "y": 1, "z": 0}, {"type": "restraint", "x": 3, "y": 1, "z": 0}, {"type": "restraint", "x": 1, "y": 3, "z": 0}, {"type": "restraint", "x": 3, "y": 3, "z": 0}, {"type": "cage", "x": 2, "y": 2, "z": 0}, {"type": "cage", "x": 1, "y": 2, "z": 0, "ignore": true}, {"type": "cage", "x": 3, "y": 2, "z": 0, "ignore": true}, {"type": "cage", "x": 2, "y": 1, "z": 0, "ignore": true}, {"type": "cage", "x": 2, "y": 3, "z": 0, "ignore": true}, {"type": "cage", "x": 0, "y": 2, "z": 0, "ignore": true}, {"type": "cage", "x": 4, "y": 2, "z": 0, "ignore": true}, {"type": "cage", "x": 2, "y": 0, "z": 0, "ignore": true}, {"type": "cage", "x": 2, "y": 4, "z": 0, "ignore": true}], "r": [{"type": "jail", "min": [1, -3, 0], "max": [5, 3, 0], "layout": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]}]}