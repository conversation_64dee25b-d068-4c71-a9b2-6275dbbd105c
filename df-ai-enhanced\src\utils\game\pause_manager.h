#pragma once

#include "../common/common.h"
#include "../../debug/logging.h"

namespace dfai {
namespace utils {

/**
 * @brief Manages game pause/unpause functionality
 * 
 * The PauseManager provides intelligent pause management for the AI system,
 * allowing it to pause the game when necessary and resume when appropriate.
 */
class PauseManager {
public:
    PauseManager();
    ~PauseManager();

    // Lifecycle management
    bool Initialize(color_ostream& out);
    void Shutdown();
    bool IsInitialized() const;
    void Update(color_ostream& out);

    // Pause control
    bool PauseGame(const std::string& reason, color_ostream& out);
    bool UnpauseGame(const std::string& reason, color_ostream& out);
    bool IsGamePaused() const;
    
    // Conditional pause/unpause
    bool PauseIfNeeded(const std::string& reason, color_ostream& out);
    bool UnpauseIfSafe(const std::string& reason, color_ostream& out);
    
    // Pause reasons tracking
    void AddPauseReason(const std::string& reason);
    void RemovePauseReason(const std::string& reason);
    std::vector<std::string> GetPauseReasons() const;
    bool HasPauseReasons() const;
    
    // Automatic pause configuration
    struct AutoPauseConfig {
        bool pause_on_error = true;
        bool pause_on_critical_event = true;
        bool pause_on_siege = false;
        bool pause_on_mood = false;
        bool pause_on_death = false;
        bool pause_on_trade = false;
        
        // Timing controls
        int max_pause_duration_seconds = 300; // 5 minutes max
        int min_pause_interval_seconds = 30;  // 30 seconds between auto-pauses
        
        std::string to_string() const;
    };
    
    void SetAutoConfig(const AutoPauseConfig& config);
    const AutoPauseConfig& GetAutoConfig() const;

    // Status and monitoring
    std::string GetPauseStatus() const;
    int GetPauseDurationSeconds() const;
    std::string GetLastPauseReason() const;
    std::string GetLastUnpauseReason() const;
    
    void ReportPauseStatus(color_ostream& out) const;

private:
    debug::Logger& logger_;
    bool initialized_;
    
    // Pause state
    bool game_paused_;
    std::set<std::string> pause_reasons_;
    mutable std::mutex pause_mutex_;
    
    // Pause history
    std::string last_pause_reason_;
    std::string last_unpause_reason_;
    std::chrono::steady_clock::time_point pause_start_time_;
    std::chrono::steady_clock::time_point last_pause_time_;
    
    // Configuration
    AutoPauseConfig auto_config_;
    
    // Legacy integration
    bool CallLegacyPause(color_ostream& out);
    bool CallLegacyUnpause(color_ostream& out);
    bool GetLegacyPauseState() const;
    
    // Utility methods
    bool IsSafeToUnpause() const;
    bool ShouldAutoPause(const std::string& reason) const;
    std::chrono::steady_clock::time_point GetCurrentTime() const;
    int GetElapsedSeconds(std::chrono::steady_clock::time_point start) const;
};

} // namespace utils  
} // namespace dfai
