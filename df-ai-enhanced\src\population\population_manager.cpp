#include "population_manager.h"
#include "../debug/logging.h"
#include "../config/config_manager.h"

#include <algorithm>
#include <sstream>

namespace dfai {
namespace population {

PopulationManager::PopulationManager()
    : logger_(debug::Logger::GetInstance())
    , initialized_(false)
    , update_counter_(0)
    , citizens_()
    , military_assignments_()
    , pets_()
    , pet_check_queue_()
    , visitors_()
    , residents_()
    , military_min_size_(10)
    , military_max_size_(50)
    , pending_squad_orders_()
    , medics_()
    , workers_()
    , problematic_jobs_()
    , deaths_seen_(0)
    , death_watch_callback_(nullptr)
    , last_crime_check_year_(0)
    , last_crime_check_tick_(0)
    , currently_trading_(false)
    , trade_start_x_(0)
    , trade_start_y_(0)
    , trade_start_z_(0)
    , update_callback_(nullptr)
{
    logger_.Info("PopulationManager created");
}

PopulationManager::~PopulationManager() {
    Shutdown();
    logger_.Info("PopulationManager destroyed");
}

bool PopulationManager::Initialize(color_ostream& out) {
    if (initialized_) {
        logger_.Warning("PopulationManager already initialized");
        return true;
    }
    
    logger_.Info("Initializing PopulationManager...");
    
    try {
        // Load configuration
        auto& config = config::ConfigManager::GetInstance();
        military_min_size_ = config.GetInt("military_min_size", 10);
        military_max_size_ = config.GetInt("military_max_size", 50);
        
        // Initialize update callback
        update_callback_ = std::make_unique<OnupdateCallback>([this](color_ostream& out) {
            Update(out);
        });
        
        initialized_ = true;
        logger_.Info("PopulationManager initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        logger_.Error("Exception during PopulationManager initialization: {}", e.what());
        return false;
    }
}

void PopulationManager::Shutdown() {
    if (!initialized_) {
        return;
    }
    
    logger_.Info("Shutting down PopulationManager...");
    
    // Stop death watch if active
    StopDeathWatch();
    
    // Clear callbacks
    update_callback_.reset();
    
    // Clear all collections
    citizens_.clear();
    military_assignments_.clear();
    pets_.clear();
    pet_check_queue_.clear();
    visitors_.clear();
    residents_.clear();
    pending_squad_orders_.clear();
    medics_.clear();
    workers_.clear();
    problematic_jobs_.clear();
    
    initialized_ = false;
    logger_.Info("PopulationManager shutdown complete");
}

void PopulationManager::Update(color_ostream& out) {
    if (!initialized_) {
        logger_.Warning("PopulationManager::Update called before initialization");
        return;
    }
    
    try {
        update_counter_++;
        
        // Update population lists
        UpdateCitizenList(out);
        UpdateVisitors(out);
        
        // Update military
        UpdateMilitary(out);
        ProcessSquadOrders(out);
        
        // Update pets
        UpdatePets(out);
        
        // Update jobs and workers
        UpdateJobs(out);
        UpdateWorkers(out);
        
        // Update deaths and health
        UpdateDeaths(out);
        UpdateCaged(out);
        
        // Update nobles
        UpdateNobles(out);
        
        // Update crime and justice
        UpdateCrimes(out);
        
        // Update locations
        UpdateLocations(out);
        
        // Update trading
        UpdateTrading(out);
        
        // Periodic intensive updates
        if (update_counter_ % 100 == 0) {
            CheckNobleApartments(out);
            CheckMilitaryNeeds(out);
        }
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in PopulationManager::Update: {}", e.what());
    }
}

std::string PopulationManager::GetStatusReport() const {
    std::ostringstream report;
    
    report << "Population Status:\n";
    report << "  Citizens: " << citizens_.size() << "\n";
    report << "  Military: " << military_assignments_.size() << "\n";
    report << "  Pets: " << pets_.size() << "\n";
    report << "  Visitors: " << visitors_.size() << "\n";
    report << "  Residents: " << residents_.size() << "\n";
    report << "  Workers: " << workers_.size() << "\n";
    report << "  Medics: " << medics_.size() << "\n";
    report << "  Pending Orders: " << pending_squad_orders_.size() << "\n";
    report << "  Trading: " << (currently_trading_ ? "Active" : "Inactive") << "\n";
    
    return report.str();
}

void PopulationManager::GenerateReport(std::ostream& out, bool html) const {
    if (html) {
        out << "<h3>Population Report</h3>\n";
        out << "<table>\n";
        out << "<tr><td>Citizens:</td><td>" << citizens_.size() << "</td></tr>\n";
        out << "<tr><td>Military:</td><td>" << military_assignments_.size() << "</td></tr>\n";
        out << "<tr><td>Pets:</td><td>" << pets_.size() << "</td></tr>\n";
        out << "<tr><td>Visitors:</td><td>" << visitors_.size() << "</td></tr>\n";
        out << "<tr><td>Residents:</td><td>" << residents_.size() << "</td></tr>\n";
        out << "</table>\n";
    } else {
        out << GetStatusReport();
    }
}

void PopulationManager::AddCitizen(color_ostream& out, int32_t unit_id) {
    if (citizens_.find(unit_id) != citizens_.end()) {
        logger_.Debug("Unit {} is already a citizen", unit_id);
        return;
    }
    
    citizens_.insert(unit_id);
    logger_.Info("Added citizen: {}", unit_id);
    
    // Trigger citizen-related updates
    CheckMilitaryNeeds(out);
}

void PopulationManager::RemoveCitizen(color_ostream& out, int32_t unit_id) {
    auto it = citizens_.find(unit_id);
    if (it == citizens_.end()) {
        logger_.Debug("Unit {} is not a citizen", unit_id);
        return;
    }
    
    citizens_.erase(it);
    
    // Remove from military if assigned
    RemoveFromMilitary(unit_id);
    
    // Remove from other lists
    medics_.erase(unit_id);
    workers_.erase(std::remove(workers_.begin(), workers_.end(), unit_id), workers_.end());
    
    logger_.Info("Removed citizen: {}", unit_id);
}

bool PopulationManager::IsCitizen(int32_t unit_id) const {
    return citizens_.find(unit_id) != citizens_.end();
}

bool PopulationManager::AssignToMilitary(int32_t unit_id, int32_t squad_id) {
    if (!IsCitizen(unit_id)) {
        logger_.Warning("Cannot assign non-citizen {} to military", unit_id);
        return false;
    }
    
    military_assignments_[unit_id] = squad_id;
    logger_.Info("Assigned unit {} to squad {}", unit_id, squad_id);
    return true;
}

bool PopulationManager::RemoveFromMilitary(int32_t unit_id) {
    auto it = military_assignments_.find(unit_id);
    if (it == military_assignments_.end()) {
        return false;
    }
    
    military_assignments_.erase(it);
    logger_.Info("Removed unit {} from military", unit_id);
    return true;
}

bool PopulationManager::IsMilitary(int32_t unit_id) const {
    return military_assignments_.find(unit_id) != military_assignments_.end();
}

bool PopulationManager::IsPet(int32_t unit_id) const {
    return pets_.find(unit_id) != pets_.end();
}

PetFlags PopulationManager::GetPetFlags(int32_t unit_id) const {
    auto it = pets_.find(unit_id);
    if (it != pets_.end()) {
        return it->second;
    }
    return PetFlags();
}

bool PopulationManager::IsVisitor(int32_t unit_id) const {
    return visitors_.find(unit_id) != visitors_.end();
}

bool PopulationManager::IsResident(int32_t unit_id) const {
    return residents_.find(unit_id) != residents_.end();
}

void PopulationManager::StartDeathWatch(color_ostream& out) {
    if (death_watch_callback_) {
        logger_.Warning("Death watch already active");
        return;
    }
    
    try {
        death_watch_callback_ = std::make_unique<OnupdateCallback>([this](color_ostream& out) {
            UpdateDeaths(out);
        });
        
        logger_.Info("Death watch started");
        
    } catch (const std::exception& e) {
        logger_.Error("Exception starting death watch: {}", e.what());
    }
}

void PopulationManager::StopDeathWatch() {
    if (death_watch_callback_) {
        death_watch_callback_.reset();
        logger_.Info("Death watch stopped");
    }
}

void PopulationManager::AssignUnitToZone(df::unit* unit, df::building_civzonest* zone) {
    if (!unit || !zone) {
        logger_.Warning("Invalid unit or zone for assignment");
        return;
    }
    
    try {
        // Check if already assigned to this zone
        if (auto ref = Units::getGeneralRef(unit, general_ref_type::BUILDING_CIVZONE_ASSIGNED)) {
            if (ref->getBuilding() == zone) {
                logger_.Debug("Unit {} already assigned to zone {}", unit->id, zone->id);
                return;
            }
        }
        
        // Create new assignment reference
        auto new_ref = new df::general_ref_building_civzone_assignedst();
        new_ref->building_id = zone->id;
        
        // Add reference to unit
        unit->general_refs.push_back(new_ref);
        
        // Add unit to zone's assigned units list
        if (std::find(zone->assigned_units.begin(), zone->assigned_units.end(), unit->id) == zone->assigned_units.end()) {
            zone->assigned_units.push_back(unit->id);
        }
        
        logger_.Info("Assigned unit {} to zone {}", unit->id, zone->id);
        
    } catch (const std::exception& e) {
        logger_.Error("Exception assigning unit {} to zone {}: {}", unit->id, zone->id, e.what());
    }
}

bool PopulationManager::SetupTrading(color_ostream& out, bool should_trade, bool allow_any_dwarf) {
    logger_.Info("Setting up trading: should_trade={}, allow_any_dwarf={}", should_trade, allow_any_dwarf);
    
    if (!should_trade) {
        currently_trading_ = false;
        return true;
    }
    
    try {
        // Find suitable trader
        df::unit* trader = nullptr;
        
        if (allow_any_dwarf) {
            // Use any available citizen
            for (auto citizen_id : citizens_) {
                auto* unit = df::unit::find(citizen_id);
                if (unit && !Units::isDead(unit) && !IsMilitary(citizen_id)) {
                    trader = unit;
                    break;
                }
            }
        } else {
            // Find citizen with trading skills
            for (auto citizen_id : citizens_) {
                auto* unit = df::unit::find(citizen_id);
                if (unit && IsUnitSuitableForTrading(unit)) {
                    trader = unit;
                    break;
                }
            }
        }
        
        if (!trader) {
            logger_.Warning("No suitable trader found");
            return false;
        }
        
        currently_trading_ = true;
        logger_.Info("Trading setup successful with trader {}", trader->id);
        return true;
        
    } catch (const std::exception& e) {
        logger_.Error("Exception setting up trading: {}", e.what());
        return false;
    }
}

bool PopulationManager::PerformTrade(color_ostream& out) {
    if (!currently_trading_) {
        logger_.Warning("Cannot perform trade - trading not active");
        return false;
    }
    
    try {
        // This would contain the actual trading logic
        // For now, just log that trading is being performed
        logger_.Info("Performing trade operations");
        
        // Trading would be handled by the DF trading interface
        // This is a placeholder for more complex trading logic
        
        return true;
        
    } catch (const std::exception& e) {
        logger_.Error("Exception performing trade: {}", e.what());
        return false;
    }
}

bool PopulationManager::OrderRandomSquadAttackUnit(color_ostream& out, df::unit* target, const std::string& reason) {
    if (!target) return false;
    
    auto* squad = GetRandomActiveSquad();
    if (!squad) {
        logger_.Warning("No active squads available for attack order");
        return false;
    }
    
    return OrderSquadAttackUnit(out, squad, target, reason);
}

bool PopulationManager::CancelAttackOrder(color_ostream& out, df::unit* target, const std::string& reason) {
    if (!target || !world) return false;
    
    int orders_cancelled = 0;
    
    try {
        for (auto* squad : world->squads.all) {
            if (!squad) continue;
            
            auto it = std::remove_if(squad->orders.begin(), squad->orders.end(),
                [target](df::squad_order* order) {
                    if (auto* kill_order = virtual_cast<df::squad_order_kill_listst>(order)) {
                        auto unit_it = std::find(kill_order->units.begin(), kill_order->units.end(), target->id);
                        if (unit_it != kill_order->units.end()) {
                            kill_order->units.erase(unit_it);
                            return kill_order->units.empty(); // Remove order if no targets left
                        }
                    }
                    return false;
                });
            
            if (it != squad->orders.end()) {
                for (auto order_it = it; order_it != squad->orders.end(); ++order_it) {
                    delete *order_it;
                    orders_cancelled++;
                }
                squad->orders.erase(it, squad->orders.end());
            }
        }
        
        if (orders_cancelled > 0) {
            logger_.Info("Cancelled {} attack orders for target {} - reason: {}", 
                        orders_cancelled, target->id, reason);
        }
        
        return orders_cancelled > 0;
        
    } catch (const std::exception& e) {
        logger_.Error("Exception cancelling attack orders: {}", e.what());
        return false;
    }
}

// Implementation for complex methods with DF integration
void PopulationManager::UpdateCitizenList(color_ostream& out) {
    // Update citizen list from DF unit structures
    citizens_.clear();
    
    // This would iterate through world->units.active in real implementation
    // For now, maintain a basic count and status
    logger_.Debug("Scanning for citizens and updating population records");
    
    // Basic population tracking
    int32_t citizen_count = 0;
    int32_t visitor_count = 0;
    int32_t resident_count = 0;
    
    // In real implementation, would check unit flags and relationships
    // citizens_.push_back(unit_id) for each valid citizen
    
    logger_.Info("Population update: {} citizens, {} visitors, {} residents", 
                citizen_count, visitor_count, resident_count);
}

void PopulationManager::UpdateMilitary(color_ostream& out) {
    // Update military assignments and status
    military_units_.clear();
    
    logger_.Debug("Updating military unit assignments and equipment status");
    
    // This would check squad assignments and equipment in real implementation
    // For now, maintain basic military tracking
    int32_t active_military = 0;
    int32_t training_military = 0;
    int32_t off_duty_military = 0;
    
    logger_.Info("Military update: {} active, {} training, {} off-duty", 
                active_military, training_military, off_duty_military);
}

void PopulationManager::UpdatePets(color_ostream& out) {
    logger_.Debug("Updating pets and livestock");
    
    if (!world) {
        logger_.Warning("World data not available for pet updates");
        return;
    }
    
    try {
        // Clear existing pet data for revalidation
        std::set<int32_t> old_pets = pet_check_queue_;
        pet_check_queue_.clear();
        
        // Scan all active units for pets and livestock
        for (auto* unit : world->units.active) {
            if (!unit || Units::isDead(unit) || Units::isOpposedToLife(unit)) {
                continue;
            }
            
            // Skip citizens and visitors
            if (Units::isCitizen(unit) || Units::isVisitor(unit) || Units::isResident(unit)) {
                continue;
            }
            
            // Check if this is a pet or livestock
            if (IsValidPet(unit)) {
                UpdatePetFlags(unit->id, unit);
                pet_check_queue_.insert(unit->id);
                
                // Process pet assignment if needed
                if (old_pets.find(unit->id) == old_pets.end()) {
                    logger_.Info("New pet detected: {} ({})", unit->id, Units::getReadableName(unit));
                }
            }
        }
        
        // Remove pets that are no longer valid
        for (auto pet_id : old_pets) {
            if (pet_check_queue_.find(pet_id) == pet_check_queue_.end()) {
                pets_.erase(pet_id);
                logger_.Info("Pet {} is no longer valid, removed from tracking", pet_id);
            }
        }
        
        logger_.Info("Updated {} pets/livestock", pets_.size());
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in UpdatePets: {}", e.what());
    }
}

void PopulationManager::UpdateVisitors(color_ostream& out) {
    logger_.Debug("Updating visitors and residents");
    
    if (!world) {
        logger_.Warning("World data not available for visitor updates");
        return;
    }
    
    try {
        visitors_.clear();
        residents_.clear();
        
        for (auto* unit : world->units.active) {
            if (!unit || Units::isDead(unit)) {
                continue;
            }
            
            if (Units::isVisitor(unit)) {
                visitors_.insert(unit->id);
                
                // Check if visitor should become a resident
                if (unit->flags1.bits.merchant || unit->flags2.bits.visitor_uninvited) {
                    // Skip merchants and uninvited visitors
                    continue;
                }
                
                // Check if visitor has been here long enough or has petition status
                if (unit->relations.pet_owner_id != -1 || 
                    unit->status.misc_traits.size() > 0) {
                    // Visitor has established connections, potential resident
                    residents_.insert(unit->id);
                    logger_.Debug("Visitor {} flagged as potential resident", unit->id);
                }
            }
        }
        
        logger_.Info("Updated {} visitors, {} potential residents", 
                    visitors_.size(), residents_.size());
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in UpdateVisitors: {}", e.what());
    }
}

void PopulationManager::UpdateJobs(color_ostream& out) {
    logger_.Debug("Updating job assignments and labor efficiency");
    
    if (!world) {
        logger_.Warning("World data not available for job updates");
        return;
    }
    
    try {
        problematic_jobs_.clear();
        int32_t job_count = 0;
        int32_t unassigned_jobs = 0;
        
        // Check all active jobs
        for (auto* job : world->jobs.list.all) {
            if (!job) continue;
            
            job_count++;
            
            // Check if job has been assigned
            if (job->worker == nullptr) {
                unassigned_jobs++;
                
                // Check if this is a problematic job type
                if (job->job_type == job_type::Rest || 
                    job->job_type == job_type::Sleep ||
                    job->job_type == job_type::PickupEquipment) {
                    // These are normal unassigned jobs
                    continue;
                }
                
                // Mark as potentially problematic if unassigned for too long
                if (job->completion_timer > 100) { // Arbitrary threshold
                    problematic_jobs_.insert(job->job_type);
                }
            }
        }
        
        // Update medic assignments
        medics_.clear();
        for (auto citizen_id : citizens_) {
            auto* unit = df::unit::find(citizen_id);
            if (!unit || Units::isDead(unit)) continue;
            
            // Check if unit has medical labors enabled
            if (unit->status.labors[unit_labor::DIAGNOSE] ||
                unit->status.labors[unit_labor::SURGERY] ||
                unit->status.labors[unit_labor::BONE_SETTING] ||
                unit->status.labors[unit_labor::SUTURING] ||
                unit->status.labors[unit_labor::DRESSING_WOUNDS]) {
                medics_.insert(unit->id);
            }
        }
        
        logger_.Info("Job update: {} total jobs, {} unassigned, {} problematic types, {} medics", 
                    job_count, unassigned_jobs, problematic_jobs_.size(), medics_.size());
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in UpdateJobs: {}", e.what());
    }
}

void PopulationManager::UpdateWorkers(color_ostream& out) {
    logger_.Debug("Updating worker assignments and efficiency");
    
    if (!world) {
        logger_.Warning("World data not available for worker updates");
        return;
    }
    
    try {
        workers_.clear();
        
        // Categorize citizens by their work assignments
        int32_t idle_workers = 0;
        int32_t busy_workers = 0;
        int32_t military_workers = 0;
        
        for (auto citizen_id : citizens_) {
            auto* unit = df::unit::find(citizen_id);
            if (!unit || Units::isDead(unit)) continue;
            
            workers_.push_back(unit->id);
            
            // Check work status
            if (IsMilitary(unit->id)) {
                military_workers++;
                continue;
            }
            
            // Check if unit has an active job
            bool has_job = false;
            if (unit->job.current_job) {
                has_job = true;
                busy_workers++;
            } else {
                // Check if unit is traveling to a job
                if (unit->path.dest.x != -30000) { // Has a destination
                    has_job = true;
                    busy_workers++;
                }
            }
            
            if (!has_job) {
                idle_workers++;
                
                // Check if unit should be assigned work
                bool has_enabled_labors = false;
                for (size_t i = 0; i < unit->status.labors.size(); ++i) {
                    if (unit->status.labors[i]) {
                        has_enabled_labors = true;
                        break;
                    }
                }
                
                if (!has_enabled_labors) {
                    logger_.Debug("Worker {} has no enabled labors", unit->id);
                }
            }
        }
        
        logger_.Info("Worker update: {} total, {} busy, {} idle, {} military", 
                    workers_.size(), busy_workers, idle_workers, military_workers);
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in UpdateWorkers: {}", e.what());
    }
}

void PopulationManager::UpdateDeaths(color_ostream& out) {
    logger_.Debug("Updating death tracking and memorial management");
    
    if (!world) {
        logger_.Warning("World data not available for death updates");
        return;
    }
    
    try {
        size_t current_deaths = 0;
        
        // Count current deaths
        for (auto* unit : world->units.all) {
            if (unit && Units::isDead(unit)) {
                current_deaths++;
            }
        }
        
        // Check if there are new deaths
        if (current_deaths > deaths_seen_) {
            size_t new_deaths = current_deaths - deaths_seen_;
            logger_.Info("Detected {} new deaths, total deaths: {}", new_deaths, current_deaths);
            
            // Trigger any death-related processing
            for (auto* unit : world->units.all) {
                if (unit && Units::isDead(unit) && unit->counters.death_time != -1) {
                    // Check if this is a recent death (within last few ticks)
                    int32_t death_age = world->cur_year_tick - unit->counters.death_time;
                    if (death_age < 100) { // Recent death
                        OnUnitDeath(out, unit->id);
                    }
                }
            }
            
            deaths_seen_ = current_deaths;
        }
        
        // Check for memorial needs
        size_t slabs_needed = 0;
        for (auto* unit : world->units.all) {
            if (unit && Units::isDead(unit) && Units::isCitizen(unit)) {
                // Check if citizen needs a memorial slab
                bool has_memorial = false;
                for (auto* building : world->buildings.all) {
                    if (auto* slab = virtual_cast<df::building_slabst>(building)) {
                        if (slab->unit_id == unit->id) {
                            has_memorial = true;
                            break;
                        }
                    }
                }
                
                if (!has_memorial) {
                    slabs_needed++;
                }
            }
        }
        
        if (slabs_needed > 0) {
            logger_.Info("Memorial slabs needed: {}", slabs_needed);
        }
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in UpdateDeaths: {}", e.what());
    }
}

void PopulationManager::UpdateCaged(color_ostream& out) {
    logger_.Debug("Updating caged units and prison management");
    
    if (!world) {
        logger_.Warning("World data not available for caged unit updates");
        return;
    }
    
    try {
        int32_t caged_count = 0;
        int32_t prisoner_count = 0;
        
        for (auto* unit : world->units.active) {
            if (!unit || Units::isDead(unit)) continue;
            
            // Check if unit is caged
            if (unit->flags1.bits.caged) {
                caged_count++;
                
                // Check if this is a prisoner vs captured animal
                if (Units::isIntelligent(unit) && !Units::isOwnCiv(unit)) {
                    prisoner_count++;
                    
                    // Check if prisoner should be processed
                    if (unit->relations.enemy_status_id != -1) {
                        // This is a hostile prisoner
                        logger_.Debug("Hostile prisoner detected: {} ({})", 
                                    unit->id, Units::getReadableName(unit));
                        
                        // Check if prisoner needs to be stripped of items
                        bool has_items = false;
                        for (auto* item : unit->inventory) {
                            if (item && !item->item->flags.bits.dump) {
                                has_items = true;
                                // Mark for dumping
                                item->item->flags.bits.dump = true;
                                item->item->flags.bits.forbid = false;
                            }
                        }
                        
                        if (has_items) {
                            logger_.Debug("Marked prisoner {} items for dumping", unit->id);
                        }
                    }
                }
                
                // Check cage condition and accessibility
                if (auto* cage = df::building::find(unit->caged_in)) {
                    if (cage->getBuildStage() != cage->getMaxBuildStage()) {
                        logger_.Warning("Unit {} is in incomplete cage {}", unit->id, cage->id);
                    }
                }
            }
        }
        
        logger_.Info("Caged units: {} total, {} prisoners", caged_count, prisoner_count);
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in UpdateCaged: {}", e.what());
    }
}

void PopulationManager::UpdateNobles(color_ostream& out) {
    logger_.Debug("Updating noble assignments and requirements");
    
    if (!world || !world->entities.all.empty()) {
        logger_.Warning("World or entity data not available for noble updates");
        return;
    }
    
    try {
        auto* fort_entity = world->entities.all[0]; // Fortress entity
        if (!fort_entity) {
            logger_.Warning("Fortress entity not found");
            return;
        }
        
        // Track noble positions and assignments
        std::map<df::entity_position_responsibility, std::vector<int32_t>> noble_assignments;
        
        // Check current noble assignments
        for (auto& assignment_pair : fort_entity->assignments_by_type) {
            auto responsibility = assignment_pair.first;
            auto& assignments = assignment_pair.second;
            
            for (auto* assignment : assignments) {
                if (assignment && assignment->histfig != -1) {
                    auto* hf = df::historical_figure::find(assignment->histfig);
                    if (hf && hf->unit_id != -1) {
                        noble_assignments[responsibility].push_back(hf->unit_id);
                    }
                }
            }
        }
        
        // Check for needed positions
        std::vector<df::entity_position_responsibility> needed_positions;
        
        // Essential positions
        if (noble_assignments[entity_position_responsibility::MANAGE_PRODUCTION].empty()) {
            needed_positions.push_back(entity_position_responsibility::MANAGE_PRODUCTION);
        }
        
        if (noble_assignments[entity_position_responsibility::ACCOUNTING].empty()) {
            needed_positions.push_back(entity_position_responsibility::ACCOUNTING);
        }
        
        if (noble_assignments[entity_position_responsibility::TRADE].empty()) {
            needed_positions.push_back(entity_position_responsibility::TRADE);
        }
        
        // Check for infirmary before assigning health management
        bool has_infirmary = false;
        for (auto* building : world->buildings.all) {
            if (building && building->getType() == building_type::Bed) {
                // Check if this is in a hospital zone
                auto* bed = virtual_cast<df::building_bedst>(building);
                if (bed && bed->hospital.room_id != -1) {
                    has_infirmary = true;
                    break;
                }
            }
        }
        
        if (has_infirmary && noble_assignments[entity_position_responsibility::HEALTH_MANAGEMENT].empty()) {
            needed_positions.push_back(entity_position_responsibility::HEALTH_MANAGEMENT);
        }
        
        // Check for justice positions if we have justice infrastructure
        bool has_jail = false;
        for (auto* building : world->buildings.all) {
            if (building && building->getType() == building_type::Cage) {
                // This is a simple check - could be more sophisticated
                has_jail = true;
                break;
            }
        }
        
        if (has_jail) {
            if (noble_assignments[entity_position_responsibility::LAW_ENFORCEMENT].empty()) {
                needed_positions.push_back(entity_position_responsibility::LAW_ENFORCEMENT);
            }
            if (noble_assignments[entity_position_responsibility::EXECUTIONS].empty()) {
                needed_positions.push_back(entity_position_responsibility::EXECUTIONS);
            }
        }
        
        logger_.Info("Noble status: {} positions filled, {} positions needed", 
                    noble_assignments.size(), needed_positions.size());
        
        for (auto responsibility : needed_positions) {
            logger_.Debug("Need noble for responsibility: {}", static_cast<int>(responsibility));
        }
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in UpdateNobles: {}", e.what());
    }
}

void PopulationManager::UpdateCrimes(color_ostream& out) {
    logger_.Debug("Updating crime tracking and justice system");
    
    if (!world) {
        logger_.Warning("World data not available for crime updates");
        return;
    }
    
    try {
        // Only check crimes periodically to avoid performance issues
        bool should_check = false;
        if (last_crime_check_year_ != world->cur_year) {
            should_check = true;
            last_crime_check_year_ = world->cur_year;
            last_crime_check_tick_ = world->cur_year_tick;
        } else if (world->cur_year_tick - last_crime_check_tick_ > 1200) { // Check once per day
            should_check = true;
            last_crime_check_tick_ = world->cur_year_tick;
        }
        
        if (!should_check) {
            return;
        }
        
        int32_t crime_count = 0;
        int32_t conviction_count = 0;
        
        // Check incidents (crimes)
        for (auto* incident : world->incidents.all) {
            if (!incident) continue;
            
            crime_count++;
            
            // Check if this incident has been resolved
            if (!incident->outcome.empty()) {
                conviction_count++;
                
                // Process conviction if needed
                for (auto* outcome : incident->outcome) {
                    if (!outcome) continue;
                    
                    // Check for punishment assignments
                    if (outcome->punishment_type != punishment_type::None) {
                        logger_.Debug("Crime {} resolved with punishment type {}", 
                                    incident->id, static_cast<int>(outcome->punishment_type));
                    }
                }
            }
        }
        
        // Check for units needing justice attention
        int32_t criminals_at_large = 0;
        for (auto* unit : world->units.active) {
            if (!unit || Units::isDead(unit)) continue;
            
            // Check if unit has active criminal status
            if (!unit->military.convictions.empty()) {
                bool has_unresolved_conviction = false;
                for (auto* conviction : unit->military.convictions) {
                    if (conviction && !conviction->sentence_satisfied) {
                        has_unresolved_conviction = true;
                        break;
                    }
                }
                
                if (has_unresolved_conviction) {
                    criminals_at_large++;
                    logger_.Debug("Unit {} has unresolved conviction", unit->id);
                }
            }
        }
        
        if (crime_count > 0 || criminals_at_large > 0) {
            logger_.Info("Crime update: {} incidents, {} resolved, {} criminals at large", 
                        crime_count, conviction_count, criminals_at_large);
        }
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in UpdateCrimes: {}", e.what());
    }
}

void PopulationManager::UpdateLocations(color_ostream& out) {
    logger_.Debug("Updating location assignments and occupation management");
    
    if (!world) {
        logger_.Warning("World data not available for location updates");
        return;
    }
    
    try {
        int32_t tavern_count = 0;
        int32_t temple_count = 0;
        int32_t library_count = 0;
        int32_t occupied_locations = 0;
        
        // Check all abstract buildings (locations)
        for (auto* location : world->buildings.all) {
            if (!location) continue;
            
            auto* abstract_bld = virtual_cast<df::abstract_building>(location);
            if (!abstract_bld) continue;
            
            // Check location type and occupancy
            switch (abstract_bld->getType()) {
                case abstract_building_type::TEMPLE:
                    temple_count++;
                    break;
                case abstract_building_type::GUILDHALL:
                    // Could be various types including taverns
                    if (auto* guildhall = virtual_cast<df::abstract_building_guildhallst>(abstract_bld)) {
                        // Check if this is functioning as a tavern
                        tavern_count++;
                    }
                    break;
                case abstract_building_type::LIBRARY:
                    library_count++;
                    break;
                default:
                    break;
            }
            
            // Check if location has assigned occupations
            bool has_occupants = false;
            for (auto* building : abstract_bld->buildings) {
                if (!building) continue;
                
                // Check for assigned occupations
                if (building->owner != -1) {
                    has_occupants = true;
                    occupied_locations++;
                    break;
                }
            }
            
            // If location lacks occupants, try to assign them
            if (!has_occupants) {
                // Find suitable candidates for occupation
                for (auto citizen_id : citizens_) {
                    auto* unit = df::unit::find(citizen_id);
                    if (!unit || Units::isDead(unit)) continue;
                    
                    // Check if unit is suitable for this location type
                    if (IsUnitSuitableForLocation(unit, abstract_bld)) {
                        // Attempt assignment (this would be more complex in real implementation)
                        logger_.Debug("Could assign unit {} to location {}", 
                                    unit->id, abstract_bld->id);
                        break;
                    }
                }
            }
        }
        
        logger_.Info("Location update: {} taverns, {} temples, {} libraries, {} occupied", 
                    tavern_count, temple_count, library_count, occupied_locations);
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in UpdateLocations: {}", e.what());
    }
}

void PopulationManager::UpdateTrading(color_ostream& out) {
    logger_.Debug("Updating trading status and caravan management");
    
    if (!world) {
        logger_.Warning("World data not available for trading updates");
        return;
    }
    
    try {
        bool caravans_present = false;
        int32_t trader_count = 0;
        
        // Check for active caravans
        for (auto* unit : world->units.active) {
            if (!unit || Units::isDead(unit)) continue;
            
            if (unit->flags1.bits.merchant) {
                trader_count++;
                caravans_present = true;
                
                // Check if this trader is at the trade depot
                bool at_depot = false;
                for (auto* building : world->buildings.all) {
                    if (building && building->getType() == building_type::TradeDepot) {
                        auto* depot = virtual_cast<df::building_tradedepotst>(building);
                        if (depot && depot->getBuildStage() == depot->getMaxBuildStage()) {
                            // Check if trader is near depot (simplified check)
                            int32_t dx = abs(unit->pos.x - depot->centerx);
                            int32_t dy = abs(unit->pos.y - depot->centery);
                            if (dx <= 3 && dy <= 3) { // Within reasonable distance
                                at_depot = true;
                                break;
                            }
                        }
                    }
                }
                
                if (at_depot) {
                    logger_.Debug("Trader {} is at trade depot", unit->id);
                    
                    // Check if we should initiate trading
                    if (!currently_trading_) {
                        // Find suitable trader from our citizens
                        df::unit* our_trader = nullptr;
                        for (auto citizen_id : citizens_) {
                            auto* citizen = df::unit::find(citizen_id);
                            if (citizen && IsUnitSuitableForTrading(citizen)) {
                                our_trader = citizen;
                                break;
                            }
                        }
                        
                        if (our_trader) {
                            logger_.Info("Potential trading opportunity with trader {} using citizen {}", 
                                        unit->id, our_trader->id);
                            
                            // Store trading position for reference
                            trade_start_x_ = unit->pos.x;
                            trade_start_y_ = unit->pos.y;
                            trade_start_z_ = unit->pos.z;
                        }
                    }
                }
            }
        }
        
        // Update trading state
        if (caravans_present && !currently_trading_) {
            logger_.Info("Caravans present: {} traders detected", trader_count);
        } else if (!caravans_present && currently_trading_) {
            logger_.Info("No caravans present, ending trading session");
            currently_trading_ = false;
        }
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in UpdateTrading: {}", e.what());
    }
}

void PopulationManager::ProcessSquadOrders(color_ostream& out) {
    logger_.Debug("Processing pending squad orders");
    
    if (!world || pending_squad_orders_.empty()) {
        return;
    }
    
    try {
        auto it = pending_squad_orders_.begin();
        while (it != pending_squad_orders_.end()) {
            auto& order = *it;
            bool processed = false;
            
            switch (order.type) {
                case SquadOrderChange::kill: {
                    auto* squad = df::squad::find(order.squad_id);
                    auto* target = df::unit::find(order.unit_id);
                    
                    if (squad && target) {
                        if (order.remove) {
                            // Remove attack order
                            auto order_it = std::find_if(squad->orders.begin(), squad->orders.end(),
                                [target](df::squad_order* order) {
                                    if (auto* kill_order = virtual_cast<df::squad_order_kill_listst>(order)) {
                                        return std::find(kill_order->units.begin(), kill_order->units.end(), 
                                                       target->id) != kill_order->units.end();
                                    }
                                    return false;
                                });
                            
                            if (order_it != squad->orders.end()) {
                                delete *order_it;
                                squad->orders.erase(order_it);
                                logger_.Info("Removed attack order for squad {} target {} - reason: {}", 
                                           order.squad_id, order.unit_id, order.reason);
                                processed = true;
                            }
                        } else {
                            // Add attack order
                            if (OrderSquadAttackUnit(out, squad, target, order.reason)) {
                                processed = true;
                            }
                        }
                    } else {
                        // Squad or target no longer exists
                        processed = true;
                    }
                    break;
                }
            }
            
            if (processed) {
                it = pending_squad_orders_.erase(it);
            } else {
                ++it;
            }
        }
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in ProcessSquadOrders: {}", e.what());
    }
}

void PopulationManager::CheckMilitaryNeeds(color_ostream& out) {
    logger_.Debug("Checking military needs and recruitment");
    
    if (!world || citizens_.empty()) {
        return;
    }
    
    try {
        // Count current military personnel
        int32_t active_military = 0;
        int32_t available_for_military = 0;
        
        for (auto citizen_id : citizens_) {
            auto* unit = df::unit::find(citizen_id);
            if (!unit || Units::isDead(unit)) continue;
            
            if (IsMilitary(citizen_id)) {
                active_military++;
            } else if (ShouldAssignToMilitary(unit)) {
                available_for_military++;
            }
        }
        
        // Calculate military needs
        int32_t total_citizens = static_cast<int32_t>(citizens_.size());
        int32_t min_military = std::max(1, (total_citizens * military_min_size_) / 100);
        int32_t max_military = std::max(min_military, (total_citizens * military_max_size_) / 100);
        
        logger_.Info("Military status: {}/{} active, {} available for recruitment", 
                    active_military, max_military, available_for_military);
        
        // Check if we need more military
        if (active_military < min_military && available_for_military > 0) {
            int32_t needed = std::min(min_military - active_military, available_for_military);
            logger_.Info("Need to recruit {} more military personnel", needed);
            
            // Find best candidates for military service
            std::vector<std::pair<df::unit*, int32_t>> candidates;
            
            for (auto citizen_id : citizens_) {
                auto* unit = df::unit::find(citizen_id);
                if (!unit || !ShouldAssignToMilitary(unit)) continue;
                
                // Calculate military suitability score
                int32_t score = CalculateUnitTotalExperience(unit);
                
                // Bonus for physical attributes
                if (unit->body.physical_attrs.size() > physical_attribute_type::STRENGTH) {
                    score += unit->body.physical_attrs[physical_attribute_type::STRENGTH].value / 10;
                }
                if (unit->body.physical_attrs.size() > physical_attribute_type::TOUGHNESS) {
                    score += unit->body.physical_attrs[physical_attribute_type::TOUGHNESS].value / 10;
                }
                
                // Bonus for weapon skills
                for (auto& skill : unit->status.skills) {
                    if (skill->id >= job_skill::AXE && skill->id <= job_skill::CROSSBOW) {
                        score += skill->experience / 100;
                    }
                }
                
                candidates.emplace_back(unit, score);
            }
            
            // Sort by suitability score
            std::sort(candidates.begin(), candidates.end(), 
                     [](const auto& a, const auto& b) { return a.second > b.second; });
            
            // Assign best candidates to military
            int32_t recruited = 0;
            for (auto& candidate : candidates) {
                if (recruited >= needed) break;
                
                // Find available squad
                df::squad* available_squad = FindBestSquadForUnit(candidate.first);
                if (available_squad) {
                    AssignToMilitary(candidate.first->id, available_squad->id);
                    recruited++;
                    logger_.Info("Recruited {} to military squad {}", 
                               candidate.first->id, available_squad->id);
                }
            }
        }
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in CheckMilitaryNeeds: {}", e.what());
    }
}

void PopulationManager::CheckNobleApartments(color_ostream& out) {
    logger_.Debug("Checking noble quarters and accommodation needs");
    
    if (!world || !world->entities.all.empty()) {
        logger_.Warning("World or entity data not available for noble apartment checks");
        return;
    }
    
    try {
        auto* fort_entity = world->entities.all[0];
        if (!fort_entity) return;
        
        int32_t nobles_needing_rooms = 0;
        int32_t nobles_with_rooms = 0;
        
        // Check each noble assignment
        for (auto& assignment_pair : fort_entity->assignments_by_type) {
            auto& assignments = assignment_pair.second;
            
            for (auto* assignment : assignments) {
                if (!assignment || assignment->histfig == -1) continue;
                
                auto* hf = df::historical_figure::find(assignment->histfig);
                if (!hf || hf->unit_id == -1) continue;
                
                auto* unit = df::unit::find(hf->unit_id);
                if (!unit || Units::isDead(unit)) continue;
                
                // Check if noble has appropriate quarters
                bool has_room = false;
                df::building* assigned_room = nullptr;
                
                // Check for assigned bedroom
                for (auto* building : world->buildings.all) {
                    if (!building || building->getType() != building_type::Bed) continue;
                    
                    auto* bed = virtual_cast<df::building_bedst>(building);
                    if (bed && bed->owner == unit->id) {
                        assigned_room = bed;
                        has_room = true;
                        break;
                    }
                }
                
                if (has_room) {
                    nobles_with_rooms++;
                    
                    // Check if room meets noble requirements
                    if (assigned_room) {
                        // Check room size and value
                        int32_t room_size = (assigned_room->x2 - assigned_room->x1 + 1) * 
                                          (assigned_room->y2 - assigned_room->y1 + 1);
                        
                        // Basic room quality check
                        if (room_size < 4) { // Minimum 2x2 room
                            logger_.Debug("Noble {} has inadequate room size: {}", 
                                        unit->id, room_size);
                        }
                        
                        // Check for required furniture
                        bool has_cabinet = false;
                        bool has_chest = false;
                        
                        for (auto* furniture : world->buildings.all) {
                            if (!furniture || furniture->owner != unit->id) continue;
                            
                            if (furniture->getType() == building_type::Cabinet) {
                                has_cabinet = true;
                            } else if (furniture->getType() == building_type::Box) {
                                has_chest = true;
                            }
                        }
                        
                        if (!has_cabinet || !has_chest) {
                            logger_.Debug("Noble {} missing required furniture", unit->id);
                        }
                    }
                } else {
                    nobles_needing_rooms++;
                    logger_.Info("Noble {} needs quarters assigned", unit->id);
                }
            }
        }
        
        logger_.Info("Noble accommodation: {} with rooms, {} need rooms", 
                    nobles_with_rooms, nobles_needing_rooms);
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in CheckNobleApartments: {}", e.what());
    }
}

// Static utility methods
int32_t PopulationManager::CalculateUnitTotalExperience(const df::unit* unit) {
    if (!unit) return 0;
    
    int32_t total_exp = 0;
    
    try {
        // Sum up experience from all skills
        for (const auto& skill : unit->status.skills) {
            total_exp += skill->experience;
        }
        
        // Add experience from attributes
        const auto& attributes = unit->body.physical_attrs;
        for (size_t i = 0; i < attributes.size(); ++i) {
            total_exp += attributes[i].value / 10; // Scale down attribute values
        }
        
        // Add experience from mental attributes
        const auto& mental_attrs = unit->status.mental_attrs;
        for (size_t i = 0; i < mental_attrs.size(); ++i) {
            total_exp += mental_attrs[i].value / 10;
        }
        
        // Add military experience if applicable
        if (unit->military.squad_id != -1) {
            // Add bonus for military service
            total_exp += 1000;
            
            // Add experience based on combat history
            if (unit->counters.death_tally > 0) {
                total_exp += unit->counters.death_tally * 500;
            }
            
            if (unit->counters.kill_count > 0) {
                total_exp += unit->counters.kill_count * 300;
            }
        }
        
        return total_exp;
        
    } catch (const std::exception& e) {
        logger_.Error(stl_sprintf("Exception calculating experience for unit %d: %s", 
            unit->id, e.what()));
        return 0;
    }
}

int32_t PopulationManager::DaysSince(int32_t year, int32_t tick) {
    if (!world) return 0;
    
    try {
        // Calculate days between given date and current date
        int32_t current_year = world->cur_year;
        int32_t current_tick = world->cur_year_tick;
        
        // Simple calculation - this could be more sophisticated
        int32_t year_diff = current_year - year;
        int32_t tick_diff = current_tick - tick;
        
        // Each year has roughly 403200 ticks (28 days * 12 months * 1200 ticks/day)
        // Each day has 1200 ticks
        const int32_t TICKS_PER_DAY = 1200;
        const int32_t TICKS_PER_YEAR = 403200;
        
        int32_t total_days = (year_diff * TICKS_PER_YEAR + tick_diff) / TICKS_PER_DAY;
        
        return std::max(0, total_days);
        
    } catch (const std::exception& e) {
        logger_.Error(stl_sprintf("Exception calculating days since %d:%d: %s", 
            year, tick, e.what()));
        return 0;
    }
}

bool PopulationManager::HasMilitaryDuty(df::unit* unit) const {
    if (!unit) return false;
    
    try {
        // Check if unit is assigned to a squad
        if (unit->military.squad_id != -1) {
            auto* squad = df::squad::find(unit->military.squad_id);
            if (squad && squad->activity != -1) {
                return true; // Squad has active military duty
            }
            return true; // Unit is in a military squad
        }
        
        // Check if unit has military-related labors enabled
        if (unit->status.labors[unit_labor::MECHANIC] && 
            unit->status.labors[unit_labor::SIEGE_ENGINEER]) {
            return true; // Military engineering duties
        }
        
        // Check if unit is assigned as a fortress guard or similar
        if (unit->military.squad_position != -1) {
            return true;
        }
        
        return false;
        
    } catch (const std::exception& e) {
        logger_.Error(stl_sprintf("Exception checking military duty for unit %d: %s", 
            unit->id, e.what()));
        return false;
    }
}

bool PopulationManager::OrderSquadAttackUnit(color_ostream& out, df::squad* squad, df::unit* target, const std::string& reason) {
    if (!squad || !target) return false;
    try {
        // Check if already attacking this target
        for (auto* order : squad->orders) {
            if (auto* kill = virtual_cast<df::squad_order_kill_listst>(order)) {
                for (auto unit_id : kill->units) {
                    if (unit_id == target->id) {
                        logger_.Info(stl_sprintf("Squad %s already ordered to attack %s", squad->name.c_str(), Units::getReadableName(target).c_str()));
                        return false;
                    }
                }
            }
        }
        // Remove conflicting orders (e.g., defend, patrol, etc.)
        squad->orders.erase(std::remove_if(squad->orders.begin(), squad->orders.end(), [](df::squad_order* order) {
            return !virtual_cast<df::squad_order_kill_listst>(order);
        }), squad->orders.end());
        // Create new attack order
        auto* kill_order = new df::squad_order_kill_listst();
        kill_order->units.push_back(target->id);
        kill_order->squad_id = squad->id;
        kill_order->unk1 = 0; // placeholder for any required field
        squad->orders.push_back(kill_order);
        squad->activity = squad_activity::Attack;
        logger_.Info(stl_sprintf("Squad %s ordered to attack %s - reason: %s", squad->name.c_str(), Units::getReadableName(target).c_str(), reason.c_str()));
        return true;
    } catch (const std::exception& e) {
        logger_.Error(stl_sprintf("Exception ordering squad attack: %s", e.what()));
        return false;
    }
}

bool PopulationManager::OrderAllSquadsAttackUnit(color_ostream& out, df::unit* target, const std::string& reason) {
    if (!target) return false;
    int orders_issued = 0;
    for (auto* squad : world->squads.all) {
        if (!squad || squad->positions.empty()) continue;
        // Check if squad is ready (has active members, not already attacking this target)
        int active_members = 0;
        for (auto* pos : squad->positions) {
            if (pos && pos->occupant != -1) {
                auto* unit = df::unit::find(pos->occupant);
                if (unit && !Units::isDead(unit)) {
                    active_members++;
                }
            }
        }
        if (active_members == 0) continue;
        // Check if already has an attack order for this target
        bool already_ordered = false;
        for (auto* order : squad->orders) {
            if (auto* kill = virtual_cast<df::squad_order_kill_listst>(order)) {
                for (auto unit_id : kill->units) {
                    if (unit_id == target->id) {
                        already_ordered = true;
                        break;
                    }
                }
            }
            if (already_ordered) break;
        }
        if (!already_ordered) {
            if (OrderSquadAttackUnit(out, squad, target, reason)) {
                orders_issued++;
            }
        }
    }
    logger_.Info(stl_sprintf("Issued attack orders to %d squads for target %s", orders_issued, Units::getReadableName(target).c_str()));
    return orders_issued > 0;
}

// Helper method implementations
void PopulationManager::UpdatePetFlags(int32_t unit_id, df::unit* unit) {
    if (!unit) return;
    
    try {
        PetFlags flags;
        
        if (unit->race >= 0 && unit->caste >= 0) {
            auto* creature = df::creature_raw::find(unit->race);
            if (creature && unit->caste < (int32_t)creature->caste.size()) {
                auto* caste = creature->caste[unit->caste];
                
                flags.bits.milkable = caste->flags.is_set(caste_raw_flags::MILKABLE) ? 1 : 0;
                flags.bits.shearable = !caste->shearable_tissue_layer.empty() ? 1 : 0;
                flags.bits.hunts_vermin = caste->flags.is_set(caste_raw_flags::HUNTS_VERMIN) ? 1 : 0;
                flags.bits.trainable = (caste->flags.is_set(caste_raw_flags::TRAINABLE_HUNTING) || 
                                       caste->flags.is_set(caste_raw_flags::TRAINABLE_WAR)) ? 1 : 0;
                flags.bits.grazer = caste->flags.is_set(caste_raw_flags::GRAZER) ? 1 : 0;
                flags.bits.lays_eggs = caste->flags.is_set(caste_raw_flags::LAYS_EGGS) ? 1 : 0;
            }
        }
        
        pets_[unit_id] = flags;
        
    } catch (const std::exception& e) {
        logger_.Error("Exception updating pet flags for unit {}: {}", unit_id, e.what());
    }
}

bool PopulationManager::IsValidPet(df::unit* unit) const {
    if (!unit || Units::isDead(unit) || Units::isOpposedToLife(unit)) {
        return false;
    }
    
    // Skip intelligent units
    if (Units::isIntelligent(unit)) {
        return false;
    }
    
    // Skip citizens, visitors, residents
    if (Units::isCitizen(unit) || Units::isVisitor(unit) || Units::isResident(unit)) {
        return false;
    }
    
    // Check if this is a domesticated animal or livestock
    if (unit->race >= 0 && unit->caste >= 0) {
        auto* creature = df::creature_raw::find(unit->race);
        if (creature && unit->caste < (int32_t)creature->caste.size()) {
            auto* caste = creature->caste[unit->caste];
            
            // Check if it's a domestic animal
            if (caste->flags.is_set(caste_raw_flags::MILKABLE) ||
                caste->flags.is_set(caste_raw_flags::GRAZER) ||
                caste->flags.is_set(caste_raw_flags::LAYS_EGGS) ||
                !caste->shearable_tissue_layer.empty() ||
                caste->flags.is_set(caste_raw_flags::TRAINABLE_HUNTING) ||
                caste->flags.is_set(caste_raw_flags::TRAINABLE_WAR)) {
                return true;
            }
        }
    }
    
    return false;
}

bool PopulationManager::IsUnitSuitableForTrading(df::unit* unit) const {
    if (!unit || Units::isDead(unit) || !Units::isCitizen(unit)) {
        return false;
    }
    
    // Check if unit has trading-related skills
    if (unit->status.labors[unit_labor::APPRAISER] ||
        unit->status.labors[unit_labor::TRADER] ||
        unit->status.labors[unit_labor::MERCHANT]) {
        return true;
    }
    
    // Check if unit has high social skills
    for (auto& skill : unit->status.skills) {
        if (skill->id == job_skill::NEGOTIATOR ||
            skill->id == job_skill::JUDGE_INTENT ||
            skill->id == job_skill::APPRAISER) {
            if (skill->rating >= skill_rating::Competent) {
                return true;
            }
        }
    }
    
    return false;
}

bool PopulationManager::IsUnitSuitableForLocation(df::unit* unit, df::abstract_building* location) const {
    if (!unit || !location || Units::isDead(unit) || !Units::isCitizen(unit)) {
        return false;
    }
    
    // Check if unit is already assigned to a location
    if (unit->relationship_ids[unit_relationship_type::Lover] != -1) {
        return false; // Has other commitments
    }
    
    // Check location type suitability
    switch (location->getType()) {
        case abstract_building_type::TEMPLE:
            // Prefer units with high focus or meditation skills
            return unit->status.labors[unit_labor::PRAY] ||
                   unit->body.mental_attrs[mental_attribute_type::FOCUS] > 1000;
            
        case abstract_building_type::LIBRARY:
            // Prefer scholars and those with reading/writing skills
            return unit->status.labors[unit_labor::SCHOLAR] ||
                   unit->status.labors[unit_labor::SCRIBE];
            
        case abstract_building_type::GUILDHALL:
            // Check if unit has relevant craft skills
            for (size_t i = 0; i < unit->status.labors.size(); ++i) {
                if (unit->status.labors[i]) {
                    // This is a simplified check - could be more sophisticated
                    return true;
                }
            }
            break;
            
        default:
            break;
    }
    
    return false;
}

void PopulationManager::OnUnitDeath(color_ostream& out, int32_t unit_id) {
    logger_.Info("Processing death of unit {}", unit_id);
    
    // Remove unit from all tracking lists
    citizens_.erase(unit_id);
    military_assignments_.erase(unit_id);
    pets_.erase(unit_id);
    visitors_.erase(unit_id);
    residents_.erase(unit_id);
    medics_.erase(unit_id);
    workers_.erase(std::remove(workers_.begin(), workers_.end(), unit_id), workers_.end());
    
    // Additional death processing could go here
    // e.g., memorial creation, inheritance, etc.
}

void PopulationManager::OnUnitBirth(color_ostream& out, int32_t unit_id) {
    logger_.Info("Processing birth of unit {}", unit_id);
    
    auto* unit = df::unit::find(unit_id);
    if (!unit) return;
    
    // Check if this is a citizen birth
    if (Units::isCitizen(unit)) {
        // Will be added to citizens list on next update
        logger_.Info("New citizen born: {}", unit_id);
    } else if (IsValidPet(unit)) {
        // Will be added to pets list on next update
        logger_.Info("New animal born: {}", unit_id);
    }
}

void PopulationManager::OnMigrantArrival(color_ostream& out, int32_t unit_id) {
    logger_.Info("Processing migrant arrival: {}", unit_id);
    
    auto* unit = df::unit::find(unit_id);
    if (!unit) return;
    
    if (Units::isCitizen(unit)) {
        AddCitizen(out, unit_id);
        logger_.Info("New migrant citizen: {} ({})", unit_id, Units::getReadableName(unit));
    }
}

void PopulationManager::OnUnitLeaving(color_ostream& out, int32_t unit_id) {
    logger_.Info("Processing unit departure: {}", unit_id);
    
    // Clean up all references to this unit
    OnUnitDeath(out, unit_id); // Reuse the cleanup logic
}

bool PopulationManager::ShouldAssignToMilitary(df::unit* unit) const {
    if (!unit || Units::isDead(unit) || !Units::isCitizen(unit)) {
        return false;
    }
    
    // Skip if already in military
    if (IsMilitary(unit->id)) {
        return false;
    }
    
    // Skip children and babies
    if (Units::isChild(unit) || Units::isBaby(unit)) {
        return false;
    }
    
    // Skip nobles with important positions
    std::vector<Units::NoblePosition> positions;
    if (Units::getNoblePositions(&positions, unit) && !positions.empty()) {
        return false;
    }
    
    // Skip if unit has critical civilian job
    if (unit->status.labors[unit_labor::DIAGNOSE] ||
        unit->status.labors[unit_labor::SURGERY] ||
        unit->status.labors[unit_labor::APPRAISER] ||
        unit->status.labors[unit_labor::TRADER]) {
        return false;
    }
    
    // Skip if unit is in a strange mood
    if (unit->mood != mood_type::None) {
        return false;
    }
    
    return true;
}

df::squad* PopulationManager::FindBestSquadForUnit(df::unit* unit) const {
    if (!unit || !world) return nullptr;
    
    // Find squad with open positions
    for (auto* squad : world->squads.all) {
        if (!squad || squad->positions.empty()) continue;
        
        // Count filled positions
        int32_t filled_positions = 0;
        for (auto* position : squad->positions) {
            if (position && position->occupant != -1) {
                auto* unit = df::unit::find(position->occupant);
                if (unit && !Units::isDead(unit)) {
                    filled_positions++;
                }
            }
        }
        
        // If squad has open positions, it's suitable
        if (filled_positions < static_cast<int32_t>(squad->positions.size())) {
            return squad;
        }
    }
    
    return nullptr;
}

df::squad* PopulationManager::GetRandomActiveSquad() const {
    if (!world) return nullptr;
    
    std::vector<df::squad*> active_squads;
    
    for (auto* squad : world->squads.all) {
        if (!squad || squad->positions.empty()) continue;
        
        // Check if squad has active members
        bool has_active = false;
        for (auto* position : squad->positions) {
            if (position && position->occupant != -1) {
                auto* unit = df::unit::find(position->occupant);
                if (unit && !Units::isDead(unit)) {
                    has_active = true;
                    break;
                }
            }
        }
        
        if (has_active) {
            active_squads.push_back(squad);
        }
    }
    
    if (active_squads.empty()) {
        return nullptr;
    }
    
    // Return random squad
    return active_squads[rand() % active_squads.size()];
}

PetFlags PopulationManager::AnalyzePetCapabilities(df::unit* unit) const {
    PetFlags flags;
    
    if (!unit || unit->race < 0 || unit->caste < 0) {
        return flags;
    }
    
    auto* creature = df::creature_raw::find(unit->race);
    if (!creature || unit->caste >= (int32_t)creature->caste.size()) {
        return flags;
    }
    
    auto* caste = creature->caste[unit->caste];
    
    flags.bits.milkable = caste->flags.is_set(caste_raw_flags::MILKABLE) ? 1 : 0;
    flags.bits.shearable = !caste->shearable_tissue_layer.empty() ? 1 : 0;
    flags.bits.hunts_vermin = caste->flags.is_set(caste_raw_flags::HUNTS_VERMIN) ? 1 : 0;
    flags.bits.trainable = (caste->flags.is_set(caste_raw_flags::TRAINABLE_HUNTING) || 
                           caste->flags.is_set(caste_raw_flags::TRAINABLE_WAR)) ? 1 : 0;
    flags.bits.grazer = caste->flags.is_set(caste_raw_flags::GRAZER) ? 1 : 0;
    flags.bits.lays_eggs = caste->flags.is_set(caste_raw_flags::LAYS_EGGS) ? 1 : 0;
    
    return flags;
}

std::string PopulationManager::GetUnitDescription(df::unit* unit) const {
    if (!unit) return "Unknown unit";
    
    try {
        std::string name = Units::getReadableName(unit);
        if (name.empty()) {
            name = "Unnamed";
        }
        
        std::string profession = "Unknown";
        if (unit->profession >= 0 && unit->profession < profession::NONE) {
            // Get profession name (simplified)
            profession = std::to_string(static_cast<int>(unit->profession));
        }
        
        return name + " (" + profession + ")";
        
    } catch (const std::exception& e) {
        return "Unit " + std::to_string(unit->id);
    }
}
