#pragma once

// Common DFHack includes
#include <Core.h>
#include <Console.h>
#include <Export.h>
#include <PluginManager.h>
#include <DataDefs.h>

// Standard library includes
#include <map>
#include <memory>
#include <set>
#include <tuple>
#include <string>
#include <vector>
#include <functional>
#include <iostream>
#include <fstream>

// DataFrame includes  
#include "df/coord.h"
#include "df/interface_key.h"
#include "df/language_name.h"
#include "df/unit.h"
#include "df/item.h"
#include "df/building.h"
#include "df/viewscreen.h"

// Json support
#include "json/json.h"

// DFHack namespace usage
using namespace DFHack;
using namespace df::enums;

// Forward declarations
struct room;
struct furniture;
struct task;
class OnupdateCallback;
enum class state_change_event;

// Common type aliases
using coord_t = df::coord;
using unit_ptr = df::unit*;
using item_ptr = df::item*;
using building_ptr = df::building*;
using room_ptr = room*;

// Common constants
namespace df_ai {
namespace constants {
    constexpr int DEFAULT_TIMEOUT = 30;
    constexpr int MAX_RETRIES = 3;
    constexpr int TICK_FREQUENCY = 600;
}
}

// Utility macros
#define DFAI_ASSERT(condition, message) \
    do { \
        if (!(condition)) { \
            throw std::runtime_error(message); \
        } \
    } while(0)

#define DFAI_LOG_ERROR(out, message) \
    do { \
        out.printerr("df-ai error: %s\n", (message).c_str()); \
    } while(0)

#define DFAI_LOG_INFO(out, message) \
    do { \
        out.print("df-ai: %s\n", (message).c_str()); \
    } while(0)

// Common result types
enum class Result {
    SUCCESS,
    FAILURE,
    RETRY,
    SKIP
};

// Common enumerations and types from the original codebase
#include "../../room.h"

// Make sure nullptr is properly defined
#ifdef nullptr
#undef nullptr
#endif

// Support for older compilers
#if defined(__GNUC__) && __cplusplus == 201103L
namespace std {
    template<typename T, typename... Args>
    std::unique_ptr<T> make_unique(Args&&... args) {
        return std::unique_ptr<T>(new T(std::forward<Args>(args)...));
    }
}
#endif
