#include "game_state.h"
#include "df_interface.h"
#include "../common/types.h"
#include "modules/World.h"
#include "modules/Gui.h"
#include "df/world.h"
#include "df/ui.h"
#include "df/viewscreen.h"
#include "df/viewscreen_dwarfmodest.h"
#include "df/viewscreen_titlest.h"

namespace dfai {
namespace utils {
namespace df {

// Game state detection
GameState GetCurrentGameState() {
    if (!::world) {
        return GameState::INVALID;
    }
    
    if (DFHack::World::isFortressMode()) {
        if (IsPaused()) {
            return GameState::PAUSED;
        } else {
            return GameState::FORTRESS_MODE;
        }
    } else if (DFHack::World::isAdventureMode()) {
        return GameState::ADVENTURE_MODE;
    } else if (DFHack::World::isArena()) {
        return GameState::ARENA_MODE;
    } else if (DFHack::World::isLegends()) {
        return GameState::LEGENDS_MODE;
    }
    
    // Check if we're in the main menu
    df::viewscreen* screen = DFHack::Gui::getCurViewscreen();
    if (screen && virtual_cast<df::viewscreen_titlest>(screen)) {
        return GameState::MENU;
    }
    
    return GameState::LOADING;
}

bool IsGameStateValid() {
    return GetCurrentGameState() != GameState::INVALID;
}

bool IsInFortressMode() {
    return GetCurrentGameState() == GameState::FORTRESS_MODE;
}

bool IsInAdventureMode() {
    return GetCurrentGameState() == GameState::ADVENTURE_MODE;
}

bool IsInMenu() {
    return GetCurrentGameState() == GameState::MENU;
}

bool IsGamePaused() {
    return GetCurrentGameState() == GameState::PAUSED;
}

bool IsGameLoading() {
    return GetCurrentGameState() == GameState::LOADING;
}

std::string GameStateToString(GameState state) {
    switch (state) {
        case GameState::INVALID: return "Invalid";
        case GameState::LOADING: return "Loading";
        case GameState::MENU: return "Menu";
        case GameState::FORTRESS_MODE: return "Fortress Mode";
        case GameState::ADVENTURE_MODE: return "Adventure Mode";
        case GameState::ARENA_MODE: return "Arena Mode";
        case GameState::LEGENDS_MODE: return "Legends Mode";
        case GameState::PAUSED: return "Paused";
        case GameState::ERROR_STATE: return "Error";
        default: return "Unknown";
    }
}

// World information
WorldInfo GetWorldInfo() {
    WorldInfo info;
    
    if (!::world) {
        return info;
    }
    
    info.current_year = GetCurrentYear();
    info.current_season = GetCurrentSeason();
    info.current_tick = GetCurrentTick();
    info.fortress_name = GetFortressName();
    info.is_paused = IsPaused();
    info.game_state = GetCurrentGameState();
    
    // Get world size information
    if (::world->world_data.world_width > 0 && ::world->world_data.world_height > 0) {
        info.world_width = ::world->world_data.world_width;
        info.world_height = ::world->world_data.world_height;
    }
    
    // Get embark information
    if (::world->world_data.active_site.size() > 0 && ::world->world_data.active_site[0]) {
        auto site = ::world->world_data.active_site[0];
        info.embark_x = site->global_min_x;
        info.embark_y = site->global_min_y;
        info.embark_width = site->global_max_x - site->global_min_x + 1;
        info.embark_height = site->global_max_y - site->global_min_y + 1;
    }
    
    return info;
}

FortressInfo GetFortressInfo() {
    FortressInfo info;
    
    if (!IsInFortressMode() && !IsGamePaused()) {
        return info;
    }
    
    info.name = GetFortressName();
    info.population_count = static_cast<int>(GetCitizenUnits().size());
    info.year_founded = GetCurrentYear(); // This would need more specific calculation
    info.wealth = 0; // Would need to calculate from items/buildings
    
    // Get basic statistics
    auto citizens = GetCitizenUnits();
    info.population_count = static_cast<int>(citizens.size());
    
    // Count different unit types
    for (auto unit : citizens) {
        if (IsUnitAlive(unit)) {
            info.living_count++;
        }
    }
    
    auto enemies = GetEnemyUnits();
    info.enemy_count = static_cast<int>(enemies.size());
    
    // Get building counts
    auto buildings = GetAllBuildings();
    info.building_count = static_cast<int>(buildings.size());
    
    return info;
}

// Screen and UI state
df::viewscreen* GetCurrentScreen() {
    return DFHack::Gui::getCurViewscreen();
}

bool IsScreenType(const std::string& screen_type) {
    df::viewscreen* screen = GetCurrentScreen();
    if (!screen) {
        return false;
    }
    
    // This is a simplified check - would need more specific type checking
    return screen->_identity.getFullName().find(screen_type) != std::string::npos;
}

bool IsInDwarfMode() {
    df::viewscreen* screen = GetCurrentScreen();
    return screen && virtual_cast<df::viewscreen_dwarfmodest>(screen);
}

bool IsInMainMenu() {
    df::viewscreen* screen = GetCurrentScreen();
    return screen && virtual_cast<df::viewscreen_titlest>(screen);
}

Coordinate GetViewportCenter() {
    if (!::ui) {
        return {0, 0, 0};
    }
    
    return {
        ::ui->main.view_rect.map_x1 + (::ui->main.view_rect.map_x2 - ::ui->main.view_rect.map_x1) / 2,
        ::ui->main.view_rect.map_y1 + (::ui->main.view_rect.map_y2 - ::ui->main.view_rect.map_y1) / 2,
        ::ui->main.view_rect.map_z
    };
}

Rectangle GetViewportBounds() {
    if (!::ui) {
        return {{0, 0, 0}, {0, 0, 0}};
    }
    
    return {
        {::ui->main.view_rect.map_x1, ::ui->main.view_rect.map_y1, ::ui->main.view_rect.map_z},
        {::ui->main.view_rect.map_x2, ::ui->main.view_rect.map_y2, ::ui->main.view_rect.map_z}
    };
}

bool SetViewportCenter(const Coordinate& coord) {
    if (!::ui || !IsValidCoordinate(coord)) {
        return false;
    }
    
    // Calculate viewport size
    int view_width = ::ui->main.view_rect.map_x2 - ::ui->main.view_rect.map_x1 + 1;
    int view_height = ::ui->main.view_rect.map_y2 - ::ui->main.view_rect.map_y1 + 1;
    
    // Set new viewport bounds centered on the coordinate
    ::ui->main.view_rect.map_x1 = coord.x - view_width / 2;
    ::ui->main.view_rect.map_x2 = coord.x + view_width / 2;
    ::ui->main.view_rect.map_y1 = coord.y - view_height / 2;
    ::ui->main.view_rect.map_y2 = coord.y + view_height / 2;
    ::ui->main.view_rect.map_z = coord.z;
    
    return true;
}

// Time and calendar
int GetCurrentYear() {
    return ::world ? ::world->cur_year : 0;
}

int GetCurrentSeason() {
    return ::world ? ::world->cur_season : 0;
}

int GetCurrentMonth() {
    return ::world ? ::world->cur_season_tick / 33600 : 0; // Approximate
}

int GetCurrentDay() {
    return ::world ? (::world->cur_season_tick % 33600) / 1200 : 0; // Approximate
}

int GetCurrentTick() {
    return ::world ? ::world->frame_counter : 0;
}

std::string GetSeasonName(int season) {
    switch (season) {
        case 0: return "Spring";
        case 1: return "Summer";
        case 2: return "Autumn";
        case 3: return "Winter";
        default: return "Unknown";
    }
}

std::string GetCurrentSeasonName() {
    return GetSeasonName(GetCurrentSeason());
}

std::string GetDateString() {
    int year = GetCurrentYear();
    int season = GetCurrentSeason();
    int month = GetCurrentMonth();
    int day = GetCurrentDay();
    
    return std::to_string(day) + " " + GetSeasonName(season) + " " + std::to_string(year);
}

// Game speed and timing
int GetGameSpeed() {
    return ::ui ? ::ui->main.fps_counter : 0;
}

bool SetGameSpeed(int speed) {
    if (!::ui || speed < 0 || speed > 100) {
        return false;
    }
    
    // This would need proper implementation to set game speed
    // For now, just return true as a placeholder
    return true;
}

float GetFPS() {
    return ::ui ? static_cast<float>(::ui->main.fps_counter) : 0.0f;
}

// Save/Load state
bool CanSaveGame() {
    return IsInFortressMode() && !IsGamePaused();
}

bool CanLoadGame() {
    return IsInMenu();
}

std::string GetSaveGameName() {
    if (!::world || !::world->world_data.name.has_name) {
        return "Unknown";
    }
    
    return DFHack::Translation::TranslateName(&::world->world_data.name, false);
}

// Performance monitoring
PerformanceInfo GetPerformanceInfo() {
    PerformanceInfo info;
    
    if (::ui) {
        info.fps = static_cast<float>(::ui->main.fps_counter);
        info.frame_time_ms = 1000.0f / std::max(1.0f, info.fps);
    }
    
    // These would need proper implementation
    info.memory_usage_mb = 0.0f;
    info.cpu_usage_percent = 0.0f;
    
    return info;
}

// Event detection
bool HasNewAnnouncements() {
    // This would need to track announcement state
    return false;
}

bool HasNewJobs() {
    // This would need to track job state
    return false;
}

bool HasNewUnits() {
    // This would need to track unit state
    return false;
}

bool HasGameStateChanged() {
    // This would need to track previous game state
    static GameState last_state = GameState::INVALID;
    GameState current_state = GetCurrentGameState();
    
    if (current_state != last_state) {
        last_state = current_state;
        return true;
    }
    
    return false;
}

// Utility functions
void WaitForGameState(GameState target_state, int timeout_seconds) {
    // This would need proper implementation with timing
    // For now, just return immediately
}

bool WaitForScreenChange(int timeout_seconds) {
    // This would need proper implementation with timing
    return false;
}

void RefreshScreen() {
    // This would trigger a screen refresh in DF
}

} // namespace df
} // namespace utils
} // namespace dfai
