#pragma once

#include "../utils/common/common.h" 
#include "../debug/logging.h"
#include "population_types.h"

#include <vector>
#include <map>
#include <set>

// Forward declarations
class color_ostream;
namespace df {
    struct unit;
    struct entity_position_assignment;
}

namespace dfai {
namespace population {

/**
 * Manages nobles, their positions, and their apartment requirements
 */
class NoblesManager {
public:
    NoblesManager();
    ~NoblesManager();
    
    // Core lifecycle
    bool Initialize(color_ostream& out);
    void Shutdown();
    void Update(color_ostream& out);
    
    // Noble management
    void CheckNoblePositions(color_ostream& out);
    void CheckNobleApartments(color_ostream& out);
    void AssignNobleRooms(color_ostream& out);
    
    // Queries
    bool IsNoble(int32_t unit_id) const;
    std::vector<int32_t> GetNobles() const;
    std::string GetNobleTitle(int32_t unit_id) const;
    
    // Status reporting
    std::string GetStatusReport() const;

private:
    debug::Logger& logger_;
    bool initialized_;
    
    // Noble tracking
    std::map<int32_t, std::string> nobles_; // unit_id -> title
    std::set<int32_t> nobles_needing_rooms_;
    
    // Helper methods
    void UpdateNoblesList(color_ostream& out);
    void CheckNobleRoomRequirements(color_ostream& out, df::unit* unit);
    bool HasAdequateRooms(df::unit* unit) const;
    void RequestNobleRooms(color_ostream& out, df::unit* unit);
};

} // namespace population
} // namespace dfai
