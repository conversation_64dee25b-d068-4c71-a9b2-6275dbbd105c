#include "command_processor.h"
#include "ai_controller.h"
#include "../config/config_manager.h"

namespace dfai {
namespace core {

CommandProcessor::CommandProcessor(AIController& controller)
    : controller_(controller)
    , logger_(debug::Logger::GetInstance())
    , initialized_(false)
{
    logger_.Info("CommandProcessor created");
}

CommandProcessor::~CommandProcessor() {
    logger_.Info("CommandProcessor destroyed");
}

void CommandProcessor::Initialize() {
    if (initialized_) {
        logger_.Warning("CommandProcessor already initialized");
        return;
    }

    logger_.Info("Initializing CommandProcessor...");
    RegisterBuiltinCommands();
    initialized_ = true;
    logger_.Info("CommandProcessor initialization complete");
}

CommandProcessor::CommandResult CommandProcessor::ProcessCommand(
    color_ostream& out, 
    const std::string& command, 
    const std::vector<std::string>& parameters) {
    
    if (!initialized_) {
        out.printerr("df-ai: Command processor not initialized\n");
        return CommandResult::SYSTEM_NOT_READY;
    }

    auto it = registered_commands_.find(command);
    if (it == registered_commands_.end()) {
        out.printerr("df-ai: Unknown command '%s'. Use 'ai help' for available commands.\n", 
                    command.c_str());
        return CommandResult::INVALID_COMMAND;
    }

    const CommandInfo& cmd_info = it->second;

    // Check if system is ready for this command
    if (!CheckSystemReady(cmd_info)) {
        out.printerr("df-ai: System not ready for command '%s'\n", command.c_str());
        return CommandResult::SYSTEM_NOT_READY;
    }

    // Check permissions
    if (!CheckPermissions(cmd_info)) {
        out.printerr("df-ai: Insufficient permissions for command '%s'\n", command.c_str());
        return CommandResult::PERMISSION_DENIED;
    }

    // Validate parameters
    if (!ValidateCommand(command, parameters)) {
        out.printerr("df-ai: Invalid parameters for command '%s'. Usage: %s\n", 
                    command.c_str(), cmd_info.usage.c_str());
        return CommandResult::INVALID_PARAMETERS;
    }

    try {
        logger_.Info("Executing command: {} with {} parameters", command, parameters.size());
        CommandResult result = cmd_info.handler(out, parameters);
        
        if (result == CommandResult::SUCCESS) {
            logger_.Info("Command '{}' executed successfully", command);
        } else {
            logger_.Warning("Command '{}' failed with result: {}", 
                          command, CommandResultToString(result));
        }
        
        return result;
        
    } catch (const std::exception& e) {
        logger_.Error("Exception executing command '{}': {}", command, e.what());
        out.printerr("df-ai: Error executing command '%s': %s\n", command.c_str(), e.what());
        return CommandResult::FAILURE;
    }
}

void CommandProcessor::RegisterCommand(const CommandInfo& command_info) {
    if (registered_commands_.find(command_info.name) != registered_commands_.end()) {
        logger_.Warning("Overriding existing command: {}", command_info.name);
    }
    
    registered_commands_[command_info.name] = command_info;
    logger_.Info("Registered command: {}", command_info.name);
}

void CommandProcessor::UnregisterCommand(const std::string& command_name) {
    auto it = registered_commands_.find(command_name);
    if (it != registered_commands_.end()) {
        registered_commands_.erase(it);
        logger_.Info("Unregistered command: {}", command_name);
    }
}

bool CommandProcessor::IsCommandRegistered(const std::string& command_name) const {
    return registered_commands_.find(command_name) != registered_commands_.end();
}

void CommandProcessor::ShowHelp(color_ostream& out, const std::string& command) const {
    if (command.empty()) {
        out << "df-ai: Available commands:\n";
        FormatCommandList(out);
    } else {
        auto it = registered_commands_.find(command);
        if (it != registered_commands_.end()) {
            FormatCommandHelp(out, it->second);
        } else {
            out.printerr("df-ai: Unknown command '%s'\n", command.c_str());
        }
    }
}

void CommandProcessor::ShowVersion(color_ostream& out) const {
    out << "df-ai Enhanced v" << DF_AI_VERSION_MAJOR << "." 
        << DF_AI_VERSION_MINOR << "." << DF_AI_VERSION_PATCH << std::endl;
    
    // Add git information if available
    #ifdef DF_AI_GIT_DESCRIPTION
    out << "Git: " << DF_AI_GIT_DESCRIPTION << std::endl;
    #endif
}

void CommandProcessor::ShowStatus(color_ostream& out) const {
    out << "df-ai: " << controller_.GetStatusReport() << std::endl;
}

bool CommandProcessor::ValidateCommand(const std::string& command, 
                                      const std::vector<std::string>& parameters) const {
    // Basic validation - specific commands can override this
    auto it = registered_commands_.find(command);
    if (it == registered_commands_.end()) {
        return false;
    }
    
    // For now, just return true - specific validation will be added per command
    return true;
}

void CommandProcessor::RegisterBuiltinCommands() {
    // Status command
    RegisterCommand({
        "status", 
        "Show AI status", 
        "ai status",
        [this](color_ostream& out, const std::vector<std::string>& params) {
            return HandleStatusCommand(out, params);
        },
        false, false
    });

    // Version command
    RegisterCommand({
        "version", 
        "Show version information", 
        "ai version",
        [this](color_ostream& out, const std::vector<std::string>& params) {
            return HandleVersionCommand(out, params);
        },
        false, false
    });

    // Help command
    RegisterCommand({
        "help", 
        "Show help information", 
        "ai help [command]",
        [this](color_ostream& out, const std::vector<std::string>& params) {
            return HandleHelpCommand(out, params);
        },
        false, false
    });

    // Report command
    RegisterCommand({
        "report", 
        "Generate detailed status report", 
        "ai report",
        [this](color_ostream& out, const std::vector<std::string>& params) {
            return HandleReportCommand(out, params);
        },
        true, false
    });

    // Config command
    RegisterCommand({
        "config", 
        "Manage configuration", 
        "ai config [get|set|reload] [key] [value]",
        [this](color_ostream& out, const std::vector<std::string>& params) {
            return HandleConfigCommand(out, params);
        },
        false, false
    });

    // Debug command
    RegisterCommand({
        "debug", 
        "Debug operations", 
        "ai debug [level|dump|test]",
        [this](color_ostream& out, const std::vector<std::string>& params) {
            return HandleDebugCommand(out, params);
        },
        false, true
    });

    // Pause command
    RegisterCommand({
        "pause", 
        "Pause AI operations", 
        "ai pause",
        [this](color_ostream& out, const std::vector<std::string>& params) {
            return HandlePauseCommand(out, params);
        },
        true, false
    });

    // Resume command
    RegisterCommand({
        "resume", 
        "Resume AI operations", 
        "ai resume",
        [this](color_ostream& out, const std::vector<std::string>& params) {
            return HandleResumeCommand(out, params);
        },
        true, false
    });

    // Shutdown command
    RegisterCommand({
        "shutdown", 
        "Shutdown AI system", 
        "ai shutdown",
        [this](color_ostream& out, const std::vector<std::string>& params) {
            return HandleShutdownCommand(out, params);
        },
        true, false
    });

    // Reload command
    RegisterCommand({
        "reload", 
        "Reload configuration", 
        "ai reload",
        [this](color_ostream& out, const std::vector<std::string>& params) {
            return HandleReloadCommand(out, params);
        },
        true, false
    });

    logger_.Info("Registered {} built-in commands", registered_commands_.size());
}

// Command handler implementations

CommandProcessor::CommandResult CommandProcessor::HandleStatusCommand(
    color_ostream& out, const std::vector<std::string>& params) {
    ShowStatus(out);
    return CommandResult::SUCCESS;
}

CommandProcessor::CommandResult CommandProcessor::HandleVersionCommand(
    color_ostream& out, const std::vector<std::string>& params) {
    ShowVersion(out);
    return CommandResult::SUCCESS;
}

CommandProcessor::CommandResult CommandProcessor::HandleHelpCommand(
    color_ostream& out, const std::vector<std::string>& params) {
    std::string command = params.empty() ? "" : params[0];
    ShowHelp(out, command);
    return CommandResult::SUCCESS;
}

CommandProcessor::CommandResult CommandProcessor::HandleReportCommand(
    color_ostream& out, const std::vector<std::string>& params) {
    out << "df-ai: Generating detailed report..." << std::endl;
    // Report generation will be implemented
    return CommandResult::SUCCESS;
}

CommandProcessor::CommandResult CommandProcessor::HandleConfigCommand(
    color_ostream& out, const std::vector<std::string>& params) {
    if (params.empty()) {
        out << "df-ai: Config command requires parameters. Usage: ai config [get|set|reload] [key] [value]" << std::endl;
        return CommandResult::INVALID_PARAMETERS;
    }

    const std::string& action = params[0];

    if (action == "reload") {
        out << "df-ai: Reloading configuration..." << std::endl;
        // Configuration reload will be implemented
        return CommandResult::SUCCESS;
    } else if (action == "get" && params.size() >= 2) {
        out << "df-ai: Getting config value for: " << params[1] << std::endl;
        // Config get will be implemented
        return CommandResult::SUCCESS;
    } else if (action == "set" && params.size() >= 3) {
        out << "df-ai: Setting config " << params[1] << " = " << params[2] << std::endl;
        // Config set will be implemented
        return CommandResult::SUCCESS;
    }

    return CommandResult::INVALID_PARAMETERS;
}

CommandProcessor::CommandResult CommandProcessor::HandleDebugCommand(
    color_ostream& out, const std::vector<std::string>& params) {
    out << "df-ai: Debug command executed" << std::endl;
    // Debug functionality will be implemented
    return CommandResult::SUCCESS;
}

CommandProcessor::CommandResult CommandProcessor::HandlePauseCommand(
    color_ostream& out, const std::vector<std::string>& params) {
    out << "df-ai: Pausing AI operations..." << std::endl;
    // Pause functionality will be implemented
    return CommandResult::SUCCESS;
}

CommandProcessor::CommandResult CommandProcessor::HandleResumeCommand(
    color_ostream& out, const std::vector<std::string>& params) {
    out << "df-ai: Resuming AI operations..." << std::endl;
    // Resume functionality will be implemented
    return CommandResult::SUCCESS;
}

CommandProcessor::CommandResult CommandProcessor::HandleShutdownCommand(
    color_ostream& out, const std::vector<std::string>& params) {
    out << "df-ai: Shutting down AI system..." << std::endl;
    controller_.Shutdown();
    return CommandResult::SUCCESS;
}

CommandProcessor::CommandResult CommandProcessor::HandleReloadCommand(
    color_ostream& out, const std::vector<std::string>& params) {
    out << "df-ai: Reloading system..." << std::endl;
    // Reload functionality will be implemented
    return CommandResult::SUCCESS;
}

// Utility method implementations

std::string CommandProcessor::CommandResultToString(CommandResult result) const {
    switch (result) {
        case CommandResult::SUCCESS: return "Success";
        case CommandResult::FAILURE: return "Failure";
        case CommandResult::INVALID_COMMAND: return "Invalid Command";
        case CommandResult::INVALID_PARAMETERS: return "Invalid Parameters";
        case CommandResult::SYSTEM_NOT_READY: return "System Not Ready";
        case CommandResult::PERMISSION_DENIED: return "Permission Denied";
        default: return "Unknown";
    }
}

bool CommandProcessor::CheckPermissions(const CommandInfo& command) const {
    // For now, all commands are allowed
    // Admin-only commands will be implemented later
    return true;
}

bool CommandProcessor::CheckSystemReady(const CommandInfo& command) const {
    if (!command.requires_initialization) {
        return true;
    }

    // Check if AI controller is properly initialized
    return controller_.IsInitialized();
}

void CommandProcessor::FormatCommandList(color_ostream& out) const {
    for (const auto& pair : registered_commands_) {
        const CommandInfo& cmd = pair.second;
        out << "  " << cmd.name << " - " << cmd.description << std::endl;
    }
}

void CommandProcessor::FormatCommandHelp(color_ostream& out, const CommandInfo& command) const {
    out << "Command: " << command.name << std::endl;
    out << "Description: " << command.description << std::endl;
    out << "Usage: " << command.usage << std::endl;
    if (command.requires_initialization) {
        out << "Note: Requires AI system to be initialized" << std::endl;
    }
    if (command.admin_only) {
        out << "Note: Admin-only command" << std::endl;
    }
}

bool CommandProcessor::ParseBoolParameter(const std::string& param, bool& result) const {
    if (param == "true" || param == "1" || param == "yes" || param == "on") {
        result = true;
        return true;
    } else if (param == "false" || param == "0" || param == "no" || param == "off") {
        result = false;
        return true;
    }
    return false;
}

bool CommandProcessor::ParseIntParameter(const std::string& param, int& result) const {
    try {
        result = std::stoi(param);
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool CommandProcessor::ParseFloatParameter(const std::string& param, float& result) const {
    try {
        result = std::stof(param);
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

} // namespace core
} // namespace dfai
