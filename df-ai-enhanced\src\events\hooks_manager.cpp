#include "hooks_manager.h"
#include "core/event_manager.h"
#include <algorithm>
#include <sstream>

namespace dfai {
namespace events {

// Static instance for callbacks
HooksManager* HooksManager::instance_ = nullptr;

HooksManager::HooksManager()
    : initialized_(false)
    , logger_(debug::Logger::GetInstance())
    , event_manager_(nullptr)
{
    instance_ = this;
    logger_.Info("HooksManager created");
}

HooksManager::~HooksManager() {
    if (initialized_) {
        Shutdown();
    }
    instance_ = nullptr;
}

bool HooksManager::Initialize() {
    if (initialized_) {
        logger_.Warning("HooksManager already initialized");
        return true;
    }

    logger_.Info("Initializing HooksManager...");
    
    std::lock_guard<std::mutex> lock(hooks_mutex_);
    hooks_.clear();
    hooks_by_type_.clear();
    dfhack_hooks_.clear();
    
    // Setup DFHack hooks
    if (!SetupDFHackHooks()) {
        logger_.Error("Failed to setup DFHack hooks");
        return false;
    }
    
    initialized_ = true;
    logger_.Info("HooksManager initialization complete");
    return true;
}

void HooksManager::Shutdown() {
    if (!initialized_) {
        return;
    }

    logger_.Info("Shutting down HooksManager...");
    
    // Cleanup DFHack hooks first
    CleanupDFHackHooks();
    
    std::lock_guard<std::mutex> lock(hooks_mutex_);
    
    // Log final statistics
    LogHookStatistics();
    
    // Clear all hooks
    hooks_.clear();
    hooks_by_type_.clear();
    dfhack_hooks_.clear();
    
    initialized_ = false;
    logger_.Info("HooksManager shutdown complete");
}

std::string HooksManager::RegisterHook(HookType type, const std::string& description) {
    if (!initialized_) {
        logger_.Error("HooksManager not initialized");
        return "";
    }

    if (!ValidateHookType(type)) {
        logger_.Error("Invalid hook type");
        return "";
    }

    std::lock_guard<std::mutex> lock(hooks_mutex_);
    
    std::string hook_id = GenerateHookId();
    auto hook_info = std::make_unique<HookInfo>(hook_id, type, description);
    
    hooks_[hook_id] = std::move(hook_info);
    AddHookToTypeMap(hook_id, type);
    
    logger_.Info("Registered hook '{}' for type {}", hook_id, HookTypeToString(type));
    
    return hook_id;
}

bool HooksManager::UnregisterHook(const std::string& hook_id) {
    if (!initialized_) {
        logger_.Error("HooksManager not initialized");
        return false;
    }

    std::lock_guard<std::mutex> lock(hooks_mutex_);
    
    auto it = hooks_.find(hook_id);
    if (it == hooks_.end()) {
        logger_.Warning("Hook '{}' not found for unregistration", hook_id);
        return false;
    }

    HookType type = it->second->type;
    RemoveHookFromTypeMap(hook_id, type);
    hooks_.erase(it);
    
    logger_.Info("Unregistered hook '{}'", hook_id);
    return true;
}

bool HooksManager::UnregisterHooksByType(HookType type) {
    if (!initialized_) {
        logger_.Error("HooksManager not initialized");
        return false;
    }

    std::lock_guard<std::mutex> lock(hooks_mutex_);
    
    auto type_it = hooks_by_type_.find(type);
    if (type_it == hooks_by_type_.end()) {
        return true; // No hooks of this type
    }

    std::vector<std::string> hook_ids = type_it->second;
    for (const std::string& hook_id : hook_ids) {
        hooks_.erase(hook_id);
    }
    
    hooks_by_type_.erase(type_it);
    
    logger_.Info("Unregistered {} hooks for type {}", 
                hook_ids.size(), HookTypeToString(type));
    return true;
}

void HooksManager::UnregisterAllHooks() {
    if (!initialized_) {
        return;
    }

    std::lock_guard<std::mutex> lock(hooks_mutex_);
    
    size_t count = hooks_.size();
    hooks_.clear();
    hooks_by_type_.clear();
    
    logger_.Info("Unregistered all {} hooks", count);
}

bool HooksManager::EnableHook(const std::string& hook_id) {
    std::lock_guard<std::mutex> lock(hooks_mutex_);
    
    auto it = hooks_.find(hook_id);
    if (it == hooks_.end()) {
        return false;
    }

    it->second->enabled = true;
    logger_.Info("Enabled hook '{}'", hook_id);
    return true;
}

bool HooksManager::DisableHook(const std::string& hook_id) {
    std::lock_guard<std::mutex> lock(hooks_mutex_);
    
    auto it = hooks_.find(hook_id);
    if (it == hooks_.end()) {
        return false;
    }

    it->second->enabled = false;
    logger_.Info("Disabled hook '{}'", hook_id);
    return true;
}

bool HooksManager::IsHookRegistered(const std::string& hook_id) const {
    std::lock_guard<std::mutex> lock(hooks_mutex_);
    return hooks_.find(hook_id) != hooks_.end();
}

bool HooksManager::IsHookEnabled(const std::string& hook_id) const {
    std::lock_guard<std::mutex> lock(hooks_mutex_);
    
    auto it = hooks_.find(hook_id);
    return it != hooks_.end() && it->second->enabled;
}

void HooksManager::TriggerHook(HookType type, const Json::Value& data) {
    if (!initialized_) {
        return;
    }

    std::vector<std::string> hook_ids;
    
    {
        std::lock_guard<std::mutex> lock(hooks_mutex_);
        
        auto type_it = hooks_by_type_.find(type);
        if (type_it == hooks_by_type_.end()) {
            return; // No hooks for this type
        }
        
        hook_ids = type_it->second;
    }

    // Process hooks outside of lock
    for (const std::string& hook_id : hook_ids) {
        std::unique_ptr<HookInfo> hook_copy;
        
        {
            std::lock_guard<std::mutex> lock(hooks_mutex_);
            auto it = hooks_.find(hook_id);
            if (it == hooks_.end() || !it->second->enabled) {
                continue;
            }
            
            hook_copy = std::make_unique<HookInfo>(*it->second);
        }
        
        if (hook_copy) {
            ProcessHookTrigger(*hook_copy, data);
            
            // Update statistics
            {
                std::lock_guard<std::mutex> lock(hooks_mutex_);
                auto it = hooks_.find(hook_id);
                if (it != hooks_.end()) {
                    it->second->trigger_count = hook_copy->trigger_count;
                    it->second->total_processing_time = hook_copy->total_processing_time;
                }
            }
        }
    }
}

void HooksManager::TriggerHook(const std::string& hook_id, const Json::Value& data) {
    if (!initialized_) {
        return;
    }

    std::unique_ptr<HookInfo> hook_copy;
    
    {
        std::lock_guard<std::mutex> lock(hooks_mutex_);
        auto it = hooks_.find(hook_id);
        if (it == hooks_.end() || !it->second->enabled) {
            return;
        }
        
        hook_copy = std::make_unique<HookInfo>(*it->second);
    }
    
    if (hook_copy) {
        ProcessHookTrigger(*hook_copy, data);
        
        // Update statistics
        {
            std::lock_guard<std::mutex> lock(hooks_mutex_);
            auto it = hooks_.find(hook_id);
            if (it != hooks_.end()) {
                it->second->trigger_count = hook_copy->trigger_count;
                it->second->total_processing_time = hook_copy->total_processing_time;
            }
        }
    }
}

std::vector<HooksManager::HookInfo> HooksManager::GetHooksForType(HookType type) const {
    std::lock_guard<std::mutex> lock(hooks_mutex_);
    
    std::vector<HookInfo> result;
    
    auto type_it = hooks_by_type_.find(type);
    if (type_it != hooks_by_type_.end()) {
        for (const std::string& hook_id : type_it->second) {
            auto it = hooks_.find(hook_id);
            if (it != hooks_.end()) {
                result.push_back(*it->second);
            }
        }
    }
    
    return result;
}

std::vector<HooksManager::HookInfo> HooksManager::GetAllHooks() const {
    std::lock_guard<std::mutex> lock(hooks_mutex_);
    
    std::vector<HookInfo> result;
    result.reserve(hooks_.size());
    
    for (const auto& pair : hooks_) {
        result.push_back(*pair.second);
    }
    
    return result;
}

HooksManager::HookInfo HooksManager::GetHookInfo(const std::string& hook_id) const {
    std::lock_guard<std::mutex> lock(hooks_mutex_);
    
    auto it = hooks_.find(hook_id);
    if (it != hooks_.end()) {
        return *it->second;
    }
    
    // Return empty hook info if not found
    return HookInfo("", HookType::CUSTOM, "");
}

size_t HooksManager::GetHookCount() const {
    std::lock_guard<std::mutex> lock(hooks_mutex_);
    return hooks_.size();
}

size_t HooksManager::GetHookCount(HookType type) const {
    std::lock_guard<std::mutex> lock(hooks_mutex_);
    
    auto type_it = hooks_by_type_.find(type);
    return type_it != hooks_by_type_.end() ? type_it->second.size() : 0;
}

// Private method implementations

std::string HooksManager::GenerateHookId() {
    static std::atomic<uint64_t> counter{1};
    return "hook_" + std::to_string(counter.fetch_add(1));
}

void HooksManager::ProcessHookTrigger(const HookInfo& hook, const Json::Value& data) {
    auto start_time = std::chrono::steady_clock::now();

    try {
        // Convert hook to event and dispatch
        if (event_manager_) {
            EventType event_type = HookTypeToEventType(hook.type);
            EventData event_data = CreateEvent(event_type, hook.description);
            event_data.payload = data;

            event_manager_->DispatchEvent(event_data, color_ostream::null());
        }

        auto end_time = std::chrono::steady_clock::now();
        auto processing_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        // Update statistics (modifying copy)
        const_cast<HookInfo&>(hook).trigger_count++;
        const_cast<HookInfo&>(hook).total_processing_time += processing_time;

    } catch (const std::exception& e) {
        logger_.Error("Exception processing hook '{}': {}", hook.id, e.what());
    }
}

void HooksManager::UpdateTriggerStatistics(HookInfo& hook, std::chrono::milliseconds processing_time) {
    hook.trigger_count++;
    hook.total_processing_time += processing_time;
}

void HooksManager::AddHookToTypeMap(const std::string& hook_id, HookType type) {
    hooks_by_type_[type].push_back(hook_id);
}

void HooksManager::RemoveHookFromTypeMap(const std::string& hook_id, HookType type) {
    auto type_it = hooks_by_type_.find(type);
    if (type_it != hooks_by_type_.end()) {
        auto& hook_list = type_it->second;
        hook_list.erase(
            std::remove(hook_list.begin(), hook_list.end(), hook_id),
            hook_list.end()
        );

        if (hook_list.empty()) {
            hooks_by_type_.erase(type_it);
        }
    }
}

// DFHack hook setup
bool HooksManager::SetupDFHackHooks() {
    logger_.Info("Setting up DFHack hooks...");

    // For now, just return true as placeholder
    // Actual DFHack hook setup would require proper DFHack integration
    return true;
}

void HooksManager::CleanupDFHackHooks() {
    logger_.Info("Cleaning up DFHack hooks...");

    // Cleanup all registered DFHack hooks
    dfhack_hooks_.clear();
}

bool HooksManager::IsDFHackHookActive(HookType type) const {
    return dfhack_hooks_.find(type) != dfhack_hooks_.end();
}

// Static hook callbacks (placeholders)
void HooksManager::OnUpdateHook(color_ostream& out) {
    if (instance_) {
        instance_->TriggerHook(HookType::ON_UPDATE);
    }
}

void HooksManager::OnStateChangeHook(color_ostream& out, int32_t state) {
    if (instance_) {
        Json::Value data;
        data["state"] = state;
        instance_->TriggerHook(HookType::ON_STATE_CHANGE, data);
    }
}

void HooksManager::OnUnitNewHook(color_ostream& out, int32_t unit_id) {
    if (instance_) {
        Json::Value data;
        data["unit_id"] = unit_id;
        instance_->TriggerHook(HookType::ON_UNIT_NEW, data);
    }
}

void HooksManager::OnUnitDeathHook(color_ostream& out, int32_t unit_id) {
    if (instance_) {
        Json::Value data;
        data["unit_id"] = unit_id;
        instance_->TriggerHook(HookType::ON_UNIT_DEATH, data);
    }
}

// Utility methods
std::string HooksManager::HookTypeToString(HookType type) {
    switch (type) {
        case HookType::ON_UPDATE: return "OnUpdate";
        case HookType::ON_STATE_CHANGE: return "OnStateChange";
        case HookType::ON_UNIT_NEW: return "OnUnitNew";
        case HookType::ON_UNIT_DEATH: return "OnUnitDeath";
        case HookType::ON_JOB_INITIATED: return "OnJobInitiated";
        case HookType::ON_JOB_COMPLETED: return "OnJobCompleted";
        case HookType::ON_BUILDING_CREATED: return "OnBuildingCreated";
        case HookType::ON_BUILDING_DESTROYED: return "OnBuildingDestroyed";
        case HookType::ON_ITEM_CREATED: return "OnItemCreated";
        case HookType::ON_ANNOUNCEMENT: return "OnAnnouncement";
        case HookType::ON_PAUSE: return "OnPause";
        case HookType::ON_UNPAUSE: return "OnUnpause";
        case HookType::CUSTOM: return "Custom";
        default: return "Unknown";
    }
}

HooksManager::HookType HooksManager::StringToHookType(const std::string& str) {
    if (str == "OnUpdate") return HookType::ON_UPDATE;
    if (str == "OnStateChange") return HookType::ON_STATE_CHANGE;
    if (str == "OnUnitNew") return HookType::ON_UNIT_NEW;
    if (str == "OnUnitDeath") return HookType::ON_UNIT_DEATH;
    if (str == "OnJobInitiated") return HookType::ON_JOB_INITIATED;
    if (str == "OnJobCompleted") return HookType::ON_JOB_COMPLETED;
    if (str == "OnBuildingCreated") return HookType::ON_BUILDING_CREATED;
    if (str == "OnBuildingDestroyed") return HookType::ON_BUILDING_DESTROYED;
    if (str == "OnItemCreated") return HookType::ON_ITEM_CREATED;
    if (str == "OnAnnouncement") return HookType::ON_ANNOUNCEMENT;
    if (str == "OnPause") return HookType::ON_PAUSE;
    if (str == "OnUnpause") return HookType::ON_UNPAUSE;
    return HookType::CUSTOM;
}

EventType HooksManager::HookTypeToEventType(HookType type) {
    switch (type) {
        case HookType::ON_UPDATE: return EventType::GAME_UPDATE;
        case HookType::ON_STATE_CHANGE: return EventType::GAME_STATE_CHANGE;
        case HookType::ON_UNIT_NEW: return EventType::UNIT_CREATED;
        case HookType::ON_UNIT_DEATH: return EventType::UNIT_DEATH;
        case HookType::ON_JOB_INITIATED: return EventType::JOB_STARTED;
        case HookType::ON_JOB_COMPLETED: return EventType::JOB_COMPLETED;
        case HookType::ON_BUILDING_CREATED: return EventType::BUILDING_CREATED;
        case HookType::ON_BUILDING_DESTROYED: return EventType::BUILDING_DESTROYED;
        case HookType::ON_ITEM_CREATED: return EventType::ITEM_CREATED;
        case HookType::ON_ANNOUNCEMENT: return EventType::ANNOUNCEMENT;
        case HookType::ON_PAUSE: return EventType::GAME_PAUSED;
        case HookType::ON_UNPAUSE: return EventType::GAME_UNPAUSED;
        default: return EventType::CUSTOM;
    }
}

bool HooksManager::ValidateHookType(HookType type) const {
    return type >= HookType::ON_UPDATE && type <= HookType::CUSTOM;
}

bool HooksManager::ValidateHookId(const std::string& hook_id) const {
    return !hook_id.empty();
}

void HooksManager::LogHookStatistics() const {
    logger_.Info("Hook Statistics:");
    logger_.Info("  Total hooks: {}", hooks_.size());

    for (const auto& pair : hooks_by_type_) {
        logger_.Info("  Hook type {}: {} hooks",
                    HookTypeToString(pair.first), pair.second.size());
    }
}

std::string HooksManager::GetStatusReport() const {
    std::lock_guard<std::mutex> lock(hooks_mutex_);

    std::ostringstream oss;
    oss << "HooksManager Status:\n";
    oss << "  Initialized: " << (initialized_ ? "Yes" : "No") << "\n";
    oss << "  Total hooks: " << hooks_.size() << "\n";
    oss << "  Hook types: " << hooks_by_type_.size() << "\n";
    oss << "  DFHack hooks: " << dfhack_hooks_.size() << "\n";

    for (const auto& pair : hooks_by_type_) {
        oss << "    " << HookTypeToString(pair.first) << ": " << pair.second.size() << " hooks\n";
    }

    return oss.str();
}

void HooksManager::DumpHookInfo(color_ostream& out) const {
    std::lock_guard<std::mutex> lock(hooks_mutex_);

    out << "Registered Hooks:\n";

    for (const auto& pair : hooks_) {
        const HookInfo& info = *pair.second;
        out << "  " << info.id << ":\n";
        out << "    Type: " << HookTypeToString(info.type) << "\n";
        out << "    Description: " << info.description << "\n";
        out << "    Enabled: " << (info.enabled ? "Yes" : "No") << "\n";
        out << "    Triggers: " << info.trigger_count << "\n";
        out << "    Total time: " << info.total_processing_time.count() << "ms\n";

        if (info.trigger_count > 0) {
            auto avg_time = info.total_processing_time.count() / info.trigger_count;
            out << "    Average time: " << avg_time << "ms\n";
        }
        out << "\n";
    }
}

// ScopedHook implementation

ScopedHook::ScopedHook(HooksManager& manager, HooksManager::HookType type,
                      const std::string& description)
    : manager_(&manager)
    , hook_id_(manager.RegisterHook(type, description))
{
}

ScopedHook::~ScopedHook() {
    if (manager_ && !hook_id_.empty()) {
        manager_->UnregisterHook(hook_id_);
    }
}

ScopedHook::ScopedHook(ScopedHook&& other) noexcept
    : manager_(other.manager_)
    , hook_id_(std::move(other.hook_id_))
{
    other.manager_ = nullptr;
    other.hook_id_.clear();
}

ScopedHook& ScopedHook::operator=(ScopedHook&& other) noexcept {
    if (this != &other) {
        // Unregister current hook
        if (manager_ && !hook_id_.empty()) {
            manager_->UnregisterHook(hook_id_);
        }

        // Move from other
        manager_ = other.manager_;
        hook_id_ = std::move(other.hook_id_);

        // Clear other
        other.manager_ = nullptr;
        other.hook_id_.clear();
    }
    return *this;
}

bool ScopedHook::Enable() {
    return manager_ && manager_->EnableHook(hook_id_);
}

bool ScopedHook::Disable() {
    return manager_ && manager_->DisableHook(hook_id_);
}

void ScopedHook::Release() {
    manager_ = nullptr;
    hook_id_.clear();
}

void ScopedHook::Unregister() {
    if (manager_ && !hook_id_.empty()) {
        manager_->UnregisterHook(hook_id_);
        hook_id_.clear();
    }
}

// HookContext implementation

HookContext::HookContext(HooksManager::HookType type, const Json::Value& data)
    : hook_type_(type)
    , data_(data)
    , trigger_time_(std::chrono::steady_clock::now())
{
}

std::chrono::milliseconds HookContext::GetElapsedTime() const {
    auto now = std::chrono::steady_clock::now();
    return std::chrono::duration_cast<std::chrono::milliseconds>(now - trigger_time_);
}

int32_t HookContext::GetIntData(const std::string& key, int32_t default_value) const {
    if (data_.isMember(key) && data_[key].isInt()) {
        return data_[key].asInt();
    }
    return default_value;
}

std::string HookContext::GetStringData(const std::string& key, const std::string& default_value) const {
    if (data_.isMember(key) && data_[key].isString()) {
        return data_[key].asString();
    }
    return default_value;
}

bool HookContext::GetBoolData(const std::string& key, bool default_value) const {
    if (data_.isMember(key) && data_[key].isBool()) {
        return data_[key].asBool();
    }
    return default_value;
}

} // namespace events
} // namespace dfai
