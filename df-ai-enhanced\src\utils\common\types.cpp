#include "types.h"

namespace dfai {
namespace utils {

// VoidResult implementation
VoidResult::VoidResult(bool success) : success_(success) {}

VoidResult::VoidResult(const std::string& error) : success_(false), error_message_(error) {}

bool VoidResult::is_success() const {
    return success_;
}

const std::string& VoidResult::get_error() const {
    return error_message_;
}

// Coordinate utilities
bool Coordinate::IsValid() const {
    return x >= 0 && y >= 0 && z >= 0;
}

bool Coordinate::operator==(const Coordinate& other) const {
    return x == other.x && y == other.y && z == other.z;
}

bool Coordinate::operator!=(const Coordinate& other) const {
    return !(*this == other);
}

bool Coordinate::operator<(const Coordinate& other) const {
    if (z != other.z) return z < other.z;
    if (y != other.y) return y < other.y;
    return x < other.x;
}

Coordinate Coordinate::operator+(const Coordinate& other) const {
    return {x + other.x, y + other.y, z + other.z};
}

Coordinate Coordinate::operator-(const Coordinate& other) const {
    return {x - other.x, y - other.y, z - other.z};
}

double Coordinate::DistanceTo(const Coordinate& other) const {
    int dx = x - other.x;
    int dy = y - other.y;
    int dz = z - other.z;
    return std::sqrt(dx*dx + dy*dy + dz*dz);
}

int Coordinate::ManhattanDistanceTo(const Coordinate& other) const {
    return std::abs(x - other.x) + std::abs(y - other.y) + std::abs(z - other.z);
}

std::string Coordinate::ToString() const {
    return "(" + std::to_string(x) + "," + std::to_string(y) + "," + std::to_string(z) + ")";
}

// Rectangle utilities
bool Rectangle::Contains(const Coordinate& point) const {
    return point.x >= min.x && point.x <= max.x &&
           point.y >= min.y && point.y <= max.y &&
           point.z >= min.z && point.z <= max.z;
}

bool Rectangle::IsValid() const {
    return min.x <= max.x && min.y <= max.y && min.z <= max.z;
}

int Rectangle::Width() const {
    return max.x - min.x + 1;
}

int Rectangle::Height() const {
    return max.y - min.y + 1;
}

int Rectangle::Depth() const {
    return max.z - min.z + 1;
}

int Rectangle::Volume() const {
    return Width() * Height() * Depth();
}

Coordinate Rectangle::Center() const {
    return {
        (min.x + max.x) / 2,
        (min.y + max.y) / 2,
        (min.z + max.z) / 2
    };
}

bool Rectangle::Intersects(const Rectangle& other) const {
    return !(max.x < other.min.x || min.x > other.max.x ||
             max.y < other.min.y || min.y > other.max.y ||
             max.z < other.min.z || min.z > other.max.z);
}

Rectangle Rectangle::Intersection(const Rectangle& other) const {
    if (!Intersects(other)) {
        return {{0, 0, 0}, {-1, -1, -1}}; // Invalid rectangle
    }
    
    return {
        {std::max(min.x, other.min.x), std::max(min.y, other.min.y), std::max(min.z, other.min.z)},
        {std::min(max.x, other.max.x), std::min(max.y, other.max.y), std::min(max.z, other.max.z)}
    };
}

Rectangle Rectangle::Union(const Rectangle& other) const {
    return {
        {std::min(min.x, other.min.x), std::min(min.y, other.min.y), std::min(min.z, other.min.z)},
        {std::max(max.x, other.max.x), std::max(max.y, other.max.y), std::max(max.z, other.max.z)}
    };
}

std::string Rectangle::ToString() const {
    return min.ToString() + " to " + max.ToString();
}

// Priority utilities
std::string PriorityToString(Priority priority) {
    switch (priority) {
        case PRIORITY_CRITICAL: return "Critical";
        case PRIORITY_HIGH: return "High";
        case PRIORITY_NORMAL: return "Normal";
        case PRIORITY_LOW: return "Low";
        case PRIORITY_BACKGROUND: return "Background";
        default: return "Unknown";
    }
}

Priority StringToPriority(const std::string& str) {
    if (str == "Critical" || str == "critical") return PRIORITY_CRITICAL;
    if (str == "High" || str == "high") return PRIORITY_HIGH;
    if (str == "Normal" || str == "normal") return PRIORITY_NORMAL;
    if (str == "Low" || str == "low") return PRIORITY_LOW;
    if (str == "Background" || str == "background") return PRIORITY_BACKGROUND;
    return PRIORITY_NORMAL; // Default
}

// TaskId utilities
TaskId GenerateTaskId() {
    static std::atomic<uint64_t> counter{1};
    return counter.fetch_add(1);
}

bool IsValidTaskId(TaskId id) {
    return id > 0;
}

// ResourceAmount utilities
std::string ResourceAmountToString(ResourceAmount amount) {
    return std::to_string(amount);
}

ResourceAmount StringToResourceAmount(const std::string& str) {
    try {
        return std::stoi(str);
    } catch (const std::exception&) {
        return 0;
    }
}

// Direction utilities
std::string DirectionToString(Direction dir) {
    switch (dir) {
        case DIRECTION_NORTH: return "North";
        case DIRECTION_SOUTH: return "South";
        case DIRECTION_EAST: return "East";
        case DIRECTION_WEST: return "West";
        case DIRECTION_UP: return "Up";
        case DIRECTION_DOWN: return "Down";
        case DIRECTION_NORTHEAST: return "Northeast";
        case DIRECTION_NORTHWEST: return "Northwest";
        case DIRECTION_SOUTHEAST: return "Southeast";
        case DIRECTION_SOUTHWEST: return "Southwest";
        default: return "Unknown";
    }
}

Direction StringToDirection(const std::string& str) {
    if (str == "North" || str == "north" || str == "N") return DIRECTION_NORTH;
    if (str == "South" || str == "south" || str == "S") return DIRECTION_SOUTH;
    if (str == "East" || str == "east" || str == "E") return DIRECTION_EAST;
    if (str == "West" || str == "west" || str == "W") return DIRECTION_WEST;
    if (str == "Up" || str == "up" || str == "U") return DIRECTION_UP;
    if (str == "Down" || str == "down" || str == "D") return DIRECTION_DOWN;
    if (str == "Northeast" || str == "northeast" || str == "NE") return DIRECTION_NORTHEAST;
    if (str == "Northwest" || str == "northwest" || str == "NW") return DIRECTION_NORTHWEST;
    if (str == "Southeast" || str == "southeast" || str == "SE") return DIRECTION_SOUTHEAST;
    if (str == "Southwest" || str == "southwest" || str == "SW") return DIRECTION_SOUTHWEST;
    return DIRECTION_NORTH; // Default
}

Coordinate DirectionToOffset(Direction dir) {
    switch (dir) {
        case DIRECTION_NORTH: return {0, -1, 0};
        case DIRECTION_SOUTH: return {0, 1, 0};
        case DIRECTION_EAST: return {1, 0, 0};
        case DIRECTION_WEST: return {-1, 0, 0};
        case DIRECTION_UP: return {0, 0, 1};
        case DIRECTION_DOWN: return {0, 0, -1};
        case DIRECTION_NORTHEAST: return {1, -1, 0};
        case DIRECTION_NORTHWEST: return {-1, -1, 0};
        case DIRECTION_SOUTHEAST: return {1, 1, 0};
        case DIRECTION_SOUTHWEST: return {-1, 1, 0};
        default: return {0, 0, 0};
    }
}

} // namespace utils
} // namespace dfai
