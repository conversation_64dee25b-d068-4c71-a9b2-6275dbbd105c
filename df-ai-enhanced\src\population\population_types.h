#pragma once

#include "../utils/common/types.h"
#include <cstdint>
#include <string>

namespace dfai {
namespace population {

/**
 * Pet-related flags and data
 */
union PetFlags {
    uint32_t whole;
    struct {
        uint32_t milkable      : 1;
        uint32_t shearable     : 1;
        uint32_t hunts_vermin  : 1;
        uint32_t trainable     : 1;
        uint32_t grazer        : 1;
        uint32_t lays_eggs     : 1;
        uint32_t reserved      : 26; // Reserved for future use
    } bits;
    
    PetFlags() : whole(0) {}
    PetFlags(uint32_t value) : whole(value) {}
};

/**
 * Squad order change tracking
 */
struct SquadOrderChange {
    enum OrderType {
        KILL_ORDER
    };
    
    OrderType type;
    int32_t squad_id;
    int32_t unit_id;
    bool remove_order;  // true to remove order, false to add
    std::string reason;
    
    SquadOrderChange(OrderType t, int32_t squad, int32_t unit, bool remove, const std::string& r)
        : type(t), squad_id(squad), unit_id(unit), remove_order(remove), reason(r) {}
};

/**
 * Military configuration and limits
 */
struct MilitaryConfig {
    int32_t min_percentage = 25;
    int32_t max_percentage = 75;
    int32_t squad_size = 10;
    bool auto_equipment = true;
    bool auto_training = true;
    
    bool is_valid() const {
        return min_percentage >= 0 && max_percentage <= 100 && 
               min_percentage <= max_percentage && squad_size > 0;
    }
};

/**
 * Noble position information
 */
struct NoblePosition {
    df::entity_position* position;
    df::unit* current_holder;
    std::set<df::entity_position_responsibility> responsibilities;
    bool requires_room;
    int32_t required_room_value;
    
    NoblePosition() : position(nullptr), current_holder(nullptr), 
                     requires_room(false), required_room_value(0) {}
};

/**
 * Healthcare information for units
 */
struct HealthInfo {
    df::unit* unit;
    bool needs_medical_attention;
    bool is_injured;
    bool is_sick;
    int32_t last_check_tick;
    
    HealthInfo(df::unit* u) : unit(u), needs_medical_attention(false), 
                             is_injured(false), is_sick(false), last_check_tick(0) {}
};

/**
 * Work assignment priorities
 */
enum class WorkPriority {
    CRITICAL = 0,  // Essential survival tasks
    HIGH = 1,      // Important but not critical
    NORMAL = 2,    // Standard work
    LOW = 3,       // Optional tasks
    DISABLED = 4   // Disabled tasks
};

/**
 * Population statistics for reporting
 */
struct PopulationStats {
    size_t total_citizens = 0;
    size_t active_military = 0;
    size_t nobles = 0;
    size_t children = 0;
    size_t adults = 0;
    size_t elderly = 0;
    size_t injured = 0;
    size_t sick = 0;
    size_t idle = 0;
    size_t working = 0;
    size_t total_pets = 0;
    size_t livestock = 0;
    size_t trained_animals = 0;
    size_t visitors = 0;
    size_t residents = 0;
    
    size_t total_population() const {
        return total_citizens + visitors + residents;
    }
    
    double military_ratio() const {
        return total_citizens > 0 ? static_cast<double>(active_military) / total_citizens : 0.0;
    }
};

} // namespace population
} // namespace df_ai
