#pragma once

#include "event_types.h"
#include "../debug/logging.h"
#include "../utils/common/common.h"
#include <functional>
#include <vector>
#include <map>
#include <mutex>
#include <memory>

namespace dfai {
namespace events {

/**
 * @brief Manages event callbacks and their execution
 * 
 * The CallbackManager handles registration, unregistration, and execution
 * of event callbacks in a thread-safe manner.
 */
class CallbackManager {
public:
    struct CallbackInfo {
        std::string id;
        EventType type;
        EventHandler handler;
        std::string description;
        int32_t priority;
        bool enabled;
        std::chrono::steady_clock::time_point registered_time;
        uint64_t execution_count;
        std::chrono::milliseconds total_execution_time;
        
        CallbackInfo(const std::string& callback_id, EventType event_type, 
                    EventHandler callback_handler, const std::string& desc, int32_t prio)
            : id(callback_id), type(event_type), handler(callback_handler)
            , description(desc), priority(prio), enabled(true)
            , registered_time(std::chrono::steady_clock::now())
            , execution_count(0), total_execution_time(0) {}
    };

    CallbackManager();
    ~CallbackManager();

    // Lifecycle management
    bool Initialize();
    void Shutdown();
    bool IsInitialized() const { return initialized_; }

    // Callback registration
    std::string RegisterCallback(EventType type, EventHandler handler, 
                                const std::string& description = "", 
                                int32_t priority = 100);
    bool UnregisterCallback(const std::string& callback_id);
    bool UnregisterCallbacksByType(EventType type);
    void UnregisterAllCallbacks();

    // Callback management
    bool EnableCallback(const std::string& callback_id);
    bool DisableCallback(const std::string& callback_id);
    bool SetCallbackPriority(const std::string& callback_id, int32_t priority);
    bool IsCallbackRegistered(const std::string& callback_id) const;
    bool IsCallbackEnabled(const std::string& callback_id) const;

    // Callback execution
    void ExecuteCallbacks(EventType type, const EventData& event_data, color_ostream& out);
    void ExecuteCallback(const std::string& callback_id, const EventData& event_data, color_ostream& out);

    // Information and statistics
    std::vector<CallbackInfo> GetCallbacksForType(EventType type) const;
    std::vector<CallbackInfo> GetAllCallbacks() const;
    CallbackInfo GetCallbackInfo(const std::string& callback_id) const;
    size_t GetCallbackCount() const;
    size_t GetCallbackCount(EventType type) const;

    // Performance monitoring
    std::chrono::milliseconds GetAverageExecutionTime(const std::string& callback_id) const;
    std::chrono::milliseconds GetTotalExecutionTime(EventType type) const;
    uint64_t GetTotalExecutionCount(EventType type) const;

    // Debugging and diagnostics
    void LogCallbackStatistics() const;
    std::string GetStatusReport() const;
    void DumpCallbackInfo(color_ostream& out) const;

private:
    mutable std::mutex callbacks_mutex_;
    std::map<std::string, std::unique_ptr<CallbackInfo>> callbacks_;
    std::map<EventType, std::vector<std::string>> callbacks_by_type_;
    
    bool initialized_;
    debug::Logger& logger_;
    
    // Internal utilities
    std::string GenerateCallbackId();
    void SortCallbacksByPriority(std::vector<std::string>& callback_ids) const;
    bool ExecuteSingleCallback(const CallbackInfo& callback, const EventData& event_data, color_ostream& out);
    void UpdateExecutionStatistics(CallbackInfo& callback, std::chrono::milliseconds execution_time);
    
    // Thread safety helpers
    std::unique_lock<std::mutex> AcquireLock() const;
    void AddCallbackToTypeMap(const std::string& callback_id, EventType type);
    void RemoveCallbackFromTypeMap(const std::string& callback_id, EventType type);
    
    // Validation
    bool ValidateCallback(EventType type, const EventHandler& handler) const;
    bool ValidateCallbackId(const std::string& callback_id) const;
};

/**
 * @brief RAII wrapper for callback registration
 * 
 * Automatically unregisters the callback when the object is destroyed.
 */
class ScopedCallback {
public:
    ScopedCallback(CallbackManager& manager, EventType type, EventHandler handler,
                  const std::string& description = "", int32_t priority = 100);
    ~ScopedCallback();
    
    // Non-copyable, movable
    ScopedCallback(const ScopedCallback&) = delete;
    ScopedCallback& operator=(const ScopedCallback&) = delete;
    ScopedCallback(ScopedCallback&& other) noexcept;
    ScopedCallback& operator=(ScopedCallback&& other) noexcept;
    
    // Callback management
    bool IsValid() const { return !callback_id_.empty(); }
    const std::string& GetCallbackId() const { return callback_id_; }
    bool Enable();
    bool Disable();
    bool SetPriority(int32_t priority);
    
    // Manual control
    void Release(); // Releases ownership without unregistering
    void Unregister(); // Manually unregister

private:
    CallbackManager* manager_;
    std::string callback_id_;
};

/**
 * @brief Callback execution context
 * 
 * Provides additional context and utilities during callback execution.
 */
class CallbackContext {
public:
    CallbackContext(const EventData& event_data, color_ostream& output);
    
    // Event data access
    const EventData& GetEventData() const { return event_data_; }
    EventType GetEventType() const { return event_data_.type; }
    const std::string& GetEventName() const { return event_data_.name; }
    const Json::Value& GetEventPayload() const { return event_data_.payload; }
    
    // Output utilities
    color_ostream& GetOutput() { return output_; }
    void Log(const std::string& message);
    void LogError(const std::string& message);
    void LogWarning(const std::string& message);
    void LogInfo(const std::string& message);
    
    // Execution control
    void RequestStop() { stop_requested_ = true; }
    bool IsStopRequested() const { return stop_requested_; }
    
    // Timing information
    std::chrono::steady_clock::time_point GetStartTime() const { return start_time_; }
    std::chrono::milliseconds GetElapsedTime() const;

private:
    const EventData& event_data_;
    color_ostream& output_;
    std::chrono::steady_clock::time_point start_time_;
    bool stop_requested_;
};

} // namespace events
} // namespace dfai
