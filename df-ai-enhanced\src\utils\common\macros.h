#pragma once

#include "constants.h"

namespace df_ai {
namespace utils {

/**
 * Utility macros used throughout the DF-AI system
 */

// Assertion macros
#ifdef DF_AI_DEBUG
    #define DF_AI_ASSERT(condition, message) \
        do { \
            if (!(condition)) { \
                std::cerr << "Assertion failed: " << #condition << " - " << message \
                         << " (" << __FILE__ << ":" << __LINE__ << ")" << std::endl; \
                std::abort(); \
            } \
        } while (0)
#else
    #define DF_AI_ASSERT(condition, message) ((void)0)
#endif

// Logging macros
#define DF_AI_LOG_ERROR(message) \
    std::cerr << "[ERROR] " << message << " (" << __FILE__ << ":" << __LINE__ << ")" << std::endl

#define DF_AI_LOG_WARNING(message) \
    std::cout << "[WARNING] " << message << std::endl

#define DF_AI_LOG_INFO(message) \
    std::cout << "[INFO] " << message << std::endl

#ifdef DF_AI_DEBUG
    #define DF_AI_LOG_DEBUG(message) \
        std::cout << "[DEBUG] " << message << " (" << __FILE__ << ":" << __LINE__ << ")" << std::endl
#else
    #define DF_AI_LOG_DEBUG(message) ((void)0)
#endif

// Coordinate validation macros
#define DF_AI_VALIDATE_COORD(coord) \
    DF_AI_ASSERT((coord).x >= 0 && (coord).x < MAX_MAP_X && \
                 (coord).y >= 0 && (coord).y < MAX_MAP_Y && \
                 (coord).z >= 0 && (coord).z < MAX_MAP_Z, \
                 "Invalid coordinate: " << (coord).x << "," << (coord).y << "," << (coord).z)

// Resource validation macros
#define DF_AI_VALIDATE_RESOURCE_AMOUNT(amount) \
    DF_AI_ASSERT((amount) >= 0, "Resource amount cannot be negative: " << (amount))

// Null pointer check macros
#define DF_AI_CHECK_NULL(ptr, message) \
    do { \
        if ((ptr) == nullptr) { \
            DF_AI_LOG_ERROR("Null pointer check failed: " << message); \
            return; \
        } \
    } while (0)

#define DF_AI_CHECK_NULL_RETURN(ptr, return_value, message) \
    do { \
        if ((ptr) == nullptr) { \
            DF_AI_LOG_ERROR("Null pointer check failed: " << message); \
            return (return_value); \
        } \
    } while (0)

// Range validation macros
#define DF_AI_VALIDATE_RANGE(value, min_val, max_val) \
    DF_AI_ASSERT((value) >= (min_val) && (value) <= (max_val), \
                 "Value out of range: " << (value) << " not in [" << (min_val) << ", " << (max_val) << "]")

// Container validation macros
#define DF_AI_VALIDATE_CONTAINER_INDEX(container, index) \
    DF_AI_ASSERT((index) >= 0 && (index) < (container).size(), \
                 "Container index out of bounds: " << (index) << " >= " << (container).size())

// Enum validation macro
#define DF_AI_VALIDATE_ENUM(value, enum_type, max_value) \
    DF_AI_ASSERT(static_cast<int>(value) >= 0 && static_cast<int>(value) < static_cast<int>(max_value), \
                 "Invalid enum value: " << static_cast<int>(value))

// Try-catch wrapper for safer operations
#define DF_AI_TRY_OPERATION(operation, error_message) \
    try { \
        operation; \
    } catch (const std::exception& e) { \
        DF_AI_LOG_ERROR(error_message << ": " << e.what()); \
    } catch (...) { \
        DF_AI_LOG_ERROR(error_message << ": Unknown exception"); \
    }

// Performance measurement macros
#ifdef DF_AI_PROFILE
    #define DF_AI_PROFILE_SCOPE(name) \
        auto start_##name = std::chrono::high_resolution_clock::now(); \
        auto end_##name = std::chrono::high_resolution_clock::now(); \
        auto duration_##name = std::chrono::duration_cast<std::chrono::microseconds>(end_##name - start_##name); \
        DF_AI_LOG_DEBUG("Profile " << #name << ": " << duration_##name.count() << " microseconds")
#else
    #define DF_AI_PROFILE_SCOPE(name) ((void)0)
#endif

// Utility macros for common patterns
#define DF_AI_SAFE_DELETE(ptr) \
    do { \
        delete (ptr); \
        (ptr) = nullptr; \
    } while (0)

#define DF_AI_ARRAY_SIZE(array) \
    (sizeof(array) / sizeof((array)[0]))

// Disable copy constructor and assignment operator
#define DF_AI_DISABLE_COPY(ClassName) \
    ClassName(const ClassName&) = delete; \
    ClassName& operator=(const ClassName&) = delete;

// Disable move constructor and assignment operator
#define DF_AI_DISABLE_MOVE(ClassName) \
    ClassName(ClassName&&) = delete; \
    ClassName& operator=(ClassName&&) = delete;

// Disable both copy and move
#define DF_AI_DISABLE_COPY_AND_MOVE(ClassName) \
    DF_AI_DISABLE_COPY(ClassName) \
    DF_AI_DISABLE_MOVE(ClassName)

} // namespace utils
} // namespace df_ai
