{"$schema": "https://ben.lubar.me/df-ai-schemas/room-template.json", "f": [{"x": 5, "y": 1, "z": 0, "construction": "UpDownStair", "dig": "UpDownStair"}, {"x": 5, "y": 1, "z": 0, "construction": "UpDownStair", "dig": "UpDownStair"}, {"x": 5, "y": 1, "z": 0, "construction": "UpDownStair", "dig": "UpDownStair"}, {"x": 5, "y": 1, "z": 0, "construction": "UpDownStair", "dig": "UpDownStair"}, {"x": 5, "y": 1, "z": 0, "construction": "UpDownStair", "dig": "UpDownStair"}, {"x": 5, "y": 1, "z": 0, "construction": "UpDownStair", "dig": "UpDownStair", "stairs_special": true}, {"type": "cage_trap", "x": 0, "y": 0, "z": 0}, {"type": "cage_trap", "x": 1, "y": 0, "z": 0}, {"type": "cage_trap", "x": 2, "y": 0, "z": 0}, {"type": "cage_trap", "x": 0, "y": 1, "z": 0}, {"type": "cage_trap", "x": 1, "y": 1, "z": 0}, {"type": "cage_trap", "x": 2, "y": 1, "z": 0}, {"type": "cage_trap", "x": 0, "y": 0, "z": 0}, {"type": "cage_trap", "x": 1, "y": 0, "z": 0}, {"type": "cage_trap", "x": 0, "y": 1, "z": 0}, {"type": "cage_trap", "x": 1, "y": 1, "z": 0}, {"type": "cage_trap", "x": 0, "y": 2, "z": 0}, {"type": "cage_trap", "x": 1, "y": 2, "z": 0}, {"type": "cage_trap", "x": 0, "y": 0, "z": 0}, {"type": "cage_trap", "x": 1, "y": 0, "z": 0}, {"type": "cage_trap", "x": 2, "y": 0, "z": 0}, {"type": "cage_trap", "x": 0, "y": 1, "z": 0}, {"type": "cage_trap", "x": 1, "y": 1, "z": 0}, {"type": "cage_trap", "x": 2, "y": 1, "z": 0}, {"type": "cage_trap", "x": 0, "y": 0, "z": 0}, {"type": "cage_trap", "x": 1, "y": 0, "z": 0}, {"type": "cage_trap", "x": 0, "y": 1, "z": 0}, {"type": "cage_trap", "x": 1, "y": 1, "z": 0}, {"type": "cage_trap", "x": 0, "y": 2, "z": 0}, {"type": "cage_trap", "x": 1, "y": 2, "z": 0}, {"x": 4, "y": 0, "z": 0, "construction": "UpDownStair", "dig": "UpDownStair"}, {"x": 4, "y": 0, "z": 0, "construction": "UpDownStair", "dig": "UpDownStair", "stairs_special": true}], "r": [{"type": "outpost", "outpost_type": "mining", "min": [-3, -3, 0], "max": [3, 3, 0], "build_when_accessible": true, "layout": [0]}, {"type": "outpost", "outpost_type": "mining", "min": [-3, -3, -1], "max": [3, 3, -1], "build_when_accessible": true, "accesspath": [0], "layout": [1]}, {"type": "outpost", "outpost_type": "mining", "min": [-3, -3, -2], "max": [3, 3, -2], "build_when_accessible": true, "accesspath": [1], "layout": [2]}, {"type": "outpost", "outpost_type": "mining", "min": [-3, -3, -3], "max": [3, 3, -3], "build_when_accessible": true, "accesspath": [2], "layout": [3]}, {"type": "outpost", "outpost_type": "mining", "min": [-3, -3, -4], "max": [3, 3, -4], "build_when_accessible": true, "accesspath": [3], "layout": [4]}, {"type": "outpost", "outpost_type": "mining", "min": [-3, -3, -5], "max": [3, 3, -5], "build_when_accessible": true, "accesspath": [4], "layout": [5], "exits": [["generic01_mineshaft_segment", 3, 3, -1]]}, {"type": "corridor", "corridor_type": "veinshaft", "min": [-1, 4, -5], "max": [1, 5, -5], "accesspath": [5], "layout": [6, 7, 8, 9, 10, 11]}, {"type": "corridor", "corridor_type": "veinshaft", "min": [-5, -1, -5], "max": [-4, 1, -5], "accesspath": [5], "layout": [12, 13, 14, 15, 16, 17]}, {"type": "corridor", "corridor_type": "veinshaft", "min": [-1, -5, -2], "max": [1, -4, -2], "accesspath": [2], "layout": [18, 19, 20, 21, 22, 23]}, {"type": "corridor", "corridor_type": "veinshaft", "min": [4, -1, -2], "max": [5, 1, -2], "accesspath": [2], "layout": [24, 25, 26, 27, 28, 29]}, {"type": "stockpile", "stockpile_type": "stone", "level": 5, "min": [-2, -2, -2], "max": [2, 2, -2], "in_corridor": true, "build_when_accessible": true, "accesspath": [2], "layout": [30]}, {"type": "stockpile", "stockpile_type": "stone", "level": 5, "min": [-2, -2, -5], "max": [2, 2, -5], "in_corridor": true, "build_when_accessible": true, "accesspath": [5], "layout": [31]}]}