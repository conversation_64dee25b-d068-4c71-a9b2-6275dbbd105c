{"$schema": "https://ben.lubar.me/df-ai-schemas/room-template.json", "f": [{"dig": "Channel", "construction": "NONE", "x": 1, "y": 1, "z": 0}, {"type": "door", "x": -1, "y": 1, "z": 0, "ignore": true}, {"type": "hatch", "dig": "Channel", "construction": "NONE", "x": 0, "y": 0, "z": 0}, {"type": "door", "x": -1, "y": 1, "z": 0, "ignore": true}], "r": [{"type": "farmplot", "min": [1, -1, 0], "max": [3, 1, 0], "layout": [3], "accesspath": [1, 2], "has_users": 13, "placeholder": 0, "exits": [["generic01_underground_farm_extension", 3, 1, 0], ["generic01_underground_farm_extension", 1, -1, 0], ["generic01_underground_farm_extension", 1, 3, 0]]}, {"type": "stockpile", "stockpile_type": "food", "stock_disable": ["FoodMeat", "FoodFish", "FoodUnpreparedFish", "FoodEgg", "FoodPlants", "FoodDrinkPlant", "FoodDrinkAnimal", "FoodCheesePlant", "FoodCheeseAnimal", "FoodLeaves", "FoodMilledPlant", "FoodBoneMeal", "FoodFat", "FoodPaste", "FoodPressedMaterial", "FoodExtractPlant", "FoodExtractAnimal", "FoodMiscLiquid"], "layout": [0, 1], "stock_specific1": true, "min": [1, -1, 1], "max": [3, 1, 1], "optional_walls": [[3, 1, 0], [1, -1, 0], [1, 3, 0]], "workshop": 0, "comment": "seeds"}, {"type": "pond", "min": [2, 0, 1], "max": [2, 0, 1], "temporary": true, "workshop": 0, "layout": [2], "accesspath": [1], "require_walls": false, "comment": "irrigation"}]}