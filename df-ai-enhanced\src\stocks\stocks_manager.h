#pragma once

#include "../utils/common/common.h"
#include "../debug/logging.h"
#include "../config/config_manager.h"

#include <memory>
#include <vector>
#include <map>
#include <set>
#include <string>

// Forward declarations
class color_ostream;
namespace df {
    struct item;
    struct building;
    struct manager_order;
    struct stockpile;
}

namespace dfai {
namespace stocks {

// Forward declarations for specialized managers
class FarmManager;
class ForgeManager;
class TradeManager;
class EquipmentManager;
class FoodManager;
class ItemManager;

/**
 * Main stocks management system
 * Coordinates all aspects of fortress inventory, production, and trade
 */
class StocksManager {
public:
    StocksManager();
    ~StocksManager();
    
    // Core lifecycle
    bool Initialize(color_ostream& out);
    void Shutdown();
    void Update(color_ostream& out);
    
    // Status reporting
    std::string GetStatusReport() const;
    void GenerateReport(std::ostream& out, bool html = false) const;
    
    // Item management
    void UpdateItems(color_ostream& out);
    void ScanStockpiles(color_ostream& out);
    void ManageItemPriorities(color_ostream& out);
    
    // Production management
    void UpdateProduction(color_ostream& out);
    void QueueProductionOrders(color_ostream& out);
    void ManageManagerOrders(color_ostream& out);
    
    // Equipment management
    void UpdateEquipment(color_ostream& out);
    void AssignEquipment(color_ostream& out);
    void CheckEquipmentNeeds(color_ostream& out);
    
    // Food and farming
    void UpdateFarming(color_ostream& out);
    void ManageFoodProduction(color_ostream& out);
    void CheckFoodStocks(color_ostream& out);
    
    // Trade management
    void UpdateTrade(color_ostream& out);
    void PrepareTradeGoods(color_ostream& out);
    void ManageTradeDeals(color_ostream& out);
    
    // Forge and manufacturing
    void UpdateForge(color_ostream& out);
    void ManageMetalworking(color_ostream& out);
    void CheckRawMaterials(color_ostream& out);
    
    // Queries
    int32_t GetItemCount(const std::string& item_type) const;
    bool HasSufficientFood() const;
    bool HasSufficientWeapons() const;
    bool HasSufficientArmor() const;
    
    // Specialized manager access
    FarmManager* GetFarmManager() { return farm_manager_.get(); }
    ForgeManager* GetForgeManager() { return forge_manager_.get(); }
    TradeManager* GetTradeManager() { return trade_manager_.get(); }
    EquipmentManager* GetEquipmentManager() { return equipment_manager_.get(); }

private:
    // Core components
    debug::Logger& logger_;
    config::ConfigManager& config_;
    bool initialized_;
    size_t update_counter_;
    
    // Specialized managers
    std::unique_ptr<FarmManager> farm_manager_;
    std::unique_ptr<ForgeManager> forge_manager_;
    std::unique_ptr<TradeManager> trade_manager_;
    std::unique_ptr<EquipmentManager> equipment_manager_;
    std::unique_ptr<FoodManager> food_manager_;
    std::unique_ptr<ItemManager> item_manager_;
    
    // Item tracking
    std::map<std::string, int32_t> item_counts_;
    std::set<int32_t> monitored_items_;
    std::vector<df::stockpile*> stockpiles_;
    
    // Production tracking
    std::map<std::string, int32_t> production_targets_;
    std::vector<df::manager_order*> pending_orders_;
    
    // Initialization helpers
    bool InitializeSubManagers(color_ostream& out);
    
    // Update helpers
    void UpdateItemCounts();
    void UpdateProductionStatus();
    void ProcessPendingOrders(color_ostream& out);
    
    // Analysis helpers
    void AnalyzeStockpileContents();
    void AnalyzeProductionNeeds();
    void AnalyzeTradeOpportunities();
};

} // namespace stocks
} // namespace dfai
