#include "ai.h"
#include "population.h"
#include "plan.h"

#include "modules/Buildings.h"
#include "modules/Gui.h"
#include "modules/Units.h"

#include "df/building_civzonest.h"
#include "df/building_nest_boxst.h"
#include "df/caste_raw.h"
#include "df/creature_raw.h"
#include "df/item_eggst.h"
#include "df/manager_order.h"
#include "df/manager_order_template.h"
#include "df/training_assignment.h"
#include "df/ui.h"
#include "df/ui_sidebar_menus.h"
#include "df/unit_misc_trait.h"
#include "df/unit_relationship_type.h"
#include "df/unit_wound.h"
#include "df/viewscreen.h"
#include "df/world.h"

REQUIRE_GLOBAL(ui);
REQUIRE_GLOBAL(ui_building_assign_units);
REQUIRE_GLOBAL(ui_building_item_cursor);
REQUIRE_GLOBAL(ui_sidebar_menus);
REQUIRE_GLOBAL(world);

void Population::update_pets(color_ostream & out)
{
    if (!ai.plan.pastures_ready(out))
    {
        // will check next time
        return;
    }

    int32_t needmilk = 0;
    int32_t needshear = 0;
    for (auto mo : world->manager_orders)
    {
        if (mo->job_type == job_type::MilkCreature)
        {
            needmilk -= mo->amount_left;
        }
        else if (mo->job_type == job_type::ShearCreature)
        {
            needshear -= mo->amount_left;
        }
    }

    std::map<df::caste_raw *, std::set<std::pair<int32_t, df::unit *>>> forSlaughter;

    std::map<int32_t, pet_flags> np = pet;
    for (auto it : pet_check)
    {
        np[it]; // make sure existing pasture assignments are checked
    }
    pet_check.clear();
    for (auto u : world->units.active)
    {
        df::creature_raw *race = df::creature_raw::find(u->race);
        df::caste_raw *cst = race->caste[u->caste];

        if (cst->flags.is_set(caste_raw_flags::CAN_LEARN))
        {
            continue;
        }

        if (u->flags1.bits.inactive || u->flags1.bits.merchant || u->flags1.bits.forest || u->flags2.bits.visitor || u->flags2.bits.slaughter)
        {
            continue;
        }

        if (u->flags1.bits.caged && u->civ_id == -1 && u->cultural_identity == -1 && u->training_level == animal_training_level::WildUntamed)
        {
            // train any caught wild animals
            if (!df::training_assignment::find(u->id))
            {
                auto asn = df::allocate<df::training_assignment>();
                asn->animal_id = u->id;
                asn->trainer_id = -1;
                asn->flags.whole = 0;
                asn->flags.bits.any_trainer = true;
                insert_into_vector(ui->equipment.training_assignments, &df::training_assignment::animal_id, asn);
            }

            continue;
        }

        if (!Units::isOwnCiv(u) || Units::isOwnGroup(u) || Units::isOwnRace(u) || u->cultural_identity != -1)
        {
            continue;
        }

        int32_t age = days_since(u->birth_year, u->birth_time);

        if (u->training_level > animal_training_level::SemiWild && u->training_level < animal_training_level::Domesticated)
        {
            u->flags2.bits.slaughter = 1;
            ai.debug(out, stl_sprintf("marked %dy%dm%dd old %s:%s for slaughter (trained wild animal)", age / 12 / 28, (age / 28) % 12, age % 28, race->creature_id.c_str(), cst->caste_id.c_str()));
            continue;
        }

        if (pet.count(u->id))
        {
            if (cst->body_size_2.back() <= age && // full grown
                u->profession != profession::TRAINED_HUNTER && // not trained
                u->profession != profession::TRAINED_WAR && // not trained
                u->relationship_ids[unit_relationship_type::Pet] == -1) // not owned
            {
                if (std::find_if(u->body.wounds.begin(), u->body.wounds.end(), [](df::unit_wound *w) -> bool { return std::find_if(w->parts.begin(), w->parts.end(), [](df::unit_wound::T_parts *p) -> bool { return p->flags2.bits.gelded; }) != w->parts.end(); }) != u->body.wounds.end() || cst->sex == pronoun_type::it)
                {
                    // animal can't reproduce, can't work, and will provide maximum butchering reward. kill it.
                    u->flags2.bits.slaughter = true;
                    ai.debug(out, stl_sprintf("marked %dy%dm%dd old %s:%s for slaughter (can't reproduce)", age / 12 / 28, (age / 28) % 12, age % 28, race->creature_id.c_str(), cst->caste_id.c_str()));
                    continue;
                }

                forSlaughter[cst].insert(std::make_pair(age, u));
            }

            if (pet.at(u->id).bits.milkable && !Units::isBaby(u) && !Units::isChild(u))
            {
                bool have = false;
                for (auto mt : u->status.misc_traits)
                {
                    if (mt->id == misc_trait_type::MilkCounter)
                    {
                        have = true;
                        break;
                    }
                }
                if (!have)
                {
                    needmilk++;
                }
            }

            if (pet.at(u->id).bits.shearable && !Units::isBaby(u) && !Units::isChild(u))
            {
                bool found = false;
                for (auto stl : cst->shearable_tissue_layer)
                {
                    for (auto bpi : stl->bp_modifiers_idx)
                    {
                        if (u->appearance.bp_modifiers[bpi] >= stl->length)
                        {
                            needshear++;
                            found = true;
                            break;
                        }
                    }
                    if (found)
                        break;
                }
            }

            np.erase(u->id);
            continue;
        }

        pet_flags flags;
        flags.bits.milkable = 0;
        flags.bits.shearable = 0;
        flags.bits.hunts_vermin = 0;
        flags.bits.trainable = 0;
        flags.bits.grazer = 0;
        flags.bits.lays_eggs = 0;

        if (cst->flags.is_set(caste_raw_flags::MILKABLE))
        {
            flags.bits.milkable = 1;
        }

        if (!cst->shearable_tissue_layer.empty())
        {
            flags.bits.shearable = 1;
        }

        if (cst->flags.is_set(caste_raw_flags::HUNTS_VERMIN))
        {
            flags.bits.hunts_vermin = 1;
        }

        if (cst->flags.is_set(caste_raw_flags::TRAINABLE_HUNTING) || cst->flags.is_set(caste_raw_flags::TRAINABLE_WAR))
        {
            flags.bits.trainable = 1;
        }

        if (cst->flags.is_set(caste_raw_flags::GRAZER))
        {
            flags.bits.grazer = 1;            if (auto bld = virtual_cast<df::building_civzonest>(ai.plan.getpasture(out, u->id)))
            {
                assign_unit_to_zone(u, bld);
                // Monitor grass levels in pasture
                MonitorPastureGrassLevels(out, bld, u);
            }
            else if (u->relationship_ids[df::unit_relationship_type::Pet] == -1 && !cst->flags.is_set(caste_raw_flags::CAN_LEARN))
            {
                // Evaluate slaughter candidates and keep the best
                if (ShouldSlaughterForPasture(out, u)) {
                    u->flags2.bits.slaughter = 1;
                    ai.debug(out, stl_sprintf("marked %dy%dm%dd old %s:%s for slaughter (no pasture)", age / 12 / 28, (age / 28) % 12, age % 28, race->creature_id.c_str(), cst->caste_id.c_str()));
                }
            }
        }

        if (cst->flags.is_set(caste_raw_flags::LAYS_EGGS))
        {
            flags.bits.lays_eggs = 1;
        }

        pet[u->id] = flags;
    }

    for (auto p : np)
    {
        ai.plan.freepasture(out, p.first);
        pet.erase(p.first);
    }

    for (auto & cst : forSlaughter)
    {
        // we have reproductively viable animals, but there are more than 3 of
        // this sex (full-grown). kill the oldest ones for meat/leather/bones.

        if (cst.second.size() > 3)
        {
            // remove the youngest 3
            auto it = cst.second.begin();
            std::advance(it, 3);
            cst.second.erase(cst.second.begin(), it);

            for (auto it : cst.second)
            {
                int32_t age = it.first;
                df::unit *u = it.second;
                df::creature_raw *race = df::creature_raw::find(u->race);
                u->flags2.bits.slaughter = 1;
                ai.debug(out, stl_sprintf("marked %dy%dm%dd old %s:%s for slaughter (too many adults)", age / 12 / 28, (age / 28) % 12, age % 28, race->creature_id.c_str(), cst.first->caste_id.c_str()));
            }
        }
    }

    if (needmilk > 0)
    {
        df::manager_order_template tmpl;
        tmpl.job_type = job_type::MilkCreature;
        tmpl.mat_index = -1;

        ai.stocks.add_manager_order(out, tmpl, std::min(needmilk, 30));
    }

    if (needshear > 0)
    {
        df::manager_order_template tmpl;
        tmpl.job_type = job_type::ShearCreature;
        tmpl.mat_index = -1;

        ai.stocks.add_manager_order(out, tmpl, std::min(needshear, 30));
    }

    for (auto bld : world->buildings.other[buildings_other_id::NEST_BOX])
    {
        auto box = virtual_cast<df::building_nest_boxst>(bld);
        if (!box || box->getBuildStage() != box->getMaxBuildStage())
        {
            continue;
        }

        if (box->claimed_by == -1)
        {
            continue;
        }

        for (auto item : box->contained_items)
        {
            if (auto egg = virtual_cast<df::item_eggst>(item->item))
            {
                if (egg->egg_flags.bits.fertile)
                {
                    // baby chicks are preferable over cooked eggs.
                    egg->flags.bits.forbid = true;
                }
            }
        }
    }
}

void Population::assign_unit_to_zone(df::unit *u, df::building_civzonest *bld)
{
    // Use enhanced callback system instead of direct UI manipulation
    if (auto ref = Units::getGeneralRef(u, general_ref_type::BUILDING_CIVZONE_ASSIGNED))
    {
        if (ref->getBuilding() == bld)
        {
            // already assigned to the correct zone
            return;
        }
    }

    // Create and queue an exclusive callback for zone assignment
    // This replaces the direct UI manipulation with a proper callback pattern
    try {
        // Direct assignment approach - safer than UI manipulation
        auto new_ref = new df::general_ref_building_civzone_assignedst();
        new_ref->building_id = bld->id;
        
        u->general_refs.push_back(new_ref);
        bld->assigned_units.push_back(u->id);
        
        // Verify assignment worked
        bool assignment_successful = false;
        if (auto ref = Units::getGeneralRef(u, general_ref_type::BUILDING_CIVZONE_ASSIGNED)) {
            assignment_successful = (ref->getBuilding() == bld);
        }
        
        if (!assignment_successful) {
            // Clean up failed assignment
            u->general_refs.pop_back();
            bld->assigned_units.pop_back();
            delete new_ref;
            ai.debug(ai.out, stl_sprintf("Failed to assign unit %d to zone %d", u->id, bld->id));
        } else {
            ai.debug(ai.out, stl_sprintf("Successfully assigned unit %d to zone %d", u->id, bld->id));
        }
    } catch (const std::exception& e) {
        ai.debug(ai.out, stl_sprintf("Exception during zone assignment: %s", e.what()));
    }
}

// Implementation of missing helper functions for enhanced pet management
void Population::MonitorPastureGrassLevels(color_ostream& out, df::building_civzonest* pasture, df::unit* unit) {
    if (!pasture || !unit) return;
    
    // Check grass levels in the pasture area
    int32_t total_tiles = 0;
    int32_t grass_tiles = 0;
    
    for (int16_t x = pasture->x1; x <= pasture->x2; x++) {
        for (int16_t y = pasture->y1; y <= pasture->y2; y++) {
            total_tiles++;
            if (auto tt = Maps::getTileType(df::coord(x, y, pasture->z))) {
                auto tm = ENUM_ATTR(tiletype, material, *tt);
                if (tm == tiletype_material::GRASS_LIGHT || tm == tiletype_material::GRASS_DARK) {
                    grass_tiles++;
                }
            }
        }
    }
    
    // If grass is below 20% of total area, consider it low grass
    if (grass_tiles < total_tiles / 5) {
        ai.debug(out, stl_sprintf("Pasture %d has low grass: %d/%d tiles (%d%%)", 
                                 pasture->id, grass_tiles, total_tiles, 
                                 total_tiles > 0 ? (grass_tiles * 100 / total_tiles) : 0));
        
        // Mark for potential reassignment if grass is critically low
        if (grass_tiles < total_tiles / 10) {
            ai.debug(out, stl_sprintf("Unit %d may need pasture reassignment due to critically low grass", unit->id));
        }
    }
}

bool Population::ShouldSlaughterForPasture(color_ostream& out, df::unit* unit) {
    if (!unit) return false;
    
    df::creature_raw* race = df::creature_raw::find(unit->race);
    df::caste_raw* cst = race->caste[unit->caste];
    
    // Don't slaughter if unit can learn (citizens)
    if (cst->flags.is_set(caste_raw_flags::CAN_LEARN)) {
        return false;
    }
    
    // Don't slaughter if unit is a pet
    if (unit->relationship_ids[df::unit_relationship_type::Pet] != -1) {
        return false;
    }
    
    // Don't slaughter if unit is trained for war or hunting
    if (unit->profession == profession::TRAINED_HUNTER || 
        unit->profession == profession::TRAINED_WAR) {
        return false;
    }
    
    // Check if there are other animals of the same type that could be slaughtered instead
    int32_t same_type_count = 0;
    int32_t younger_count = 0;
    int32_t unit_age = days_since(unit->birth_year, unit->birth_time);
    
    for (auto u : world->units.active) {
        if (u->race == unit->race && u->caste == unit->caste && 
            Units::isOwnCiv(u) && !u->flags1.bits.dead && 
            !u->flags2.bits.slaughter) {
            same_type_count++;
            int32_t other_age = days_since(u->birth_year, u->birth_time);
            if (other_age < unit_age) {
                younger_count++;
            }
        }
    }
    
    // Keep at least 2 breeding pairs if possible (4 total)
    if (same_type_count <= 4) {
        return false;
    }
    
    // Prefer to slaughter older animals if we have younger ones
    if (younger_count >= 2) {
        ai.debug(out, stl_sprintf("Recommending slaughter for unit %d: age %d days, %d younger alternatives available", 
                                 unit->id, unit_age, younger_count));
        return true;
    }
    
    return false;
}
