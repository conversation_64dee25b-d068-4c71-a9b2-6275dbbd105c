#pragma once

#include "../utils/common/common.h"
#include "../debug/logging.h"
#include "../planning/plan_types.h"

#include <unordered_map>
#include <queue>
#include <mutex>
#include <atomic>

namespace dfai {
namespace resources {

/**
 * @brief Intelligent resource management system
 * 
 * Provides advanced resource allocation, prediction, and optimization
 * for both material and virtual resources (CPU, memory, etc.).
 */
class IntelligentResourceManager {
public:
    IntelligentResourceManager();
    ~IntelligentResourceManager();
    
    // Lifecycle
    bool Initialize(color_ostream& out);
    void Shutdown();
    void Update(color_ostream& out);
    
    // Resource types
    enum class ResourceType {
        MATERIAL,       // Physical materials (wood, stone, etc.)
        VIRTUAL,        // CPU, memory, network bandwidth
        TEMPORAL,       // Time-based resources (dwarf labor hours)
        SPATIAL         // Space-based resources (storage, room capacity)
    };
    
    struct ResourceDefinition {
        std::string name;
        ResourceType type;
        double total_capacity;
        double current_amount;
        double reserved_amount;
        double minimum_threshold;  // Alert when below this
        double optimal_threshold;  // Try to maintain above this
        bool is_renewable;
        double renewal_rate;       // units per tick
        std::chrono::steady_clock::time_point last_updated;
        
        // Prediction data
        std::vector<double> usage_history;
        double predicted_usage_rate;
        double confidence_level;
        
        double available_amount() const { return current_amount - reserved_amount; }
        double utilization_percent() const { return (total_capacity > 0) ? (current_amount / total_capacity) * 100.0 : 0.0; }
        bool is_critical() const { return current_amount < minimum_threshold; }
        bool is_optimal() const { return current_amount >= optimal_threshold; }
    };
    
    // Resource registration and management
    void RegisterResource(const std::string& name, ResourceType type, double total_capacity, 
                         double initial_amount = 0.0, bool renewable = false, double renewal_rate = 0.0);
    void UnregisterResource(const std::string& name);
    bool HasResource(const std::string& name) const;
    
    // Resource queries
    ResourceDefinition GetResourceInfo(const std::string& name) const;
    std::vector<ResourceDefinition> GetAllResources() const;
    std::vector<ResourceDefinition> GetResourcesByType(ResourceType type) const;
    std::vector<std::string> GetCriticalResources() const;
    std::vector<std::string> GetAvailableResources(double min_amount = 0.0) const;
    
    // Resource allocation
    struct AllocationRequest {
        std::string resource_name;
        double amount;
        int32_t priority;           // 1-10, 10 being highest
        std::string requester_id;
        std::chrono::steady_clock::time_point deadline;
        bool is_essential;          // Cannot be denied
        
        AllocationRequest(const std::string& name, double req_amount, int32_t req_priority,
                         const std::string& requester, bool essential = false)
            : resource_name(name), amount(req_amount), priority(req_priority)
            , requester_id(requester), is_essential(essential)
            , deadline(std::chrono::steady_clock::now() + std::chrono::hours(24)) {}
    };
    
    struct AllocationResult {
        bool success = false;
        double allocated_amount = 0.0;
        std::string allocation_id;
        std::string failure_reason;
        std::chrono::steady_clock::time_point expires_at;
        
        AllocationResult() = default;
        AllocationResult(bool success_, double amount, const std::string& id = "")
            : success(success_), allocated_amount(amount), allocation_id(id)
            , expires_at(std::chrono::steady_clock::now() + std::chrono::hours(1)) {}
    };
    
    AllocationResult AllocateResource(const AllocationRequest& request);
    bool ReleaseAllocation(const std::string& allocation_id);
    bool ReleaseAllAllocations(const std::string& requester_id);
    
    // Batch operations
    std::map<std::string, AllocationResult> AllocateResources(const std::vector<AllocationRequest>& requests);
    void ReleaseExpiredAllocations();
    
    // Resource updates
    void SetResourceAmount(const std::string& name, double amount);
    void AddResourceAmount(const std::string& name, double amount);
    void ConsumeResource(const std::string& name, double amount);
    void SetResourceCapacity(const std::string& name, double capacity);
    
    // Thresholds and alerts
    void SetThresholds(const std::string& name, double minimum, double optimal);
    void RegisterAlertCallback(const std::string& resource_name, 
                              std::function<void(const ResourceDefinition&, const std::string&)> callback);
    void UnregisterAlertCallback(const std::string& resource_name);
    
    // Prediction and optimization
    void UpdateUsagePrediction(const std::string& name);
    double GetPredictedUsage(const std::string& name, std::chrono::seconds time_ahead) const;
    double GetPredictionConfidence(const std::string& name) const;
    
    std::vector<std::string> GetOptimizationSuggestions() const;
    void OptimizeResourceDistribution();
    
    // Resource planning
    struct ResourcePlan {
        std::string name;
        std::vector<std::pair<std::chrono::steady_clock::time_point, double>> planned_usage;
        double total_planned_consumption;
        std::chrono::steady_clock::time_point plan_horizon;
        double feasibility_score;  // 0.0 to 1.0
    };
    
    ResourcePlan CreateResourcePlan(const std::string& name, 
                                   const std::vector<AllocationRequest>& future_requests) const;
    bool ValidateResourcePlan(const ResourcePlan& plan) const;
    std::vector<std::string> GetPlanningConflicts(const std::vector<ResourcePlan>& plans) const;
    
    // Monitoring and reporting
    struct ResourceStats {
        size_t total_resources = 0;
        size_t critical_resources = 0;
        size_t active_allocations = 0;
        double total_utilization_percent = 0.0;
        size_t prediction_updates = 0;
        size_t optimization_runs = 0;
        std::chrono::steady_clock::time_point last_optimization;
    };
    
    ResourceStats GetStatistics() const;
    std::string GenerateResourceReport(bool detailed = false) const;
    void ExportResourceData(const std::string& filename) const;
    
    // Configuration
    void SetPredictionWindow(std::chrono::seconds window) { prediction_window_ = window; }
    void SetOptimizationInterval(std::chrono::seconds interval) { optimization_interval_ = interval; }
    void SetAllocationTimeout(std::chrono::seconds timeout) { allocation_timeout_ = timeout; }
    void EnableAutoOptimization(bool enabled) { auto_optimization_enabled_ = enabled; }

private:
    debug::Logger& logger_;
    mutable std::shared_mutex resources_mutex_;
    mutable std::mutex allocations_mutex_;
    
    // Resource storage
    std::unordered_map<std::string, ResourceDefinition> resources_;
    
    // Allocation tracking
    struct ActiveAllocation {
        std::string id;
        std::string resource_name;
        std::string requester_id;
        double amount;
        std::chrono::steady_clock::time_point created_at;
        std::chrono::steady_clock::time_point expires_at;
    };
    
    std::unordered_map<std::string, ActiveAllocation> active_allocations_;
    std::atomic<size_t> next_allocation_id_;
    
    // Alert callbacks
    std::unordered_map<std::string, std::function<void(const ResourceDefinition&, const std::string&)>> alert_callbacks_;
    mutable std::mutex callbacks_mutex_;
    
    // Statistics
    mutable ResourceStats stats_;
    std::atomic<size_t> total_allocations_made_;
    std::atomic<size_t> failed_allocations_;
    
    // Configuration
    std::chrono::seconds prediction_window_;
    std::chrono::seconds optimization_interval_;
    std::chrono::seconds allocation_timeout_;
    bool auto_optimization_enabled_;
    std::chrono::steady_clock::time_point last_optimization_run_;
    
    // Private methods
    std::string GenerateAllocationId();
    void UpdateResourceStatistics();
    void ProcessAlerts();
    
    // Prediction algorithms
    double PredictLinearTrend(const std::vector<double>& history) const;
    double PredictSeasonalPattern(const std::vector<double>& history) const;
    double CalculatePredictionConfidence(const std::vector<double>& history, double prediction) const;
    
    // Optimization algorithms
    void BalanceResourceDistribution();
    void IdentifyResourceBottlenecks();
    std::vector<std::string> GenerateOptimizationRecommendations() const;
    
    // Allocation algorithms
    bool CanAllocate(const std::string& resource_name, double amount) const;
    std::vector<AllocationRequest> PrioritizeRequests(std::vector<AllocationRequest> requests) const;
    void ResolveAllocationConflicts(std::vector<AllocationRequest>& requests) const;
    
    // Validation and safety
    bool ValidateResourceName(const std::string& name) const;
    bool ValidateAmount(double amount) const;
    void SanitizeResourceDefinition(ResourceDefinition& resource) const;
};

// Convenient macros for resource management
#define ALLOCATE_RESOURCE(manager, name, amount, requester) \
    manager.AllocateResource(dfai::resources::IntelligentResourceManager::AllocationRequest(name, amount, 5, requester))

#define ALLOCATE_CRITICAL_RESOURCE(manager, name, amount, requester) \
    manager.AllocateResource(dfai::resources::IntelligentResourceManager::AllocationRequest(name, amount, 10, requester, true))

#define CHECK_RESOURCE_AVAILABLE(manager, name, amount) \
    (manager.HasResource(name) && manager.GetResourceInfo(name).available_amount() >= amount)

} // namespace resources
} // namespace dfai
