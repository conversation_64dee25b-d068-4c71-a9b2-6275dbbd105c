{"$schema": "https://ben.lubar.me/df-ai-schemas/room-template.json", "f": [{"type": "hatch", "x": 2, "y": 2, "z": 0, "dig": "DownStair", "construction": "DownStair"}, {"type": "cage_trap", "x": 0, "y": 0, "z": 0}, {"type": "cage_trap", "x": 1, "y": 0, "z": 0}, {"type": "cage_trap", "x": 2, "y": 0, "z": 0}, {"type": "cage_trap", "x": 3, "y": 0, "z": 0}, {"type": "cage_trap", "x": 4, "y": 0, "z": 0}, {"type": "cage_trap", "x": 0, "y": 1, "z": 0}, {"type": "cage_trap", "x": 1, "y": 1, "z": 0}, {"type": "cage_trap", "x": 2, "y": 1, "z": 0}, {"type": "cage_trap", "x": 3, "y": 1, "z": 0}, {"type": "cage_trap", "x": 4, "y": 1, "z": 0}, {"type": "cage_trap", "x": 0, "y": 2, "z": 0}, {"type": "cage_trap", "x": 1, "y": 2, "z": 0}, {"type": "cage_trap", "x": 3, "y": 2, "z": 0}, {"type": "cage_trap", "x": 4, "y": 2, "z": 0}, {"type": "cage_trap", "x": 0, "y": 3, "z": 0}, {"type": "cage_trap", "x": 1, "y": 3, "z": 0}, {"type": "cage_trap", "x": 2, "y": 3, "z": 0}, {"type": "cage_trap", "x": 3, "y": 3, "z": 0}, {"type": "cage_trap", "x": 4, "y": 3, "z": 0}, {"type": "cage_trap", "x": 0, "y": 4, "z": 0}, {"type": "cage_trap", "x": 1, "y": 4, "z": 0}, {"type": "cage_trap", "x": 2, "y": 4, "z": 0}, {"type": "cage_trap", "x": 3, "y": 4, "z": 0}, {"type": "cage_trap", "x": 4, "y": 4, "z": 0}, {"x": 0, "y": 0, "z": 0, "dig": "UpDownStair", "construction": "UpDownStair"}], "r": [{"type": "outpost", "outpost_type": "cavern", "min": [-2, -2, 0], "max": [2, 2, 0], "outdoor": true, "layout": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]}, {"type": "corridor", "corridor_type": "outpost", "min": [0, 0, -1], "max": [0, 0, -1], "layout": [25], "accesspath": [0]}]}