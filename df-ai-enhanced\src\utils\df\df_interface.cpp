#include "df_interface.h"
#include "../common/types.h"
#include "modules/Maps.h"
#include "modules/Units.h"
#include "modules/Items.h"
#include "modules/Buildings.h"
#include "modules/Job.h"
#include "df/world.h"
#include "df/unit.h"
#include "df/item.h"
#include "df/building.h"
#include "df/job.h"
#include "df/map_block.h"

namespace dfai {
namespace utils {
namespace df {

// World access utilities
bool IsWorldLoaded() {
    return DFHack::World::isFortressMode() || DFHack::World::isAdventureMode();
}

bool IsFortressMode() {
    return DFHack::World::isFortressMode();
}

bool IsAdventureMode() {
    return DFHack::World::isAdventureMode();
}

bool IsPaused() {
    return DFHack::World::ReadPauseState();
}

void SetPaused(bool paused) {
    DFHack::World::SetPauseState(paused);
}

int32_t GetCurrentTick() {
    return ::world ? ::world->frame_counter : 0;
}

int32_t GetCurrentYear() {
    return ::world ? ::world->cur_year : 0;
}

int32_t GetCurrentSeason() {
    return ::world ? ::world->cur_season : 0;
}

std::string GetFortressName() {
    if (!::world || !::world->world_data.active_site[0]) {
        return "Unknown Fortress";
    }
    
    auto site = ::world->world_data.active_site[0];
    if (site->name.has_name) {
        return DFHack::Translation::TranslateName(&site->name, false);
    }
    
    return "Unnamed Fortress";
}

// Map utilities
bool IsValidCoordinate(const Coordinate& coord) {
    return DFHack::Maps::isValidTilePos(coord.x, coord.y, coord.z);
}

bool IsValidCoordinate(int x, int y, int z) {
    return DFHack::Maps::isValidTilePos(x, y, z);
}

df::tiletype GetTileType(const Coordinate& coord) {
    return DFHack::Maps::getTileType(coord.x, coord.y, coord.z);
}

df::tiletype GetTileType(int x, int y, int z) {
    return DFHack::Maps::getTileType(x, y, z);
}

bool SetTileType(const Coordinate& coord, df::tiletype type) {
    return DFHack::Maps::setTileType(coord.x, coord.y, coord.z, type);
}

bool SetTileType(int x, int y, int z, df::tiletype type) {
    return DFHack::Maps::setTileType(x, y, z, type);
}

df::tile_designation GetTileDesignation(const Coordinate& coord) {
    return DFHack::Maps::getTileDesignation(coord.x, coord.y, coord.z);
}

df::tile_designation GetTileDesignation(int x, int y, int z) {
    return DFHack::Maps::getTileDesignation(x, y, z);
}

bool SetTileDesignation(const Coordinate& coord, const df::tile_designation& designation) {
    return DFHack::Maps::setTileDesignation(coord.x, coord.y, coord.z, designation);
}

bool SetTileDesignation(int x, int y, int z, const df::tile_designation& designation) {
    return DFHack::Maps::setTileDesignation(x, y, z, designation);
}

df::tile_occupancy GetTileOccupancy(const Coordinate& coord) {
    return DFHack::Maps::getTileOccupancy(coord.x, coord.y, coord.z);
}

df::tile_occupancy GetTileOccupancy(int x, int y, int z) {
    return DFHack::Maps::getTileOccupancy(x, y, z);
}

bool SetTileOccupancy(const Coordinate& coord, const df::tile_occupancy& occupancy) {
    return DFHack::Maps::setTileOccupancy(coord.x, coord.y, coord.z, occupancy);
}

bool SetTileOccupancy(int x, int y, int z, const df::tile_occupancy& occupancy) {
    return DFHack::Maps::setTileOccupancy(x, y, z, occupancy);
}

bool IsWalkable(const Coordinate& coord) {
    if (!IsValidCoordinate(coord)) {
        return false;
    }
    
    df::tiletype tile = GetTileType(coord);
    df::tiletype_shape shape = tileShape(tile);
    
    return shape == df::tiletype_shape::FLOOR || 
           shape == df::tiletype_shape::STAIR_UP ||
           shape == df::tiletype_shape::STAIR_DOWN ||
           shape == df::tiletype_shape::STAIR_UPDOWN ||
           shape == df::tiletype_shape::RAMP;
}

bool IsPassable(const Coordinate& coord) {
    if (!IsWalkable(coord)) {
        return false;
    }
    
    df::tile_occupancy occupancy = GetTileOccupancy(coord);
    return occupancy.bits.building == df::tile_building_occ::None;
}

bool IsDiggable(const Coordinate& coord) {
    if (!IsValidCoordinate(coord)) {
        return false;
    }
    
    df::tiletype tile = GetTileType(coord);
    df::tiletype_material material = tileMaterial(tile);
    df::tiletype_shape shape = tileShape(tile);
    
    return (material == df::tiletype_material::STONE ||
            material == df::tiletype_material::MINERAL ||
            material == df::tiletype_material::SOIL) &&
           (shape == df::tiletype_shape::WALL);
}

// Unit utilities
std::vector<df::unit*> GetAllUnits() {
    std::vector<df::unit*> units;
    if (::world) {
        for (auto unit : ::world->units.active) {
            if (unit) {
                units.push_back(unit);
            }
        }
    }
    return units;
}

std::vector<df::unit*> GetCitizenUnits() {
    std::vector<df::unit*> citizens;
    for (auto unit : GetAllUnits()) {
        if (DFHack::Units::isCitizen(unit)) {
            citizens.push_back(unit);
        }
    }
    return citizens;
}

std::vector<df::unit*> GetEnemyUnits() {
    std::vector<df::unit*> enemies;
    for (auto unit : GetAllUnits()) {
        if (DFHack::Units::isEnemy(unit)) {
            enemies.push_back(unit);
        }
    }
    return enemies;
}

df::unit* GetUnitById(int32_t unit_id) {
    return df::unit::find(unit_id);
}

bool IsUnitAlive(df::unit* unit) {
    return unit && DFHack::Units::isAlive(unit);
}

bool IsUnitCitizen(df::unit* unit) {
    return unit && DFHack::Units::isCitizen(unit);
}

bool IsUnitEnemy(df::unit* unit) {
    return unit && DFHack::Units::isEnemy(unit);
}

Coordinate GetUnitPosition(df::unit* unit) {
    if (!unit) {
        return {-1, -1, -1};
    }
    return {unit->pos.x, unit->pos.y, unit->pos.z};
}

std::string GetUnitName(df::unit* unit) {
    if (!unit) {
        return "Unknown Unit";
    }
    return DFHack::Units::getReadableName(unit);
}

std::string GetUnitProfession(df::unit* unit) {
    if (!unit) {
        return "Unknown";
    }
    return DFHack::Units::getProfessionName(unit);
}

// Item utilities
std::vector<df::item*> GetAllItems() {
    std::vector<df::item*> items;
    if (::world) {
        for (auto item : ::world->items.other[df::items_other_id::IN_PLAY]) {
            if (item) {
                items.push_back(item);
            }
        }
    }
    return items;
}

std::vector<df::item*> GetItemsAtPosition(const Coordinate& coord) {
    std::vector<df::item*> items;
    for (auto item : GetAllItems()) {
        if (item && item->pos.x == coord.x && item->pos.y == coord.y && item->pos.z == coord.z) {
            items.push_back(item);
        }
    }
    return items;
}

df::item* GetItemById(int32_t item_id) {
    return df::item::find(item_id);
}

Coordinate GetItemPosition(df::item* item) {
    if (!item) {
        return {-1, -1, -1};
    }
    return {item->pos.x, item->pos.y, item->pos.z};
}

std::string GetItemName(df::item* item) {
    if (!item) {
        return "Unknown Item";
    }
    return DFHack::Items::getDescription(item, 0, true);
}

df::item_type GetItemType(df::item* item) {
    if (!item) {
        return df::item_type::NONE;
    }
    return item->getType();
}

// Building utilities
std::vector<df::building*> GetAllBuildings() {
    std::vector<df::building*> buildings;
    if (::world) {
        for (auto building : ::world->buildings.other[df::buildings_other_id::IN_PLAY]) {
            if (building) {
                buildings.push_back(building);
            }
        }
    }
    return buildings;
}

std::vector<df::building*> GetBuildingsAtPosition(const Coordinate& coord) {
    std::vector<df::building*> buildings;
    for (auto building : GetAllBuildings()) {
        if (building && building->containsTile(coord.x, coord.y, coord.z)) {
            buildings.push_back(building);
        }
    }
    return buildings;
}

df::building* GetBuildingById(int32_t building_id) {
    return df::building::find(building_id);
}

Coordinate GetBuildingPosition(df::building* building) {
    if (!building) {
        return {-1, -1, -1};
    }
    return {building->centerx, building->centery, building->z};
}

Rectangle GetBuildingBounds(df::building* building) {
    if (!building) {
        return {{-1, -1, -1}, {-1, -1, -1}};
    }
    return {{building->x1, building->y1, building->z}, {building->x2, building->y2, building->z}};
}

std::string GetBuildingName(df::building* building) {
    if (!building) {
        return "Unknown Building";
    }
    return DFHack::Buildings::getName(building);
}

df::building_type GetBuildingType(df::building* building) {
    if (!building) {
        return df::building_type::NONE;
    }
    return building->getType();
}

// Job utilities
std::vector<df::job*> GetAllJobs() {
    std::vector<df::job*> jobs;
    if (::world) {
        for (auto job : ::world->jobs.list.active) {
            if (job) {
                jobs.push_back(job);
            }
        }
    }
    return jobs;
}

df::job* GetJobById(int32_t job_id) {
    for (auto job : GetAllJobs()) {
        if (job && job->id == job_id) {
            return job;
        }
    }
    return nullptr;
}

std::vector<df::job*> GetJobsForUnit(df::unit* unit) {
    std::vector<df::job*> jobs;
    if (!unit) {
        return jobs;
    }
    
    for (auto job : GetAllJobs()) {
        if (job && !job->general_refs.empty()) {
            for (auto ref : job->general_refs) {
                if (ref->getType() == df::general_ref_type::UNIT_WORKER && 
                    ref->getUnit() == unit) {
                    jobs.push_back(job);
                    break;
                }
            }
        }
    }
    return jobs;
}

Coordinate GetJobPosition(df::job* job) {
    if (!job) {
        return {-1, -1, -1};
    }
    return {job->pos.x, job->pos.y, job->pos.z};
}

std::string GetJobName(df::job* job) {
    if (!job) {
        return "Unknown Job";
    }
    return DFHack::Job::getName(job);
}

df::job_type GetJobType(df::job* job) {
    if (!job) {
        return df::job_type::NONE;
    }
    return job->job_type;
}

} // namespace df
} // namespace utils
} // namespace dfai
