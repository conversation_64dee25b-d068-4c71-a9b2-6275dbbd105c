#pragma once

#include "../utils/common.h"
#include "../utils/logging.h"
#include "plan_types.h"
#include "task_manager.h"
#include "blueprint_manager.h"

#include <list>
#include <map>
#include <vector>
#include <set>

namespace df_ai {

// Forward declaration
namespace core { class AIController; }

namespace planning {

/**
 * Manages all planning and construction activities
 */
class PlanManager {
private:
    core::AIController& ai_;
    utils::Logger& logger_;
    
    // Task management
    std::unique_ptr<TaskManager> task_manager_;
    std::unique_ptr<BlueprintManager> blueprint_manager_;
    
    // Room and corridor management
    std::vector<room*> rooms_and_corridors_;
    std::vector<PlanPriority> priorities_;
    std::map<room_type::type, std::vector<room*>> rooms_by_type_;
    std::map<int32_t, std::set<room*>> rooms_by_z_level_;
    
    // Fort structure
    room* fort_entrance_;
    std::set<stock_item::item> no_furnish_cache_;
    
    // Mining and geology
    std::map<int32_t, std::vector<std::pair<df::coord, int32_t>>> mineral_veins_;
    std::map<int32_t, size_t> dig_counts_;
    std::map<int32_t, std::vector<std::pair<df::coord, df::tile_dig_designation>>> vein_queue_;
    std::set<df::coord> dug_veins_;
    
    // Workshop management
    std::vector<df::workshop_type> important_workshops_;
    std::vector<df::furnace_type> important_furnaces_;
    std::vector<df::workshop_type> critical_workshops_;
    
    // Cistern management
    furniture* cistern_lever_in_;
    furniture* cistern_lever_out_;
    room* main_cistern_;
    room* reserve_cistern_;
    int32_t cistern_testgate_delay_;
    bool cistern_channel_requested_;
    
    // Construction state
    size_t room_check_index_;
    size_t cistern_try_count_;
    int32_t noble_suite_index_;
    int16_t max_cavern_level_;
    int32_t last_idle_year_;
    bool allow_ice_construction_;
    bool should_search_for_metal_;
    bool past_initial_phase_;
    bool deconstructed_wagons_;
    
    // Update tracking
    OnupdateCallback* onupdate_handle_;
    int32_t last_update_year_;
    int32_t last_update_tick_;
    
public:
    PlanManager(core::AIController& ai, utils::Logger& logger);
    ~PlanManager();
    
    // Lifecycle management
    command_result startup(color_ostream& out);
    command_result shutdown(color_ostream& out);
    command_result register_updates(color_ostream& out);
    command_result unregister_updates(color_ostream& out);
    
    // Main update loop
    void update(color_ostream& out);
    
    // Room management
    void add_room(room* r);
    void remove_room(room* r);
    room* find_room(room_type::type type);
    room* find_room(room_type::type type, std::function<bool(room*)> predicate);
    room* find_room_at(df::coord pos);
    std::vector<room*> find_rooms(room_type::type type);
    
    // Room assignment and configuration  
    void assign_bedroom(color_ostream& out, int32_t unit_id);
    void assign_dining_room(color_ostream& out, int32_t unit_id);
    void assign_office(color_ostream& out, int32_t unit_id);
    void assign_tomb(color_ostream& out, int32_t unit_id);
    void assign_soldier_barracks(color_ostream& out, int32_t unit_id);
    void assign_barrack_squad(color_ostream& out, df::building* building, int32_t squad_id);
    void assign_noble_rooms(color_ostream& out, const std::set<int32_t>& unit_ids);
    
    // Construction management
    void request_dig(color_ostream& out, room* r);
    void request_construction(color_ostream& out, room* r);
    void request_furniture(color_ostream& out, room* r);
    bool construct_room(color_ostream& out, room* r);
    bool construct_building(color_ostream& out, df::coord pos, df::building_type type);
    
    // Digging operations
    void dig_room(color_ostream& out, room* r);
    void dig_corridor(color_ostream& out, room* corridor);
    void channel_area(color_ostream& out, df::coord min, df::coord max);
    bool smooth_room(color_ostream& out, room* r, bool engrave = false);
    bool smooth_area(color_ostream& out, df::coord min, df::coord max, bool engrave = false);
    
    // Cistern management
    bool construct_cistern(color_ostream& out, room* r);
    void smooth_cistern(color_ostream& out, room* r);
    void smooth_cistern_access(color_ostream& out, room* r);
    bool try_dig_cistern(color_ostream& out, room* r);
    
    // Workshop and furnace management
    void setup_workshops(color_ostream& out);
    void check_workshop_needs(color_ostream& out);
    bool needs_workshop(df::workshop_type type);
    bool needs_furnace(df::furnace_type type);
    
    // Stockpile management
    void setup_stockpiles(color_ostream& out);
    void link_stockpiles(color_ostream& out);
    void configure_stockpile(color_ostream& out, room* stockpile);
    
    // Mining operations
    void search_for_minerals(color_ostream& out);
    void queue_mineral_extraction(color_ostream& out, df::coord pos, int32_t mineral_type);
    void process_mineral_veins(color_ostream& out);
    bool designate_ore_extraction(color_ostream& out);
    
    // Task management delegation
    void add_task(task_type::type type, room* r, furniture* f = nullptr, int32_t item_id = -1);
    void remove_task(task* t);
    task* get_current_task();
    bool has_tasks();
    bool is_idle();
    
    // Blueprint management
    command_result setup_blueprint(color_ostream& out);
    bool build_from_blueprint(color_ostream& out, const std::string& blueprint_name);
    
    // Priorities
    void add_priority(const PlanPriority& priority);
    void remove_priority(const PlanPriority& priority);
    void process_priorities(color_ostream& out);
    
    // Status queries
    bool rooms_ready() const;
    bool pastures_ready(color_ostream& out) const;
    bool workshops_ready() const;
    bool entrance_ready() const;
    
    // Getters
    room* fort_entrance() const { return fort_entrance_; }
    const std::vector<room*>& rooms() const { return rooms_and_corridors_; }
    TaskManager& tasks() { return *task_manager_; }
    BlueprintManager& blueprints() { return *blueprint_manager_; }
    
    // Configuration
    void set_allow_ice(bool allow) { allow_ice_construction_ = allow; }
    bool allow_ice() const { return allow_ice_construction_; }
    void set_search_for_metal(bool search) { should_search_for_metal_ = search; }
    bool search_for_metal() const { return should_search_for_metal_; }
    void set_past_initial_phase(bool past) { past_initial_phase_ = past; }
    bool past_initial_phase() const { return past_initial_phase_; }
    
    // Persistence
    command_result persist(color_ostream& out);
    command_result load_persistent_data(color_ostream& out);
    void save_to_stream(std::ostream& out);
    void load_from_stream(std::istream& in);
    
    // Status and reporting
    std::string status() const;
    void report(std::ostream& out, bool html = false) const;
    
private:
    // Helper methods
    void initialize_managers();
    void check_rooms(color_ostream& out);
    void check_room_integrity(color_ostream& out, room* r);
    void process_dig_queue(color_ostream& out);
    void update_room_categories();
    void find_and_setup_fort_entrance(color_ostream& out);
    bool check_idle_status(color_ostream& out, std::ostream& reason);
    void deconstruct_wagons(color_ostream& out);
    void setup_initial_workshops(color_ostream& out);
    df::coord find_tree_base(df::coord pos, df::plant** tree_out = nullptr);
};

} // namespace planning
} // namespace df_ai
