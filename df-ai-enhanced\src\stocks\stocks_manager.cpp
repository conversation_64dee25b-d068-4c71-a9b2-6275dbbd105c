#include "stocks_manager.h"
#include "../debug/logging.h"

// Forward declare specialized managers
// These are implemented as separate files

namespace dfai {
namespace stocks {

// Specialized managers for different stock categories
class FarmManager {
public:
    bool Initialize(color_ostream& out) { return true; }
    void Shutdown() {}
    void Update(color_ostream& out) {}
    std::string GetStatusReport() const { return "Farm manager: OK"; }
};

class ForgeManager {
public:
    bool Initialize(color_ostream& out) { return true; }
    void Shutdown() {}
    void Update(color_ostream& out) {}
    std::string GetStatusReport() const { return "Forge manager: OK"; }
};

class TradeManager {
public:
    bool Initialize(color_ostream& out) { return true; }
    void Shutdown() {}
    void Update(color_ostream& out) {}
    std::string GetStatusReport() const { return "Trade manager: OK"; }
};

class EquipmentManager {
public:
    bool Initialize(color_ostream& out) { return true; }
    void Shutdown() {}
    void Update(color_ostream& out) {}
    std::string GetStatusReport() const { return "Equipment manager: OK"; }
};

class FoodManager {
public:
    bool Initialize(color_ostream& out) { return true; }
    void Shutdown() {}
    void Update(color_ostream& out) {}
    std::string GetStatusReport() const { return "Food manager: OK"; }
};

class ItemManager {
public:
    bool Initialize(color_ostream& out) { return true; }
    void Shutdown() {}
    void Update(color_ostream& out) {}
    std::string GetStatusReport() const { return "Item manager: OK"; }
};

StocksManager::StocksManager()
    : logger_(debug::Logger::GetInstance())
    , config_(config::ConfigManager::GetInstance())
    , initialized_(false)
    , update_counter_(0)
    , farm_manager_(std::make_unique<FarmManager>())
    , forge_manager_(std::make_unique<ForgeManager>())
    , trade_manager_(std::make_unique<TradeManager>())
    , equipment_manager_(std::make_unique<EquipmentManager>())
    , food_manager_(std::make_unique<FoodManager>())
    , item_manager_(std::make_unique<ItemManager>())
    , item_counts_()
    , monitored_items_()
    , stockpiles_()
    , production_targets_()
    , pending_orders_()
{
    logger_.Info("StocksManager created");
}

StocksManager::~StocksManager() {
    Shutdown();
    logger_.Info("StocksManager destroyed");
}

bool StocksManager::Initialize(color_ostream& out) {
    if (initialized_) {
        logger_.Warning("StocksManager already initialized");
        return true;
    }
    
    logger_.Info("Initializing StocksManager...");
    
    try {
        // Initialize sub-managers
        if (!InitializeSubManagers(out)) {
            logger_.Error("Failed to initialize sub-managers");
            return false;
        }
        
        // Initialize item tracking
        UpdateItemCounts();
        
        // Set up production targets from config
        // This would read from configuration
        
        initialized_ = true;
        logger_.Info("StocksManager initialization complete");
        return true;
        
    } catch (const std::exception& e) {
        logger_.Error("Exception during StocksManager initialization: {}", e.what());
        return false;
    }
}

void StocksManager::Shutdown() {
    if (!initialized_) {
        return;
    }
    
    logger_.Info("Shutting down StocksManager...");
    
    // Shutdown sub-managers in reverse order
    item_manager_.reset();
    food_manager_.reset();
    equipment_manager_.reset();
    trade_manager_.reset();
    forge_manager_.reset();
    farm_manager_.reset();
    
    // Clear tracking data
    item_counts_.clear();
    monitored_items_.clear();
    stockpiles_.clear();
    production_targets_.clear();
    pending_orders_.clear();
    
    initialized_ = false;
    logger_.Info("StocksManager shutdown complete");
}

void StocksManager::Update(color_ostream& out) {
    if (!initialized_) {
        logger_.Warning("StocksManager::Update called before initialization");
        return;
    }
    
    try {
        ++update_counter_;
        
        // Update item tracking
        UpdateItems(out);
        
        // Update production
        UpdateProduction(out);
        
        // Update specialized managers
        farm_manager_->Update(out);
        forge_manager_->Update(out);
        trade_manager_->Update(out);
        equipment_manager_->Update(out);
        food_manager_->Update(out);
        item_manager_->Update(out);
        
        // Periodic deep analysis
        if (update_counter_ % 50 == 0) {
            AnalyzeStockpileContents();
            AnalyzeProductionNeeds();
            AnalyzeTradeOpportunities();
        }
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in StocksManager::Update: {}", e.what());
    }
}

std::string StocksManager::GetStatusReport() const {
    std::ostringstream report;
    
    report << "=== Stocks Manager Status ===\n";
    report << "Initialized: " << (initialized_ ? "Yes" : "No") << "\n";
    report << "Update cycles: " << update_counter_ << "\n";
    report << "Monitored items: " << monitored_items_.size() << "\n";
    report << "Stockpiles: " << stockpiles_.size() << "\n";
    report << "Pending orders: " << pending_orders_.size() << "\n";
    
    // Sub-manager reports
    if (farm_manager_) {
        report << "\n" << farm_manager_->GetStatusReport();
    }
    if (forge_manager_) {
        report << "\n" << forge_manager_->GetStatusReport();
    }
    if (trade_manager_) {
        report << "\n" << trade_manager_->GetStatusReport();
    }
    if (equipment_manager_) {
        report << "\n" << equipment_manager_->GetStatusReport();
    }
    if (food_manager_) {
        report << "\n" << food_manager_->GetStatusReport();
    }
    if (item_manager_) {
        report << "\n" << item_manager_->GetStatusReport();
    }
    
    return report.str();
}

void StocksManager::GenerateReport(std::ostream& out, bool html) const {
    out << GetStatusReport();
    
    // Additional detailed reporting would go here
}

void StocksManager::UpdateItems(color_ostream& out) {
    try {
        UpdateItemCounts();
        
        // Scan for items that need to be hauled to stockpiles
        for (auto* item : world->items.other[items_other_id::ANY_FREE]) {
            if (!item || item->flags.bits.dump || item->flags.bits.forbid) {
                continue;
            }
            
            // Check if item needs to be stockpiled
            if (!item->flags.bits.in_building && !item->flags.bits.in_inventory) {
                // Item is loose - should be hauled to appropriate stockpile
                // This would involve pathfinding and stockpile matching
                // For now, just log items that need attention
                if (item->getType() == item_type::FOOD) {
                    // Food items should be prioritized
                    item->flags.bits.dump = false; // Ensure not marked for dumping
                }
            }
        }
        
        logger_.Debug("Updated item tracking and hauling priorities");
        
    } catch (const std::exception& e) {
        logger_.Error(stl_sprintf("Exception updating items: %s", e.what()));
    }
}

void StocksManager::ScanStockpiles(color_ostream& out) {
    try {
        stockpile_info_.clear();
        
        // Scan all stockpiles for contents and capacity
        for (auto* building : world->buildings.other[buildings_other_id::STOCKPILE]) {
            auto* stockpile = virtual_cast<df::building_stockpilest>(building);
            if (!stockpile) continue;
            
            StockpileInfo info;
            info.id = stockpile->id;
            info.type = GetStockpileType(stockpile);
            info.capacity = CalculateStockpileCapacity(stockpile);
            info.current_items = CountItemsInStockpile(stockpile);
            info.last_scanned = world->frame_counter;
            
            // Calculate usage percentage
            if (info.capacity > 0) {
                info.usage_percent = (float)info.current_items / info.capacity * 100.0f;
            }
            
            stockpile_info_[stockpile->id] = info;
            
            // Log stockpiles that are getting full
            if (info.usage_percent > 90.0f) {
                logger_.Warning(stl_sprintf("Stockpile %d is %0.1f%% full (%d/%d items)",
                    info.id, info.usage_percent, info.current_items, info.capacity));
            }
        }
        
        logger_.Debug(stl_sprintf("Scanned %zu stockpiles", stockpile_info_.size()));
        
    } catch (const std::exception& e) {
        logger_.Error(stl_sprintf("Exception scanning stockpiles: %s", e.what()));
    }
}

void StocksManager::ManageItemPriorities(color_ostream& out) {
    try {
        // Prioritize hauling of essential items
        int food_priority = 0;
        int booze_priority = 0;
        int medical_priority = 0;
        
        // Check current stocks and adjust priorities
        auto food_count = GetItemCount(ItemCategory::FOOD);
        auto booze_count = GetItemCount(ItemCategory::BOOZE);
        
        // Prioritize food hauling if we're running low
        if (food_count < target_counts_[ItemCategory::FOOD] * 0.5f) {
            food_priority = 5; // High priority
        } else if (food_count < target_counts_[ItemCategory::FOOD] * 0.8f) {
            food_priority = 3; // Medium priority
        }
        
        // Prioritize booze hauling if we're running low
        if (booze_count < target_counts_[ItemCategory::BOOZE] * 0.3f) {
            booze_priority = 5; // High priority - dwarves need booze!
        } else if (booze_count < target_counts_[ItemCategory::BOOZE] * 0.6f) {
            booze_priority = 3; // Medium priority
        }
        
        // Always prioritize medical supplies
        medical_priority = 4;
        
        // Apply priorities to actual item hauling
        // This would interface with the labor management system
        // For now, just log the priority adjustments
        
        if (food_priority > 0 || booze_priority > 0 || medical_priority > 0) {
            logger_.Info(stl_sprintf("Updated hauling priorities: Food=%d, Booze=%d, Medical=%d",
                food_priority, booze_priority, medical_priority));
        }
        
    } catch (const std::exception& e) {
        logger_.Error(stl_sprintf("Exception managing item priorities: %s", e.what()));
    }
}

void StocksManager::UpdateProduction(color_ostream& out) {
    UpdateProductionStatus();
    ProcessPendingOrders(out);
}

void StocksManager::QueueProductionOrders(color_ostream& out) {
    // Queue new production orders based on needs
}

void StocksManager::ManageManagerOrders(color_ostream& out) {
    // Manage DwarfFortress manager orders
}

void StocksManager::UpdateEquipment(color_ostream& out) {
    equipment_manager_->Update(out);
}

void StocksManager::AssignEquipment(color_ostream& out) {
    // Assign equipment to dwarves
}

void StocksManager::CheckEquipmentNeeds(color_ostream& out) {
    // Check what equipment is needed
}

void StocksManager::UpdateFarming(color_ostream& out) {
    farm_manager_->Update(out);
}

void StocksManager::ManageFoodProduction(color_ostream& out) {
    food_manager_->Update(out);
}

void StocksManager::CheckFoodStocks(color_ostream& out) {
    // Check food stock levels
}

void StocksManager::UpdateTrade(color_ostream& out) {
    trade_manager_->Update(out);
}

void StocksManager::PrepareTradeGoods(color_ostream& out) {
    // Prepare goods for trading
}

void StocksManager::ManageTradeDeals(color_ostream& out) {
    // Manage trade negotiations and deals
}

void StocksManager::UpdateForge(color_ostream& out) {
    forge_manager_->Update(out);
}

void StocksManager::ManageMetalworking(color_ostream& out) {
    // Manage metalworking operations
}

void StocksManager::CheckRawMaterials(color_ostream& out) {
    // Check raw material stocks
}

// Query methods
int32_t StocksManager::GetItemCount(const std::string& item_type) const {
    auto it = item_counts_.find(item_type);
    return (it != item_counts_.end()) ? it->second : 0;
}

bool StocksManager::HasSufficientFood() const {
    // Check if we have sufficient food stocks
    // Base threshold on fortress population and season
    int32_t food_count = GetItemCount("food");
    int32_t population = GetItemCount("population"); // Estimate based on citizens
    int32_t min_food = std::max(100, population * 10); // 10 food per dwarf minimum
    return food_count > min_food;
}

bool StocksManager::HasSufficientWeapons() const {
    // Check if we have sufficient weapons based on military needs
    int32_t weapon_count = GetItemCount("weapons");
    int32_t military_size = GetItemCount("military"); // Estimate military size
    int32_t min_weapons = std::max(20, military_size * 2); // 2 weapons per military dwarf
    return weapon_count > min_weapons;
}

bool StocksManager::HasSufficientArmor() const {
    // Check if we have sufficient armor based on military needs
    int32_t armor_count = GetItemCount("armor");
    int32_t military_size = GetItemCount("military"); // Estimate military size
    int32_t min_armor = std::max(20, military_size * 5); // 5 armor pieces per military dwarf
    return armor_count > min_armor;
}

// Private helper methods
bool StocksManager::InitializeSubManagers(color_ostream& out) {
    logger_.Info("Initializing stocks sub-managers...");
    
    if (!farm_manager_->Initialize(out)) {
        logger_.Error("Failed to initialize farm manager");
        return false;
    }
    
    if (!forge_manager_->Initialize(out)) {
        logger_.Error("Failed to initialize forge manager");
        return false;
    }
    
    if (!trade_manager_->Initialize(out)) {
        logger_.Error("Failed to initialize trade manager");
        return false;
    }
    
    if (!equipment_manager_->Initialize(out)) {
        logger_.Error("Failed to initialize equipment manager");
        return false;
    }
    
    if (!food_manager_->Initialize(out)) {
        logger_.Error("Failed to initialize food manager");
        return false;
    }
    
    if (!item_manager_->Initialize(out)) {
        logger_.Error("Failed to initialize item manager");
        return false;
    }
    
    logger_.Info("All stocks sub-managers initialized successfully");
    return true;
}

void StocksManager::UpdateItemCounts() {
    // Update item counts from game state
    // Count items in stockpiles and workshops
    item_counts_.clear();
    
    // Basic item counting - would integrate with DF structures
    item_counts_["food"] = CountItemsOfType("food");
    item_counts_["weapons"] = CountItemsOfType("weapons");
    item_counts_["armor"] = CountItemsOfType("armor");
    item_counts_["tools"] = CountItemsOfType("tools");
    item_counts_["materials"] = CountItemsOfType("materials");
    item_counts_["furniture"] = CountItemsOfType("furniture");
    item_counts_["population"] = EstimatePopulation();
    item_counts_["military"] = EstimateMilitarySize();
    
    logger_.Debug("Updated item counts");
}

void StocksManager::UpdateProductionStatus() {
    // Update production status based on workshops and managers
    production_status_.clear();
    
    // Check workshop production rates
    production_status_["food_production"] = CalculateProductionRate("food");
    production_status_["weapon_production"] = CalculateProductionRate("weapons");
    production_status_["armor_production"] = CalculateProductionRate("armor");
    production_status_["tool_production"] = CalculateProductionRate("tools");
    
    logger_.Debug("Updated production status");
}

void StocksManager::ProcessPendingOrders(color_ostream& out) {
    if (pending_orders_.empty()) {
        return;
    }
    
    logger_.Info("Processing {} pending orders", pending_orders_.size());
    // Process orders...
    pending_orders_.clear();
}

// Helper methods for item counting and production calculation
int32_t StocksManager::CountItemsOfType(const std::string& type) const {
    // This would integrate with DF item structures
    // For now, return a basic estimate
    if (type == "food") return 150;
    if (type == "weapons") return 25;
    if (type == "armor") return 30;
    if (type == "tools") return 40;
    if (type == "materials") return 200;
    if (type == "furniture") return 50;
    return 10;
}

int32_t StocksManager::EstimatePopulation() const {
    // This would count actual citizens from DF structures
    return 80; // Basic estimate
}

int32_t StocksManager::EstimateMilitarySize() const {
    // This would count military units from DF structures
    return 12; // Basic estimate
}

int32_t StocksManager::CalculateProductionRate(const std::string& type) const {
    // This would calculate actual production based on workshops
    // For now, return basic rates
    if (type == "food") return 5; // Items per day
    if (type == "weapons") return 1;
    if (type == "armor") return 1;
    if (type == "tools") return 2;
    return 1;
}

void StocksManager::AnalyzeStockpileContents() {
    logger_.Debug("Analyzing stockpile contents");
    // Deep analysis of stockpile contents
}

void StocksManager::AnalyzeProductionNeeds() {
    logger_.Debug("Analyzing production needs");
    // Analyze what production is needed
}

void StocksManager::AnalyzeTradeOpportunities() {
    logger_.Debug("Analyzing trade opportunities");
    // Analyze potential trade opportunities
}

} // namespace stocks
} // namespace dfai
