#include "coordinate_utils.h"
#include "../common/types.h"
#include "df_interface.h"
#include <algorithm>
#include <cmath>

namespace dfai {
namespace utils {
namespace df {

// Coordinate conversion utilities
df::coord ToDF(const Coordinate& coord) {
    df::coord result;
    result.x = coord.x;
    result.y = coord.y;
    result.z = coord.z;
    return result;
}

Coordinate FromDF(const df::coord& coord) {
    return {coord.x, coord.y, coord.z};
}

std::vector<df::coord> ToDF(const std::vector<Coordinate>& coords) {
    std::vector<df::coord> result;
    result.reserve(coords.size());
    
    for (const Coordinate& coord : coords) {
        result.push_back(ToDF(coord));
    }
    
    return result;
}

std::vector<Coordinate> FromDF(const std::vector<df::coord>& coords) {
    std::vector<Coordinate> result;
    result.reserve(coords.size());
    
    for (const df::coord& coord : coords) {
        result.push_back(FromDF(coord));
    }
    
    return result;
}

// Coordinate validation
bool IsValidMapCoordinate(const Coordinate& coord) {
    return IsValidCoordinate(coord);
}

bool IsValidMapCoordinate(int x, int y, int z) {
    return IsValidCoordinate(x, y, z);
}

bool IsWithinBounds(const Coordinate& coord, const Rectangle& bounds) {
    return bounds.Contains(coord);
}

bool IsOnMapEdge(const Coordinate& coord) {
    if (!IsValidMapCoordinate(coord)) {
        return false;
    }
    
    // Check if coordinate is on the edge of the map
    // This is a simplified check - actual map bounds would need to be queried from DF
    return coord.x == 0 || coord.y == 0 || coord.z == 0;
}

bool IsAboveGround(const Coordinate& coord) {
    // In Dwarf Fortress, z-levels above 0 are typically above ground
    // This may vary depending on the specific map
    return coord.z > 0;
}

bool IsUnderground(const Coordinate& coord) {
    return coord.z <= 0;
}

// Coordinate arithmetic
Coordinate AddOffset(const Coordinate& coord, const Coordinate& offset) {
    return coord + offset;
}

Coordinate SubtractOffset(const Coordinate& coord, const Coordinate& offset) {
    return coord - offset;
}

Coordinate ScaleCoordinate(const Coordinate& coord, float scale) {
    return {
        static_cast<int>(coord.x * scale),
        static_cast<int>(coord.y * scale),
        static_cast<int>(coord.z * scale)
    };
}

Coordinate ClampToMap(const Coordinate& coord) {
    // This would need actual map bounds from DF
    // For now, just ensure coordinates are non-negative
    return {
        std::max(0, coord.x),
        std::max(0, coord.y),
        std::max(0, coord.z)
    };
}

// Distance calculations
float CalculateDistance2D(const Coordinate& a, const Coordinate& b) {
    int dx = a.x - b.x;
    int dy = a.y - b.y;
    return std::sqrt(static_cast<float>(dx*dx + dy*dy));
}

float CalculateDistance3D(const Coordinate& a, const Coordinate& b) {
    return static_cast<float>(a.DistanceTo(b));
}

int CalculateManhattanDistance2D(const Coordinate& a, const Coordinate& b) {
    return std::abs(a.x - b.x) + std::abs(a.y - b.y);
}

int CalculateManhattanDistance3D(const Coordinate& a, const Coordinate& b) {
    return a.ManhattanDistanceTo(b);
}

float CalculateChebyshevDistance2D(const Coordinate& a, const Coordinate& b) {
    int dx = std::abs(a.x - b.x);
    int dy = std::abs(a.y - b.y);
    return static_cast<float>(std::max(dx, dy));
}

float CalculateChebyshevDistance3D(const Coordinate& a, const Coordinate& b) {
    int dx = std::abs(a.x - b.x);
    int dy = std::abs(a.y - b.y);
    int dz = std::abs(a.z - b.z);
    return static_cast<float>(std::max({dx, dy, dz}));
}

// Neighbor finding
std::vector<Coordinate> GetAdjacentCoordinates(const Coordinate& coord, bool include_diagonals) {
    std::vector<Coordinate> neighbors;
    
    // Cardinal directions
    neighbors.push_back({coord.x + 1, coord.y, coord.z}); // East
    neighbors.push_back({coord.x - 1, coord.y, coord.z}); // West
    neighbors.push_back({coord.x, coord.y + 1, coord.z}); // South
    neighbors.push_back({coord.x, coord.y - 1, coord.z}); // North
    
    if (include_diagonals) {
        // Diagonal directions
        neighbors.push_back({coord.x + 1, coord.y + 1, coord.z}); // Southeast
        neighbors.push_back({coord.x + 1, coord.y - 1, coord.z}); // Northeast
        neighbors.push_back({coord.x - 1, coord.y + 1, coord.z}); // Southwest
        neighbors.push_back({coord.x - 1, coord.y - 1, coord.z}); // Northwest
    }
    
    return neighbors;
}

std::vector<Coordinate> GetNeighborsInRadius(const Coordinate& center, int radius, bool include_center) {
    std::vector<Coordinate> neighbors;
    
    for (int dx = -radius; dx <= radius; ++dx) {
        for (int dy = -radius; dy <= radius; ++dy) {
            if (!include_center && dx == 0 && dy == 0) {
                continue;
            }
            
            // Check if within circular radius
            if (dx*dx + dy*dy <= radius*radius) {
                neighbors.push_back({center.x + dx, center.y + dy, center.z});
            }
        }
    }
    
    return neighbors;
}

std::vector<Coordinate> GetNeighborsInSquare(const Coordinate& center, int radius, bool include_center) {
    std::vector<Coordinate> neighbors;
    
    for (int dx = -radius; dx <= radius; ++dx) {
        for (int dy = -radius; dy <= radius; ++dy) {
            if (!include_center && dx == 0 && dy == 0) {
                continue;
            }
            
            neighbors.push_back({center.x + dx, center.y + dy, center.z});
        }
    }
    
    return neighbors;
}

std::vector<Coordinate> GetVerticalNeighbors(const Coordinate& coord) {
    std::vector<Coordinate> neighbors;
    neighbors.push_back({coord.x, coord.y, coord.z + 1}); // Up
    neighbors.push_back({coord.x, coord.y, coord.z - 1}); // Down
    return neighbors;
}

// Direction utilities
Direction GetDirectionTo(const Coordinate& from, const Coordinate& to) {
    int dx = to.x - from.x;
    int dy = to.y - from.y;
    
    // Normalize to -1, 0, or 1
    int norm_dx = (dx > 0) ? 1 : (dx < 0) ? -1 : 0;
    int norm_dy = (dy > 0) ? 1 : (dy < 0) ? -1 : 0;
    
    if (norm_dx == 0 && norm_dy == -1) return DIRECTION_NORTH;
    if (norm_dx == 0 && norm_dy == 1) return DIRECTION_SOUTH;
    if (norm_dx == 1 && norm_dy == 0) return DIRECTION_EAST;
    if (norm_dx == -1 && norm_dy == 0) return DIRECTION_WEST;
    if (norm_dx == 1 && norm_dy == -1) return DIRECTION_NORTHEAST;
    if (norm_dx == -1 && norm_dy == -1) return DIRECTION_NORTHWEST;
    if (norm_dx == 1 && norm_dy == 1) return DIRECTION_SOUTHEAST;
    if (norm_dx == -1 && norm_dy == 1) return DIRECTION_SOUTHWEST;
    
    return DIRECTION_NORTH; // Default
}

Coordinate GetCoordinateInDirection(const Coordinate& coord, Direction direction, int distance) {
    Coordinate offset = DirectionToOffset(direction);
    return {
        coord.x + offset.x * distance,
        coord.y + offset.y * distance,
        coord.z + offset.z * distance
    };
}

Direction GetOppositeDirection(Direction direction) {
    switch (direction) {
        case DIRECTION_NORTH: return DIRECTION_SOUTH;
        case DIRECTION_SOUTH: return DIRECTION_NORTH;
        case DIRECTION_EAST: return DIRECTION_WEST;
        case DIRECTION_WEST: return DIRECTION_EAST;
        case DIRECTION_UP: return DIRECTION_DOWN;
        case DIRECTION_DOWN: return DIRECTION_UP;
        case DIRECTION_NORTHEAST: return DIRECTION_SOUTHWEST;
        case DIRECTION_NORTHWEST: return DIRECTION_SOUTHEAST;
        case DIRECTION_SOUTHEAST: return DIRECTION_NORTHWEST;
        case DIRECTION_SOUTHWEST: return DIRECTION_NORTHEAST;
        default: return direction;
    }
}

std::vector<Direction> GetCardinalDirections() {
    return {DIRECTION_NORTH, DIRECTION_SOUTH, DIRECTION_EAST, DIRECTION_WEST};
}

std::vector<Direction> GetDiagonalDirections() {
    return {DIRECTION_NORTHEAST, DIRECTION_NORTHWEST, DIRECTION_SOUTHEAST, DIRECTION_SOUTHWEST};
}

std::vector<Direction> GetAllHorizontalDirections() {
    std::vector<Direction> directions = GetCardinalDirections();
    std::vector<Direction> diagonals = GetDiagonalDirections();
    directions.insert(directions.end(), diagonals.begin(), diagonals.end());
    return directions;
}

// Area operations
std::vector<Coordinate> GetCoordinatesInRectangle(const Rectangle& rect) {
    std::vector<Coordinate> coordinates;
    
    if (!rect.IsValid()) {
        return coordinates;
    }
    
    for (int z = rect.min.z; z <= rect.max.z; ++z) {
        for (int y = rect.min.y; y <= rect.max.y; ++y) {
            for (int x = rect.min.x; x <= rect.max.x; ++x) {
                coordinates.push_back({x, y, z});
            }
        }
    }
    
    return coordinates;
}

std::vector<Coordinate> GetCoordinatesInCircle(const Coordinate& center, float radius) {
    std::vector<Coordinate> coordinates;
    
    int int_radius = static_cast<int>(std::ceil(radius));
    
    for (int dx = -int_radius; dx <= int_radius; ++dx) {
        for (int dy = -int_radius; dy <= int_radius; ++dy) {
            float distance = std::sqrt(static_cast<float>(dx*dx + dy*dy));
            if (distance <= radius) {
                coordinates.push_back({center.x + dx, center.y + dy, center.z});
            }
        }
    }
    
    return coordinates;
}

std::vector<Coordinate> GetCoordinatesOnLine(const Coordinate& start, const Coordinate& end) {
    std::vector<Coordinate> line;
    
    int dx = std::abs(end.x - start.x);
    int dy = std::abs(end.y - start.y);
    int dz = std::abs(end.z - start.z);
    
    int x_step = (start.x < end.x) ? 1 : -1;
    int y_step = (start.y < end.y) ? 1 : -1;
    int z_step = (start.z < end.z) ? 1 : -1;
    
    int max_delta = std::max({dx, dy, dz});
    
    if (max_delta == 0) {
        line.push_back(start);
        return line;
    }
    
    // 3D Bresenham-like algorithm
    Coordinate current = start;
    line.push_back(current);
    
    int x_error = dx - max_delta;
    int y_error = dy - max_delta;
    int z_error = dz - max_delta;
    
    for (int i = 0; i < max_delta; ++i) {
        x_error += dx;
        if (x_error >= 0) {
            current.x += x_step;
            x_error -= max_delta;
        }
        
        y_error += dy;
        if (y_error >= 0) {
            current.y += y_step;
            y_error -= max_delta;
        }
        
        z_error += dz;
        if (z_error >= 0) {
            current.z += z_step;
            z_error -= max_delta;
        }
        
        line.push_back(current);
    }
    
    return line;
}

Rectangle GetBoundingRectangle(const std::vector<Coordinate>& coordinates) {
    if (coordinates.empty()) {
        return {{0, 0, 0}, {-1, -1, -1}}; // Invalid rectangle
    }
    
    Coordinate min_coord = coordinates[0];
    Coordinate max_coord = coordinates[0];
    
    for (const Coordinate& coord : coordinates) {
        min_coord.x = std::min(min_coord.x, coord.x);
        min_coord.y = std::min(min_coord.y, coord.y);
        min_coord.z = std::min(min_coord.z, coord.z);
        
        max_coord.x = std::max(max_coord.x, coord.x);
        max_coord.y = std::max(max_coord.y, coord.y);
        max_coord.z = std::max(max_coord.z, coord.z);
    }
    
    return {min_coord, max_coord};
}

// Sorting and filtering
void SortCoordinatesByDistance(std::vector<Coordinate>& coordinates, const Coordinate& reference) {
    std::sort(coordinates.begin(), coordinates.end(),
        [&reference](const Coordinate& a, const Coordinate& b) {
            return CalculateDistance3D(reference, a) < CalculateDistance3D(reference, b);
        });
}

std::vector<Coordinate> FilterValidCoordinates(const std::vector<Coordinate>& coordinates) {
    std::vector<Coordinate> valid;
    
    for (const Coordinate& coord : coordinates) {
        if (IsValidMapCoordinate(coord)) {
            valid.push_back(coord);
        }
    }
    
    return valid;
}

std::vector<Coordinate> FilterCoordinatesInBounds(const std::vector<Coordinate>& coordinates, 
                                                 const Rectangle& bounds) {
    std::vector<Coordinate> filtered;
    
    for (const Coordinate& coord : coordinates) {
        if (bounds.Contains(coord)) {
            filtered.push_back(coord);
        }
    }
    
    return filtered;
}

} // namespace df
} // namespace utils
} // namespace dfai
