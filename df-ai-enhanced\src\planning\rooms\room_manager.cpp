#include "room_manager.h"
#include "../../debug/logging.h"

#include <algorithm>
#include <sstream>

namespace dfai {
namespace planning {

// Room class implementation
Room::Room(int32_t id, RoomType type, const std::string& name)
    : id_(id)
    , type_(type)
    , name_(name)
    , status_(RoomStatus::PLANNED)
    , min_{0, 0, 0}
    , max_{0, 0, 0}
    , buildings_()
    , accessors_()
    , needs_update_(false)
{
}

Room::~Room() {
    buildings_.clear();
    accessors_.clear();
}

void Room::SetStatus(RoomStatus status) {
    if (status_ != status) {
        RoomStatus old_status = status_;
        status_ = status;
        OnStatusChanged(old_status, status);
        MarkForUpdate();
    }
}

df::coord Room::GetCenter() const {
    return df::coord{
        (min_.x + max_.x) / 2,
        (min_.y + max_.y) / 2,
        (min_.z + max_.z) / 2
    };
}

int32_t Room::GetArea() const {
    return (max_.x - min_.x + 1) * (max_.y - min_.y + 1);
}

bool Room::ContainsPosition(df::coord position) const {
    return position.x >= min_.x && position.x <= max_.x &&
           position.y >= min_.y && position.y <= max_.y &&
           position.z >= min_.z && position.z <= max_.z;
}

bool Room::IntersectsWith(const Room& other) const {
    return !(max_.x < other.min_.x || min_.x > other.max_.x ||
             max_.y < other.min_.y || min_.y > other.max_.y ||
             max_.z < other.min_.z || min_.z > other.max_.z);
}

int32_t Room::GetDistanceTo(df::coord position) const {
    df::coord center = GetCenter();
    int32_t dx = abs(center.x - position.x);
    int32_t dy = abs(center.y - position.y);
    int32_t dz = abs(center.z - position.z);
    return dx + dy + dz; // Manhattan distance
}

void Room::AddBuilding(df::building* building) {
    if (building) {
        buildings_.push_back(building);
        MarkForUpdate();
    }
}

void Room::RemoveBuilding(df::building* building) {
    auto it = std::find(buildings_.begin(), buildings_.end(), building);
    if (it != buildings_.end()) {
        buildings_.erase(it);
        MarkForUpdate();
    }
}

std::vector<df::building*> Room::GetBuildings() const {
    return buildings_;
}

void Room::AddAccessor(std::shared_ptr<Room> room) {
    if (room) {
        accessors_.push_back(room);
        MarkForUpdate();
    }
}

void Room::RemoveAccessor(std::shared_ptr<Room> room) {
    accessors_.erase(
        std::remove_if(accessors_.begin(), accessors_.end(),
            [room](const std::weak_ptr<Room>& weak_room) {
                return weak_room.expired() || weak_room.lock() == room;
            }),
        accessors_.end());
    MarkForUpdate();
}

std::vector<std::shared_ptr<Room>> Room::GetAccessors() const {
    std::vector<std::shared_ptr<Room>> result;
    for (const auto& weak_room : accessors_) {
        if (auto room = weak_room.lock()) {
            result.push_back(room);
        }
    }
    return result;
}

void Room::Update(color_ostream& out) {
    if (!needs_update_) {
        return;
    }
    
    // Base room update logic
    needs_update_ = false;
}

void Room::Plan(color_ostream& out) {
    // Planning logic for the room
    SetStatus(RoomStatus::PLANNED);
}

void Room::Dig(color_ostream& out) {
    // Digging logic for the room
    SetStatus(RoomStatus::DIGGING);
}

void Room::Construct(color_ostream& out) {
    // Construction logic for the room
    SetStatus(RoomStatus::CONSTRUCTING);
}

void Room::Furnish(color_ostream& out) {
    // Furnishing logic for the room
}

std::string Room::GetStatusReport() const {
    std::ostringstream report;
    report << "Room " << id_ << " (" << name_ << ")\n";
    report << "  Type: " << RoomTypeToString(type_) << "\n";
    report << "  Status: " << RoomStatusToString(status_) << "\n";
    report << "  Bounds: (" << min_.x << "," << min_.y << "," << min_.z << ") to ";
    report << "(" << max_.x << "," << max_.y << "," << max_.z << ")\n";
    report << "  Area: " << GetArea() << "\n";
    report << "  Buildings: " << buildings_.size() << "\n";
    report << "  Accessors: " << GetAccessors().size() << "\n";
    return report.str();
}

std::string Room::GetDetailedInfo() const {
    return GetStatusReport();
}

void Room::OnStatusChanged(RoomStatus old_status, RoomStatus new_status) {
    // Override in derived classes for status-specific behavior
}

// WorkshopRoom implementation
WorkshopRoom::WorkshopRoom(int32_t id, const std::string& name)
    : Room(id, RoomType::WORKSHOP, name)
    , workshop_type_("generic")
{
}

WorkshopRoom::~WorkshopRoom() = default;

void WorkshopRoom::SetWorkshopType(const std::string& workshop_type) {
    workshop_type_ = workshop_type;
    MarkForUpdate();
}

void WorkshopRoom::Update(color_ostream& out) {
    Room::Update(out);
    // Workshop-specific update logic
}

std::string WorkshopRoom::GetDetailedInfo() const {
    std::ostringstream info;
    info << GetStatusReport();
    info << "  Workshop Type: " << workshop_type_ << "\n";
    return info.str();
}

// StockpileRoom implementation
StockpileRoom::StockpileRoom(int32_t id, const std::string& name)
    : Room(id, RoomType::STOCKPILE, name)
    , stockpile_type_("generic")
{
}

StockpileRoom::~StockpileRoom() = default;

void StockpileRoom::SetStockpileType(const std::string& stockpile_type) {
    stockpile_type_ = stockpile_type;
    MarkForUpdate();
}

void StockpileRoom::Update(color_ostream& out) {
    Room::Update(out);
    // Stockpile-specific update logic
}

std::string StockpileRoom::GetDetailedInfo() const {
    std::ostringstream info;
    info << GetStatusReport();
    info << "  Stockpile Type: " << stockpile_type_ << "\n";
    return info.str();
}

// RoomManager implementation
RoomManager::RoomManager()
    : logger_(debug::Logger::GetInstance())
    , initialized_(false)
    , rooms_()
    , rooms_by_type_()
    , next_room_id_(1)
{
    logger_.Info("RoomManager created");
}

RoomManager::~RoomManager() {
    Shutdown();
    logger_.Info("RoomManager destroyed");
}

bool RoomManager::Initialize(color_ostream& out) {
    if (initialized_) {
        logger_.Warning("RoomManager already initialized");
        return true;
    }
    
    logger_.Info("Initializing RoomManager...");
    
    try {
        // Initialize room type indices
        for (int i = 0; i <= static_cast<int>(RoomType::UNKNOWN); ++i) {
            rooms_by_type_[static_cast<RoomType>(i)] = std::vector<std::shared_ptr<Room>>();
        }
        
        initialized_ = true;
        logger_.Info("RoomManager initialization complete");
        return true;
        
    } catch (const std::exception& e) {
        logger_.Error("Exception during RoomManager initialization: {}", e.what());
        return false;
    }
}

void RoomManager::Shutdown() {
    if (!initialized_) {
        return;
    }
    
    logger_.Info("Shutting down RoomManager...");
    
    rooms_.clear();
    rooms_by_type_.clear();
    
    initialized_ = false;
    logger_.Info("RoomManager shutdown complete");
}

void RoomManager::Update(color_ostream& out) {
    if (!initialized_) {
        logger_.Warning("RoomManager::Update called before initialization");
        return;
    }
    
    try {
        // Update all rooms
        for (auto& [id, room] : rooms_) {
            if (room) {
                room->Update(out);
            }
        }
        
    } catch (const std::exception& e) {
        logger_.Error("Exception in RoomManager::Update: {}", e.what());
    }
}

std::shared_ptr<Room> RoomManager::CreateRoom(RoomType type, const std::string& name) {
    int32_t id = GenerateRoomId();
    auto room = std::make_shared<Room>(id, type, name);
    RegisterRoom(room);
    
    logger_.Info("Created room: {} ({})", name, id);
    return room;
}

std::shared_ptr<WorkshopRoom> RoomManager::CreateWorkshop(const std::string& name, const std::string& workshop_type) {
    int32_t id = GenerateRoomId();
    auto room = std::make_shared<WorkshopRoom>(id, name);
    room->SetWorkshopType(workshop_type);
    RegisterRoom(room);
    
    logger_.Info("Created workshop: {} ({}) - {}", name, id, workshop_type);
    return room;
}

std::shared_ptr<StockpileRoom> RoomManager::CreateStockpile(const std::string& name, const std::string& stockpile_type) {
    int32_t id = GenerateRoomId();
    auto room = std::make_shared<StockpileRoom>(id, name);
    room->SetStockpileType(stockpile_type);
    RegisterRoom(room);
    
    logger_.Info("Created stockpile: {} ({}) - {}", name, id, stockpile_type);
    return room;
}

void RoomManager::DestroyRoom(int32_t room_id) {
    auto it = rooms_.find(room_id);
    if (it != rooms_.end()) {
        UnregisterRoom(it->second);
        rooms_.erase(it);
        logger_.Info("Destroyed room: {}", room_id);
    }
}

void RoomManager::DestroyRoom(std::shared_ptr<Room> room) {
    if (room) {
        DestroyRoom(room->GetId());
    }
}

std::shared_ptr<Room> RoomManager::GetRoom(int32_t room_id) const {
    auto it = rooms_.find(room_id);
    return (it != rooms_.end()) ? it->second : nullptr;
}

std::vector<std::shared_ptr<Room>> RoomManager::GetRooms() const {
    std::vector<std::shared_ptr<Room>> result;
    result.reserve(rooms_.size());
    
    for (const auto& [id, room] : rooms_) {
        result.push_back(room);
    }
    
    return result;
}

std::vector<std::shared_ptr<Room>> RoomManager::GetRoomsByType(RoomType type) const {
    auto it = rooms_by_type_.find(type);
    return (it != rooms_by_type_.end()) ? it->second : std::vector<std::shared_ptr<Room>>();
}

std::vector<std::shared_ptr<Room>> RoomManager::GetRoomsByStatus(RoomStatus status) const {
    std::vector<std::shared_ptr<Room>> result;
    
    for (const auto& [id, room] : rooms_) {
        if (room && room->GetStatus() == status) {
            result.push_back(room);
        }
    }
    
    return result;
}

std::shared_ptr<Room> RoomManager::FindRoomAt(df::coord position) const {
    for (const auto& [id, room] : rooms_) {
        if (room && room->ContainsPosition(position)) {
            return room;
        }
    }
    return nullptr;
}

std::vector<std::shared_ptr<Room>> RoomManager::FindRoomsNear(df::coord position, int32_t radius) const {
    std::vector<std::shared_ptr<Room>> result;
    
    for (const auto& [id, room] : rooms_) {
        if (room && room->GetDistanceTo(position) <= radius) {
            result.push_back(room);
        }
    }
    
    return result;
}

std::string RoomManager::GetStatusReport() const {
    std::ostringstream report;
    
    report << "=== Room Manager Status ===\n";
    report << "Total rooms: " << rooms_.size() << "\n";
    
    // Count by type
    for (const auto& [type, rooms] : rooms_by_type_) {
        if (!rooms.empty()) {
            report << RoomTypeToString(type) << ": " << rooms.size() << "\n";
        }
    }
    
    // Count by status
    std::map<RoomStatus, int> status_counts;
    for (const auto& [id, room] : rooms_) {
        if (room) {
            status_counts[room->GetStatus()]++;
        }
    }
    
    report << "\nBy status:\n";
    for (const auto& [status, count] : status_counts) {
        report << RoomStatusToString(status) << ": " << count << "\n";
    }
    
    return report.str();
}

// Private helper methods
int32_t RoomManager::GenerateRoomId() {
    return next_room_id_++;
}

void RoomManager::RegisterRoom(std::shared_ptr<Room> room) {
    if (!room) return;
    
    rooms_[room->GetId()] = room;
    rooms_by_type_[room->GetType()].push_back(room);
}

void RoomManager::UnregisterRoom(std::shared_ptr<Room> room) {
    if (!room) return;
    
    auto& type_rooms = rooms_by_type_[room->GetType()];
    type_rooms.erase(
        std::remove(type_rooms.begin(), type_rooms.end(), room),
        type_rooms.end());
}

// Utility functions
std::string RoomTypeToString(RoomType type) {
    switch (type) {
        case RoomType::CORRIDOR: return "Corridor";
        case RoomType::BARRACKS: return "Barracks";
        case RoomType::BEDROOM: return "Bedroom";
        case RoomType::CEMETERY: return "Cemetery";
        case RoomType::CISTERN: return "Cistern";
        case RoomType::DINING_HALL: return "Dining Hall";
        case RoomType::FARM_PLOT: return "Farm Plot";
        case RoomType::FURNACE: return "Furnace";
        case RoomType::GARBAGE_DUMP: return "Garbage Dump";
        case RoomType::INFIRMARY: return "Infirmary";
        case RoomType::JAIL: return "Jail";
        case RoomType::LOCATION: return "Location";
        case RoomType::NOBLE_ROOM: return "Noble Room";
        case RoomType::OUTPOST: return "Outpost";
        case RoomType::PASTURE: return "Pasture";
        case RoomType::PIT_CAGE: return "Pit/Cage";
        case RoomType::STOCKPILE: return "Stockpile";
        case RoomType::TAVERN: return "Tavern";
        case RoomType::TEMPLE: return "Temple";
        case RoomType::LIBRARY: return "Library";
        case RoomType::WORKSHOP: return "Workshop";
        case RoomType::WELL: return "Well";
        default: return "Unknown";
    }
}

std::string RoomStatusToString(RoomStatus status) {
    switch (status) {
        case RoomStatus::PLANNED: return "Planned";
        case RoomStatus::DIGGING: return "Digging";
        case RoomStatus::DUG: return "Dug";
        case RoomStatus::CONSTRUCTING: return "Constructing";
        case RoomStatus::FINISHED: return "Finished";
        case RoomStatus::ABANDONED: return "Abandoned";
        default: return "Unknown";
    }
}

RoomType StringToRoomType(const std::string& type_str) {
    if (type_str == "corridor") return RoomType::CORRIDOR;
    if (type_str == "barracks") return RoomType::BARRACKS;
    if (type_str == "bedroom") return RoomType::BEDROOM;
    if (type_str == "cemetery") return RoomType::CEMETERY;
    if (type_str == "cistern") return RoomType::CISTERN;
    if (type_str == "dining_hall") return RoomType::DINING_HALL;
    if (type_str == "farm_plot") return RoomType::FARM_PLOT;
    if (type_str == "furnace") return RoomType::FURNACE;
    if (type_str == "garbage_dump") return RoomType::GARBAGE_DUMP;
    if (type_str == "infirmary") return RoomType::INFIRMARY;
    if (type_str == "jail") return RoomType::JAIL;
    if (type_str == "location") return RoomType::LOCATION;
    if (type_str == "noble_room") return RoomType::NOBLE_ROOM;
    if (type_str == "outpost") return RoomType::OUTPOST;
    if (type_str == "pasture") return RoomType::PASTURE;
    if (type_str == "pit_cage") return RoomType::PIT_CAGE;
    if (type_str == "stockpile") return RoomType::STOCKPILE;
    if (type_str == "tavern") return RoomType::TAVERN;
    if (type_str == "temple") return RoomType::TEMPLE;
    if (type_str == "library") return RoomType::LIBRARY;
    if (type_str == "workshop") return RoomType::WORKSHOP;
    if (type_str == "well") return RoomType::WELL;
    return RoomType::UNKNOWN;
}

} // namespace planning
} // namespace dfai
