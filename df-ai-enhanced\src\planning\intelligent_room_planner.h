#pragma once

#include "../utils/common/common.h"
#include "../debug/logging.h"
#include "../planning/plan_types.h"

#include <vector>
#include <map>
#include <set>
#include <queue>
#include <memory>

namespace dfai {
namespace planning {

/**
 * @brief Room template system for intelligent planning
 */
struct RoomTemplate {
    std::string name;
    room_type::type type;
    df::coord min_size;
    df::coord max_size;
    df::coord preferred_size;
    
    // Placement preferences
    int32_t preferred_z_level;
    bool needs_surface_access;
    bool needs_water_access;
    bool needs_trade_depot_access;
    bool avoid_high_traffic;
    
    // Furniture requirements
    std::vector<furniture_type::type> required_furniture;
    std::vector<furniture_type::type> optional_furniture;
    
    // Room relationships
    std::vector<room_type::type> should_be_near;
    std::vector<room_type::type> should_avoid;
    int32_t max_distance_from_entrance;
    
    // Special requirements
    std::map<std::string, std::string> custom_requirements;
    
    std::string description() const;
    bool is_suitable_location(df::coord pos, const class IntelligentRoomPlanner& planner) const;
};

/**
 * @brief Room efficiency analysis
 */
struct RoomEfficiency {
    room* target_room;
    double efficiency_score;
    std::vector<std::string> efficiency_factors;
    std::vector<std::string> improvement_suggestions;
    
    bool needs_improvement() const { return efficiency_score < 0.7; }
    bool is_highly_efficient() const { return efficiency_score > 0.9; }
};

/**
 * @brief Fort layout optimization
 */
struct LayoutOptimization {
    std::string optimization_type;
    df::coord area_min;
    df::coord area_max;
    std::vector<room_type::type> affected_rooms;
    std::string description;
    double expected_improvement;
    ResourcePriority priority;
};

/**
 * @brief Intelligent room planning and fort layout optimization
 * 
 * Provides advanced room planning capabilities including template-based
 * room creation, efficiency analysis, and layout optimization.
 */
class IntelligentRoomPlanner {
public:
    IntelligentRoomPlanner();
    ~IntelligentRoomPlanner();
    
    // Lifecycle
    bool Initialize(color_ostream& out);
    void Shutdown();
    void Update(color_ostream& out);
    
    // Room templates
    void LoadDefaultTemplates();
    void AddTemplate(const RoomTemplate& room_template);
    RoomTemplate GetTemplate(const std::string& name) const;
    std::vector<RoomTemplate> GetTemplatesForType(room_type::type type) const;
    
    // Intelligent room placement
    std::vector<df::coord> FindOptimalLocations(const RoomTemplate& room_template, int32_t max_results = 5) const;
    df::coord FindBestLocation(const RoomTemplate& room_template) const;
    double CalculateLocationScore(const RoomTemplate& room_template, df::coord location) const;
    
    // Room creation with templates
    room* CreateRoomFromTemplate(const std::string& template_name, df::coord location, color_ostream& out);
    room* CreateRoomFromTemplate(const RoomTemplate& room_template, df::coord location, color_ostream& out);
    
    // Efficiency analysis
    RoomEfficiency AnalyzeRoomEfficiency(room* target_room) const;
    std::vector<RoomEfficiency> AnalyzeFortEfficiency() const;
    void OptimizeRoomEfficiency(room* target_room, color_ostream& out);
    
    // Layout optimization
    std::vector<LayoutOptimization> AnalyzeFortLayout() const;
    void ApplyLayoutOptimization(const LayoutOptimization& optimization, color_ostream& out);
    void OptimizeEntireFort(color_ostream& out);
    
    // Advanced room features
    void PlanRoomExpansion(room* target_room, color_ostream& out);
    void PlanRoomRelocation(room* target_room, color_ostream& out);
    void PlanRoomUpgrade(room* target_room, color_ostream& out);
    
    // Traffic analysis
    struct TrafficAnalysis {
        std::map<df::coord, int32_t> traffic_density;
        std::vector<df::coord> bottlenecks;
        std::vector<df::coord> suggested_corridors;
        double overall_efficiency;
    };
    
    TrafficAnalysis AnalyzeTrafficFlow() const;
    void OptimizeTrafficFlow(color_ostream& out);
    
    // Multi-level planning
    void PlanVerticalLayout(color_ostream& out);
    void OptimizeZLevelUsage(color_ostream& out);
    std::vector<int32_t> GetOptimalZLevelsForRoomType(room_type::type type) const;
    
    // Defensive planning
    void PlanDefensiveLayout(color_ostream& out);
    void AddDefensiveFeatures(color_ostream& out);
    std::vector<df::coord> GetDefensiveChokepoints() const;
    
    // Utility queries
    std::vector<room*> GetRoomsOfType(room_type::type type) const;
    std::vector<room*> GetRoomsNear(df::coord location, int32_t max_distance) const;
    room* GetClosestRoom(df::coord location, room_type::type type) const;
    int32_t GetDistanceBetweenRooms(room* room1, room* room2) const;
    
    // Fort statistics
    struct FortStatistics {
        int32_t total_rooms;
        std::map<room_type::type, int32_t> room_type_counts;
        double average_room_efficiency;
        int32_t total_dug_tiles;
        int32_t total_constructed_tiles;
        double space_utilization_efficiency;
        
        std::string summary() const;
    };
    
    FortStatistics GetFortStatistics() const;
    
    // Reporting
    void GenerateLayoutReport(color_ostream& out, bool detailed = false) const;
    void GenerateEfficiencyReport(color_ostream& out) const;
    void GenerateRoomReport(color_ostream& out, room* target_room) const;
    
    // Configuration
    struct PlannerConfig {
        bool auto_optimization_enabled = true;
        bool traffic_optimization_enabled = true;
        bool defensive_planning_enabled = false;
        
        double min_efficiency_threshold = 0.6;
        int32_t max_optimization_iterations = 10;
        int32_t room_planning_lookahead = 20;
        
        // Pathfinding settings
        int32_t pathfinding_max_distance = 100;
        bool consider_traffic_in_scoring = true;
        bool prefer_existing_corridors = true;
        
        std::string to_string() const;
    };
    
    void SetConfig(const PlannerConfig& config);
    const PlannerConfig& GetConfig() const;
    
    // Template management
    void SaveTemplateToFile(const RoomTemplate& room_template, const std::string& filename) const;
    RoomTemplate LoadTemplateFromFile(const std::string& filename) const;
    void ExportAllTemplates(const std::string& directory) const;
    void ImportTemplatesFromDirectory(const std::string& directory);
    
private:
    debug::Logger& logger_;
    bool initialized_;
    
    // Templates
    std::map<std::string, RoomTemplate> room_templates_;
    std::map<room_type::type, std::vector<std::string>> templates_by_type_;
    
    // Analysis data
    mutable std::map<df::coord, double> location_scores_cache_;
    mutable std::map<room*, RoomEfficiency> efficiency_cache_;
    mutable TrafficAnalysis cached_traffic_analysis_;
    
    // Update tracking
    std::chrono::steady_clock::time_point last_analysis_update_;
    std::chrono::steady_clock::time_point last_optimization_time_;
    
    // Configuration
    PlannerConfig config_;
    
    // Helper methods
    void InitializeDefaultTemplates();
    double CalculateProximityScore(df::coord location, const std::vector<room_type::type>& desired_nearby) const;
    double CalculateAccessibilityScore(df::coord location) const;
    double CalculateTrafficScore(df::coord location) const;
    double CalculateResourceScore(df::coord location, const RoomTemplate& room_template) const;
    
    // Pathfinding and analysis
    std::vector<df::coord> FindPath(df::coord start, df::coord end) const;
    int32_t CalculatePathDistance(df::coord start, df::coord end) const;
    bool IsLocationSuitable(df::coord location, const RoomTemplate& room_template) const;
    bool IsAreaClear(df::coord min_pos, df::coord max_pos) const;
    
    // Optimization helpers
    std::vector<room*> GetRoomsNeedingOptimization() const;
    LayoutOptimization FindBestLayoutOptimization() const;
    void ImplementLayoutChanges(const LayoutOptimization& optimization, color_ostream& out);
    
    // Cache management
    void InvalidateCache();
    void UpdateAnalysisCache() const;
    bool IsCacheValid() const;
    
    // Template helpers
    RoomTemplate CreateBasicTemplate(room_type::type type, const std::string& name) const;
    void ValidateTemplate(const RoomTemplate& room_template) const;
    
    // Scoring functions
    double CalculateOverallLocationScore(df::coord location, const RoomTemplate& room_template) const;
    double CalculateRoomEfficiencyScore(room* target_room) const;
    double CalculateLayoutOptimizationScore(const LayoutOptimization& optimization) const;
    
public:
    // Public helpers for template suitability checking
    bool HasWaterAccess(df::coord location) const;
    bool HasSurfaceAccess(df::coord location) const;
    bool IsHighTrafficArea(df::coord location) const;
    room* GetNearestRoomOfType(df::coord location, room_type::type type) const;
    int32_t GetDistanceToEntrance(df::coord location) const;
    std::vector<room*> GetAllRooms() const;
};

} // namespace planning
} // namespace dfai
