{"$schema": "https://ben.lubar.me/df-ai-schemas/room-template.json", "f": [{"type": "door", "x": 0, "y": -1, "z": 0}, {"type": "door", "x": 2, "y": -1, "z": 0}], "r": [{"type": "corridor", "min": [-1, 1, 0], "max": [1, 7, 0], "layout": [0, 1], "exits": [["generic01_bedroom", -1, 0, 0], ["generic01_bedroom", 3, 0, 0], ["generic01_bedroom", -1, 2, 0], ["generic01_bedroom", 3, 2, 0], ["generic01_bedroom", -1, 4, 0], ["generic01_bedroom", 3, 4, 0], ["generic01_bedroom", -1, 6, 0], ["generic01_bedroom", 3, 6, 0], ["generic01_bedrooms", 1, 7, 0], ["generic01_corridor", 1, 7, 0]], "remove_if_unused": true}]}