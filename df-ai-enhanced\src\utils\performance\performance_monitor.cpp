#include "performance_monitor.h"
#include <algorithm>
#include <sstream>
#include <iomanip>

#ifdef _WIN32
#include <windows.h>
#include <psapi.h>
#else
#include <sys/resource.h>
#include <unistd.h>
#include <fstream>
#endif

using namespace dfai::utils;

PerformanceMonitor::PerformanceMonitor()
  : logger_(debug::Logger::GetInstance())
  , initialized_(false)
  , enabled_(true)
  , max_history_size_(1000)
  , last_system_update_(std::chrono::steady_clock::now())
{
}

PerformanceMonitor::~PerformanceMonitor() {
    Shutdown();
}

bool PerformanceMonitor::Initialize() {
    if (initialized_) return true;
    
    try {
        // Initialize system stats
        UpdateSystemStats();
        
        initialized_ = true;
        logger_.Info("PerformanceMonitor initialized");
        return true;
        
    } catch (const std::exception& e) {
        logger_.Error("Failed to initialize PerformanceMonitor: {}", e.what());
        return false;
    }
}

void PerformanceMonitor::Shutdown() {
    if (!initialized_) return;
    
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    // Clear all data
    performance_stats_.clear();
    active_timers_.clear();
    counters_.clear();
    gauges_.clear();
    value_history_.clear();
    memory_usage_.clear();
    watchdog_entries_.clear();
    
    initialized_ = false;
    logger_.Info("PerformanceMonitor shutdown");
}

PerformanceMonitor::ProfileScope::ProfileScope(const std::string& name, PerformanceMonitor& monitor)
  : name_(name)
  , monitor_(monitor)
  , start_time_(std::chrono::high_resolution_clock::now())
{
}

PerformanceMonitor::ProfileScope::~ProfileScope() {
    if (monitor_.IsEnabled()) {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time_);
        double elapsed_ms = duration.count() / 1000.0;
        
        monitor_.UpdatePerformanceStats(name_, elapsed_ms);
    }
}

void PerformanceMonitor::StartTimer(const std::string& name) {
    if (!enabled_) return;
    
    std::lock_guard<std::mutex> lock(stats_mutex_);
    active_timers_[name] = std::chrono::high_resolution_clock::now();
}

void PerformanceMonitor::EndTimer(const std::string& name) {
    if (!enabled_) return;
    
    auto end_time = std::chrono::high_resolution_clock::now();
    
    std::lock_guard<std::mutex> lock(stats_mutex_);
    auto it = active_timers_.find(name);
    if (it != active_timers_.end()) {
        double elapsed_ms = GetElapsedMilliseconds(it->second);
        UpdatePerformanceStats(name, elapsed_ms);
        active_timers_.erase(it);
    }
}

void PerformanceMonitor::IncrementCounter(const std::string& name, int32_t value) {
    if (!enabled_) return;
    
    std::lock_guard<std::mutex> lock(stats_mutex_);
    counters_[name] += value;
}

void PerformanceMonitor::SetGauge(const std::string& name, double value) {
    if (!enabled_) return;
    
    std::lock_guard<std::mutex> lock(stats_mutex_);
    gauges_[name] = value;
}

void PerformanceMonitor::RecordValue(const std::string& name, double value) {
    if (!enabled_) return;
    
    std::lock_guard<std::mutex> lock(stats_mutex_);
    auto& history = value_history_[name];
    history.push_back(value);
    TrimHistory(history);
}

void PerformanceMonitor::RecordMemoryUsage(const std::string& component, size_t bytes) {
    if (!enabled_) return;
    
    std::lock_guard<std::mutex> lock(stats_mutex_);
    memory_usage_[component] = bytes;
}

size_t PerformanceMonitor::GetMemoryUsage(const std::string& component) const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    auto it = memory_usage_.find(component);
    return (it != memory_usage_.end()) ? it->second : 0;
}

size_t PerformanceMonitor::GetTotalMemoryUsage() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    size_t total = 0;
    for (const auto& pair : memory_usage_) {
        total += pair.second;
    }
    return total;
}

std::vector<PerformanceMonitor::PerformanceStats> PerformanceMonitor::GetStats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    std::vector<PerformanceStats> stats;
    stats.reserve(performance_stats_.size());
    
    for (const auto& pair : performance_stats_) {
        stats.push_back(pair.second);
    }
    
    // Sort by total time descending
    std::sort(stats.begin(), stats.end(), 
              [](const PerformanceStats& a, const PerformanceStats& b) {
                  return a.total_time_ms > b.total_time_ms;
              });
    
    return stats;
}

PerformanceMonitor::PerformanceStats PerformanceMonitor::GetStats(const std::string& name) const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    auto it = performance_stats_.find(name);
    return (it != performance_stats_.end()) ? it->second : PerformanceStats{name};
}

void PerformanceMonitor::GenerateReport(color_ostream& out, bool detailed) const {
    out << "=== DF-AI Performance Report ===\n";
    
    auto stats = GetStats();
    if (stats.empty()) {
        out << "No performance data available.\n";
        return;
    }
    
    out << std::fixed << std::setprecision(3);
    out << std::setw(30) << "Operation" 
        << std::setw(10) << "Calls"
        << std::setw(12) << "Total (ms)"
        << std::setw(12) << "Avg (ms)"
        << std::setw(12) << "Min (ms)"
        << std::setw(12) << "Max (ms)"
        << std::setw(12) << "Last (ms)" << "\n";
    
    out << std::string(100, '-') << "\n";
    
    for (const auto& stat : stats) {
        out << std::setw(30) << stat.name
            << std::setw(10) << stat.call_count
            << std::setw(12) << stat.total_time_ms
            << std::setw(12) << stat.average_time_ms
            << std::setw(12) << stat.min_time_ms
            << std::setw(12) << stat.max_time_ms
            << std::setw(12) << stat.last_time_ms << "\n";
    }
    
    // System stats
    out << "\n=== System Statistics ===\n";
    auto sys_stats = GetSystemStats();
    out << "CPU Usage: " << sys_stats.cpu_percent << "%\n";
    out << "Memory Usage: " << sys_stats.memory_mb << " MB\n";
    out << "Virtual Memory: " << sys_stats.virtual_memory_mb << " MB\n";
    out << "Thread Count: " << sys_stats.thread_count << "\n";
    
    // Memory usage by component
    if (detailed) {
        out << "\n=== Memory Usage by Component ===\n";
        std::lock_guard<std::mutex> lock(stats_mutex_);
        for (const auto& pair : memory_usage_) {
            out << pair.first << ": " << (pair.second / 1024) << " KB\n";
        }
        
        // Counters
        out << "\n=== Counters ===\n";
        for (const auto& pair : counters_) {
            out << pair.first << ": " << pair.second << "\n";
        }
        
        // Gauges
        out << "\n=== Gauges ===\n";
        for (const auto& pair : gauges_) {
            out << pair.first << ": " << pair.second << "\n";
        }
    }
}

void PerformanceMonitor::GenerateHTMLReport(std::ostream& out) const {
    out << "<!DOCTYPE html><html><head><title>DF-AI Performance Report</title>";
    out << "<style>table{border-collapse:collapse;width:100%;}th,td{border:1px solid #ddd;padding:8px;text-align:right;}th{background-color:#f2f2f2;}</style>";
    out << "</head><body>";
    
    out << "<h1>DF-AI Performance Report</h1>";
    
    auto stats = GetStats();
    if (!stats.empty()) {
        out << "<h2>Performance Statistics</h2>";
        out << "<table><tr><th>Operation</th><th>Calls</th><th>Total (ms)</th><th>Avg (ms)</th><th>Min (ms)</th><th>Max (ms)</th><th>Last (ms)</th></tr>";
        
        for (const auto& stat : stats) {
            out << "<tr><td>" << stat.name << "</td>"
                << "<td>" << stat.call_count << "</td>"
                << "<td>" << std::fixed << std::setprecision(3) << stat.total_time_ms << "</td>"
                << "<td>" << stat.average_time_ms << "</td>"
                << "<td>" << stat.min_time_ms << "</td>"
                << "<td>" << stat.max_time_ms << "</td>"
                << "<td>" << stat.last_time_ms << "</td></tr>";
        }
        out << "</table>";
    }
    
    out << "</body></html>";
}

std::string PerformanceMonitor::GetQuickSummary() const {
    std::ostringstream summary;
    auto sys_stats = GetSystemStats();
    
    summary << "CPU: " << std::fixed << std::setprecision(1) << sys_stats.cpu_percent << "% | ";
    summary << "Memory: " << sys_stats.memory_mb << " MB | ";
    summary << "Threads: " << sys_stats.thread_count;
    
    std::lock_guard<std::mutex> lock(stats_mutex_);
    if (!performance_stats_.empty()) {
        summary << " | Operations: " << performance_stats_.size();
    }
    
    return summary.str();
}

void PerformanceMonitor::StartWatchdog(const std::string& operation, double timeout_seconds) {
    if (!enabled_) return;
    
    std::lock_guard<std::mutex> lock(stats_mutex_);
    watchdog_entries_[operation] = {std::chrono::high_resolution_clock::now(), timeout_seconds};
}

void PerformanceMonitor::StopWatchdog(const std::string& operation) {
    if (!enabled_) return;
    
    std::lock_guard<std::mutex> lock(stats_mutex_);
    watchdog_entries_.erase(operation);
}

std::vector<std::string> PerformanceMonitor::GetTimedOutOperations() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    std::vector<std::string> timed_out;
    
    auto now = std::chrono::high_resolution_clock::now();
    for (const auto& pair : watchdog_entries_) {
        double elapsed = GetElapsedMilliseconds(pair.second.start_time) / 1000.0;
        if (elapsed > pair.second.timeout_seconds) {
            timed_out.push_back(pair.first);
        }
    }
    
    return timed_out;
}

PerformanceMonitor::SystemStats PerformanceMonitor::GetSystemStats() const {
    return system_stats_;
}

void PerformanceMonitor::UpdateSystemStats() {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_system_update_).count();
    
    // Update every 5 seconds to avoid overhead
    if (elapsed < 5) return;
    
#ifdef _WIN32
    UpdateWindowsSystemStats();
#else
    UpdateLinuxSystemStats();
#endif
    
    last_system_update_ = now;
}

double PerformanceMonitor::GetElapsedMilliseconds(std::chrono::high_resolution_clock::time_point start) const {
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    return duration.count() / 1000.0;
}

void PerformanceMonitor::UpdatePerformanceStats(const std::string& name, double elapsed_ms) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    auto& stats = performance_stats_[name];
    stats.name = name;
    stats.call_count++;
    stats.total_time_ms += elapsed_ms;
    stats.average_time_ms = stats.total_time_ms / stats.call_count;
    stats.min_time_ms = std::min(stats.min_time_ms, elapsed_ms);
    stats.max_time_ms = std::max(stats.max_time_ms, elapsed_ms);
    stats.last_time_ms = elapsed_ms;
}

void PerformanceMonitor::TrimHistory(std::vector<double>& history) {
    if (history.size() > max_history_size_) {
        history.erase(history.begin(), history.begin() + (history.size() - max_history_size_));
    }
}

#ifdef _WIN32
void PerformanceMonitor::UpdateWindowsSystemStats() {
    PROCESS_MEMORY_COUNTERS_EX pmc = {};
    if (GetProcessMemoryInfo(GetCurrentProcess(), (PROCESS_MEMORY_COUNTERS*)&pmc, sizeof(pmc))) {
        system_stats_.memory_mb = pmc.WorkingSetSize / (1024 * 1024);
        system_stats_.virtual_memory_mb = pmc.PrivateUsage / (1024 * 1024);
    }
    
    // Thread count
    DWORD process_id = GetCurrentProcessId();
    HANDLE h_snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPTHREAD, 0);
    if (h_snapshot != INVALID_HANDLE_VALUE) {
        THREADENTRY32 te32 = {};
        te32.dwSize = sizeof(te32);
        
        system_stats_.thread_count = 0;
        if (Thread32First(h_snapshot, &te32)) {
            do {
                if (te32.th32OwnerProcessID == process_id) {
                    system_stats_.thread_count++;
                }
            } while (Thread32Next(h_snapshot, &te32));
        }
        CloseHandle(h_snapshot);
    }
}
#else
void PerformanceMonitor::UpdateLinuxSystemStats() {
    // Memory usage from /proc/self/status
    std::ifstream status_file("/proc/self/status");
    std::string line;
    while (std::getline(status_file, line)) {
        if (line.substr(0, 6) == "VmRSS:") {
            size_t kb = 0;
            std::istringstream iss(line.substr(6));
            iss >> kb;
            system_stats_.memory_mb = kb / 1024;
        } else if (line.substr(0, 7) == "VmSize:") {
            size_t kb = 0;
            std::istringstream iss(line.substr(7));
            iss >> kb;
            system_stats_.virtual_memory_mb = kb / 1024;
        } else if (line.substr(0, 8) == "Threads:") {
            std::istringstream iss(line.substr(8));
            iss >> system_stats_.thread_count;
        }
    }
}
#endif
