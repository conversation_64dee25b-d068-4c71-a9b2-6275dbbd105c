#pragma once

#include "../utils/common/common.h"
#include "../utils/common/types.h"
#include "../debug/logging.h"
#include "population_types.h"

#include <memory>
#include <vector>
#include <map>
#include <set>
#include <list>
#include <string>

// Forward declarations
class color_ostream;
namespace df {
    struct unit;
    struct squad;
    struct building;
    struct building_civzonest;
    struct abstract_building;
    struct entity_position_assignment;
    struct viewscreen_tradegoodsst;
    enum occupation_type : int16_t;
    enum job_type : int16_t;
}

namespace dfai {
namespace population {

/**
 * Modern population management system
 * Handles citizens, military, pets, trading, nobles, and occupations
 */
class PopulationManager {
public:
    PopulationManager();
    ~PopulationManager();
    
    // Core lifecycle
    bool Initialize(color_ostream& out);
    void Shutdown();
    void Update(color_ostream& out);
    
    // Status reporting
    std::string GetStatusReport() const;
    void GenerateReport(std::ostream& out, bool html = false) const;
    
    // Citizen management
    void AddCitizen(color_ostream& out, int32_t unit_id);
    void RemoveCitizen(color_ostream& out, int32_t unit_id);
    bool IsCitizen(int32_t unit_id) const;
    const std::set<int32_t>& GetCitizens() const { return citizens_; }
    
    // Military management
    void UpdateMilitary(color_ostream& out);
    bool AssignToMilitary(int32_t unit_id, int32_t squad_id);
    bool RemoveFromMilitary(int32_t unit_id);
    bool IsMilitary(int32_t unit_id) const;
    bool HasMilitaryDuty(df::unit* unit) const;
    
    // Squad orders and combat
    bool OrderSquadAttackUnit(color_ostream& out, df::squad* squad, df::unit* target, const std::string& reason);
    bool OrderRandomSquadAttackUnit(color_ostream& out, df::unit* target, const std::string& reason);
    bool OrderAllSquadsAttackUnit(color_ostream& out, df::unit* target, const std::string& reason);
    bool CancelAttackOrder(color_ostream& out, df::unit* target, const std::string& reason);
    bool CancelSquadAttackOrder(color_ostream& out, df::squad* squad, df::unit* target, const std::string& reason);
    
    // Pet management
    void UpdatePets(color_ostream& out);
    void AssignUnitToZone(df::unit* unit, df::building_civzonest* zone);
    bool IsPet(int32_t unit_id) const;
    PetFlags GetPetFlags(int32_t unit_id) const;
    
    // Visitor and resident management
    void UpdateVisitors(color_ostream& out);
    bool IsVisitor(int32_t unit_id) const;
    bool IsResident(int32_t unit_id) const;
    
    // Noble management
    void UpdateNobles(color_ostream& out);
    void CheckNobleApartments(color_ostream& out);
    
    // Occupation management
    void AssignOccupation(color_ostream& out, df::building* building, 
                         df::abstract_building* location, df::occupation_type occupation);
    
    // Trading management
    bool SetupTrading(color_ostream& out, bool should_trade, bool allow_any_dwarf = false);
    bool PerformTrade(color_ostream& out);
    void UpdateTrading(color_ostream& out);
    
    // Job and labor management
    void UpdateJobs(color_ostream& out);
    void UpdateWorkers(color_ostream& out);
    
    // Death and caging management
    void UpdateDeaths(color_ostream& out);
    void UpdateCaged(color_ostream& out);
    void StartDeathWatch(color_ostream& out);
    void StopDeathWatch();
    
    // Crime and justice
    void UpdateCrimes(color_ostream& out);
    
    // Location management
    void UpdateLocations(color_ostream& out);
    
    // Utility methods
    static int32_t CalculateUnitTotalExperience(const df::unit* unit);
    static int32_t DaysSince(int32_t year, int32_t tick);

private:
    // Core state
    debug::Logger& logger_;
    bool initialized_;
    size_t update_counter_;
    
    // Population tracking
    std::set<int32_t> citizens_;
    std::map<int32_t, int32_t> military_assignments_; // unit_id -> squad_id
    std::map<int32_t, PetFlags> pets_;
    std::set<int32_t> pet_check_queue_;
    std::set<int32_t> visitors_;
    std::set<int32_t> residents_;
    
    // Military settings
    int32_t military_min_size_;
    int32_t military_max_size_;
    
    // Squad orders
    std::list<SquadOrderChange> pending_squad_orders_;
    
    // Worker management
    std::set<int32_t> medics_;
    std::vector<int32_t> workers_;
    std::set<df::job_type> problematic_jobs_;
    
    // Death tracking
    size_t deaths_seen_;
    std::unique_ptr<OnupdateCallback> death_watch_callback_;
    
    // Crime tracking
    int32_t last_crime_check_year_;
    int32_t last_crime_check_tick_;
    
    // Trading state
    bool currently_trading_;
    int32_t trade_start_x_, trade_start_y_, trade_start_z_;
    
    // Event callbacks
    std::unique_ptr<OnupdateCallback> update_callback_;
    
    // Internal update methods
    void UpdateCitizenList(color_ostream& out);
    void CheckForNewCitizens(color_ostream& out);
    void CheckForLostCitizens(color_ostream& out);
    void UpdatePetFlags(int32_t unit_id, df::unit* unit);
    void ProcessSquadOrders(color_ostream& out);
    void UpdateMilitarySquads(color_ostream& out);
    void CheckMilitaryNeeds(color_ostream& out);
    void UpdateNobleAssignments(color_ostream& out);
    void CheckNobleRequirements(color_ostream& out);
    void ProcessJobAssignments(color_ostream& out);
    void CheckWorkerEfficiency(color_ostream& out);
    void MonitorUnitHealth(color_ostream& out);
    void CheckPrisonersAndCages(color_ostream& out);
    void ProcessCrimeReports(color_ostream& out);
    void UpdateLocationAssignments(color_ostream& out);
    
    // Helper methods
    bool IsValidCitizen(df::unit* unit) const;
    bool IsValidMilitary(df::unit* unit) const;
    bool IsValidPet(df::unit* unit) const;
    bool ShouldAssignToMilitary(df::unit* unit) const;
    df::squad* FindBestSquadForUnit(df::unit* unit) const;
    df::squad* GetRandomActiveSquad() const;
    PetFlags AnalyzePetCapabilities(df::unit* unit) const;
    bool IsUnitSuitableForTrading(df::unit* unit) const;
    std::string GetUnitDescription(df::unit* unit) const;
    
    // Event handlers
    void OnUnitDeath(color_ostream& out, int32_t unit_id);
    void OnUnitBirth(color_ostream& out, int32_t unit_id);
    void OnMigrantArrival(color_ostream& out, int32_t unit_id);
    void OnUnitLeaving(color_ostream& out, int32_t unit_id);
};

} // namespace population
} // namespace dfai
    
    // Trade state
    bool trade_in_progress_;
    
    // Squad management
    std::list<SquadOrderChange> pending_squad_orders_;
    
public:
    PopulationManager(core::AIController& ai, utils::Logger& logger);
    ~PopulationManager();
    
    // Lifecycle management
    command_result startup(color_ostream& out);
    command_result shutdown(color_ostream& out);
    command_result register_updates(color_ostream& out);
    command_result unregister_updates(color_ostream& out);
    
    // Main update loop
    void update(color_ostream& out);
    
    // Population management
    void add_citizen(color_ostream& out, int32_t unit_id);
    void remove_citizen(color_ostream& out, int32_t unit_id);
    void update_citizen_list(color_ostream& out);
    
    // Military management
    void update_military(color_ostream& out);
    bool unit_has_military_duty(df::unit* u);
    int32_t calculate_unit_experience(const df::unit* u);
    void draft_citizens(color_ostream& out);
    void manage_squads(color_ostream& out);
    
    // Military actions
    bool order_squad_attack(color_ostream& out, df::squad* squad, df::unit* target, const std::string& reason);
    bool order_all_squads_attack(color_ostream& out, df::unit* target, const std::string& reason);
    bool order_random_squad_attack(color_ostream& out, df::unit* target, const std::string& reason);
    bool cancel_attack_orders(color_ostream& out, df::unit* target, const std::string& reason);
    
    // Nobles management
    void update_nobles(color_ostream& out);
    void check_noble_quarters(color_ostream& out);
    void assign_noble_positions(color_ostream& out);
    
    // Pet management
    void update_pets(color_ostream& out);
    void assign_unit_to_zone(df::unit* u, df::building_civzonest* zone);
    bool check_pasture_capacity(color_ostream& out);
    
    // Justice system
    void update_justice(color_ostream& out);
    void update_crimes(color_ostream& out);
    void update_caged_units(color_ostream& out);
    
    // Death management
    void update_deaths(color_ostream& out);
    void death_watch(color_ostream& out);
    
    // Job management
    void update_jobs(color_ostream& out);
    void check_job_assignments(color_ostream& out);
    
    // Location management (taverns, temples, etc.)
    void update_locations(color_ostream& out);
    void manage_occupations(color_ostream& out);
    void assign_occupation(color_ostream& out, df::building* building, 
                          df::abstract_building* location, df::occupation_type occupation);
    
    // Trading
    void update_trading(color_ostream& out);
    bool setup_trading(color_ostream& out, bool should_trade, bool allow_any_dwarf = false);
    bool perform_trade(color_ostream& out);
    
    // Population queries
    size_t citizen_count() const { return citizens_.size(); }
    size_t military_count() const { return military_assignments_.size(); }
    size_t pet_count() const { return pets_.size(); }
    size_t visitor_count() const { return visitors_.size(); }
    size_t resident_count() const { return residents_.size(); }
    
    // Population access
    const std::set<int32_t>& citizens() const { return citizens_; }
    const std::map<int32_t, int32_t>& military_assignments() const { return military_assignments_; }
    const std::map<int32_t, PetFlags>& pets() const { return pets_; }
    const std::set<int32_t>& visitors() const { return visitors_; }
    const std::set<int32_t>& residents() const { return residents_; }
    
    // Configuration
    void set_military_limits(int32_t min_percent, int32_t max_percent);
    int32_t military_min_percent() const { return military_min_percent_; }
    int32_t military_max_percent() const { return military_max_percent_; }
    
    // Status and reporting
    std::string status() const;
    void report(std::ostream& out, bool html = false) const;
    
    // Utility functions
    static int32_t days_since(int32_t year, int32_t tick);
    
private:
    // Helper methods
    void process_squad_order_changes(color_ostream& out);
    void check_unit_health(color_ostream& out, df::unit* u);
    void manage_healthcare(color_ostream& out);
    bool is_valid_military_candidate(df::unit* u);
    void update_work_assignments(color_ostream& out);
};

} // namespace population
} // namespace df_ai
