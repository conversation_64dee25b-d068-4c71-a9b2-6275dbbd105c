#pragma once

#include "common.h"

namespace df_ai {
namespace utils {

/**
 * Type definitions and aliases used throughout the DF-AI system
 */

// Basic types
using EntityId = int32_t;
using UnitId = int32_t;
using BuildingId = int32_t;
using ItemId = int32_t;
using JobId = int32_t;
using RoomId = size_t;
using TaskId = size_t;

// Coordinate types
using Coordinate = df::coord;
using CoordinateSet = std::set<df::coord>;
using CoordinateVector = std::vector<df::coord>;

// Time types
using GameTick = int32_t;
using RealTime = std::chrono::steady_clock::time_point;

// Callback types
using UpdateCallback = std::function<void(color_ostream&)>;
using StateChangeCallback = std::function<void(color_ostream&, int)>;
using TimerCallback = std::function<void()>;

// Resource types
using ResourceAmount = int32_t;
using ResourceMap = std::map<std::string, ResourceAmount>;

// Task and priority types
using Priority = int32_t;
using TaskQueue = std::list<TaskId>;
using PriorityQueue = std::multimap<Priority, TaskId>;

// String types
using StringSet = std::set<std::string>;
using StringVector = std::vector<std::string>;

// Collection aliases
template<typename T>
using UniquePtr = std::unique_ptr<T>;

template<typename T>
using SharedPtr = std::shared_ptr<T>;

template<typename T>
using WeakPtr = std::weak_ptr<T>;

template<typename Key, typename Value>
using Map = std::map<Key, Value>;

template<typename Key, typename Value>
using UnorderedMap = std::unordered_map<Key, Value>;

template<typename T>
using Vector = std::vector<T>;

template<typename T>
using List = std::list<T>;

template<typename T>
using Set = std::set<T>;

// Result types for error handling
template<typename T>
struct Result {
    bool success;
    T value;
    std::string error_message;
    
    Result() : success(false) {}
    Result(const T& val) : success(true), value(val) {}
    Result(const std::string& error) : success(false), error_message(error) {}
    
    bool is_success() const { return success; }
    bool is_error() const { return !success; }
    const T& get_value() const { return value; }
    const std::string& get_error() const { return error_message; }
};

// Specialized result type for operations that don't return a value
using VoidResult = Result<bool>;

// Command result type compatible with DFHack
enum class CommandResult {
    OK,
    FAILURE,
    WRONG_USAGE,
    NOT_IMPLEMENTED
};

} // namespace utils
} // namespace df_ai
